#!/bin/bash
trap "exit" INT
## 整体思路，此文件名不能乱修改，跟 wing平台约定为此名称，为触发构建docker镜像的入口，执行此文件命令，默认会执行 deploy/services 里面配置好的
## 要构建的项目，每个子项目里面需要有一个docker.sh 脚本来 触发构建，最终构建的命令，是在最外层的
## commons_docker.sh 里面进行 build  push 操作！

SERVICE_FILE=deploy/services
if [ ! -s "$SERVICE_FILE" ]; then
    ALL_MODULES=()
else
    ALL_MODULES=( $(cat $SERVICE_FILE) )
fi

function usage() {
    echo "Usage: $0 [docker] [all ${ALL_MODULES[@]}]"
}

MODULE=$1
if [ "$MODULE" == "docker" ]; then
    DOCKER="docker"
    MODULE=$2
fi

if [ "$MODULE" == "" ]; then
    MODULE="all"
fi
if [ "$MODULE" != "all" ] && ! [ -d $MODULE ]; then
    usage
    exit 1
fi

## check lock file to avoid concurrent running
LOCK_FILE=dist.lock
#if [ -f $LOCK_FILE ]; then
#    last_time=`date -r $LOCK_FILE "+%s"`
#    current_time=`date "+%s"`
#    elapsed=$(( $current_time - $last_time ))
#    if [ $elapsed -gt 600 ]; then
#        rm -rf $LOCK_FILE
#        echo "remove lock file $LOCK_FILE created $elapsed seconds ago"
#    else
#        echo "failed: another build is running (started $elapsed seconds ago), try again later"
#        exit 1
#    fi
#fi
touch $LOCK_FILE
function cleanup {
    rm -rf $LOCK_FILE
}
trap cleanup exit;

echo "
#######################################

clean and install all modules

#######################################
"
function mvncmd() {
    if [ -x "$(command -v mvnd)" ]; then
        echo "mvnd $@"
        mvnd $@
    else
        echo "mvn $@"
        mvn $@
    fi
}
export -f mvncmd

mvncmd  -Dmaven.test.skip=true -U clean package
ret=$?
if [ $ret != 0 ]; then
    echo ""
    echo "Maven install failed"
    echo ""
    exit 1
fi

if [ "$MODULE" == "all" ]; then
    MODULES=( "${ALL_MODULES[@]}" )
else
    MODULES=("$MODULE")
fi

for service in ${MODULES[@]}; do
    echo "
#######################################

packaging $service

#######################################
"
    cd $service
    ./docker.sh

    ret=$?
    if [ $ret != 0 ]; then
        echo ""
        echo "Package $service failed"
        echo ""
        exit 1
    fi
    cd ..
done


MIGRATIONS=( $(find . -name migrations -type d) )
for migration in ${MIGRATIONS[@]}; do
    if ! [ -f "$migration/docker.sh" ]; then
        continue
    fi
    migration_name=`grep "NAME=" $migration/docker.sh | awk -F= '{print $2}'`
    if [ "$migration_name" == "" ]; then
        continue
    fi
    echo "
#######################################

packaging migration $migration_name

#######################################
"
    cd $migration
    ./docker.sh
    ret=$?
    if [ $ret != 0 ]; then
        echo ""
        echo "Package migration $migration failed"
        echo ""
        exit 1
    fi
    cd ../..
done

