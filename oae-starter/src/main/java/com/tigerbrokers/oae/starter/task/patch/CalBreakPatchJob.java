package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.PreAllocationService;
import com.tigerbrokers.oae.starter.task.BaseJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */
@Slf4j
@ScheduledJob(name = "calBreak::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai",
        disabled = true, justRunOnStart = true)
public class CalBreakPatchJob extends BaseJob {

    @Autowired
    private PreAllocationService preAllocationService;

    @Override
    protected void doExecute() {
        try {
            List<Long> aggregationIds = getListParam("aggregationId", Long.class);
            log.info("Start calBreak patch job: aggregationId-{}", aggregationIds);
            if (CollectionUtils.isEmpty(aggregationIds)) {
                log.error("no aggregation id");
                return;
            }
            preAllocationService.patchBuildBreakAllocation(aggregationIds);
            log.info("Finished calBreak patch job.");
        } catch (Exception e) {
            log.error("CalBreakPatchJob {}", e.getMessage(), e);
        }
    }
}
