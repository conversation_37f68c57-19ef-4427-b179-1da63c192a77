package com.tigerbrokers.oae.starter.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.tigerbrokers.infra.schedule.api.JobContext;
import com.tigerbrokers.infra.schedule.api.simple.SimpleJob;
import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.TimeUtils;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Slf4j
public abstract class BaseJob implements SimpleJob {

    private static final String TRADE_DATE = "tradeDate";

    protected JobContext jobContext;

    protected JsonNode jobParams;

    @Override
    public void execute(JobContext jobContext) {
        this.jobContext = jobContext;
        String jobParameter = jobContext.getJobParameter();
        if (jobParams != null) {
            jobParams = null;
        }
        if (StringUtils.isNotEmpty(jobParameter)) {
            jobParameter = jobParameter.trim();
            if (jobParameter.startsWith("{")) {
                this.jobParams = JacksonUtil.parseJsonNode(jobParameter);
            }
        }
        doBefore();
        doExecute();
        doAfter();
    }

    protected void doBefore() {

    }

    protected abstract void doExecute();

    protected void doAfter() {

    }

    protected DateTime getTradeTime(CommonConsts.StockMarket stockMarket) {
        DateTime tradeDateTime = TimeUtils.getNow(stockMarket).withTimeAtStartOfDay();
        String tradeDateStr = getStringParam(TRADE_DATE);
        if (StringUtils.isNotEmpty(tradeDateStr)) {
            long tradeTime = TimeUtils.getCommonDate(tradeDateStr, stockMarket);
            tradeDateTime = TimeUtils.createDateTime(tradeTime, stockMarket);
        }
        return tradeDateTime;
    }

    protected DateTime getDateTimeParam(CommonConsts.StockMarket stockMarket, String name) {
        String tradeDateStr = getStringParam(name);
        if (StringUtils.isEmpty(tradeDateStr)) {
            return null;
        }
        long tradeTime = TimeUtils.getCommonDate(tradeDateStr, stockMarket);
        return TimeUtils.createDateTime(tradeTime, stockMarket);
    }

    protected String getStringParam(String name) {
        return getParam(jsonNode -> JacksonUtil.asString(jsonNode, name));
    }

    protected Double getDoubleParam(String name) {
        return getParam(jsonNode -> JacksonUtil.asDouble(jsonNode, name));
    }

    protected Integer getIntegerParam(String name) {
        return getParam(jsonNode -> JacksonUtil.asInteger(jsonNode, name));
    }

    protected Long getLongParam(String name) {
        return getParam(jsonNode -> JacksonUtil.asLong(jsonNode, name));
    }

    protected Boolean getBooleanParam(String name) {
        return getParam(jsonNode -> JacksonUtil.asBoolean(jsonNode, name));
    }

    protected <T> List<T> getListParam(String name, Class<T> cls) {
        return getParam(jsonNode -> JacksonUtil.asList(jsonNode, name, cls));
    }

    private <R> R getParam(Function<JsonNode, R> function) {
        if (jobParams == null) {
            return null;
        }
        return function.apply(jobParams);
    }
}
