package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.TradingCalendar;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Slf4j
public abstract class USBaseJob extends BaseJob {

    @Override
    protected void doExecute() {
        DateTime tradeDateTime = getTradeTime();
        if (!needHandle(tradeDateTime)) {
            log.info("{} is not US trading date", tradeDateTime);
            return;
        }
        handle(tradeDateTime);
    }

    protected DateTime getTradeTime() {
        return getTradeTime(CommonConsts.StockMarket.US);
    }

    protected boolean needHandle(DateTime tradeDateTime) {
        return TradingCalendar.isTradingDay(tradeDateTime, CommonConsts.StockMarket.US);
    }

    protected abstract void handle(DateTime tradeDateTime);
}
