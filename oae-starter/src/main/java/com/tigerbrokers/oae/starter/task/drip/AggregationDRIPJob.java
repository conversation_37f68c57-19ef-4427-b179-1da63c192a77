package com.tigerbrokers.oae.starter.task.drip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.BaseJob;
import com.tigerbrokers.oae.trade.service.AggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ScheduledJob(name = "aggregation::drip::job", cron = "0 50 9,10 * * ?", zone = "Asia/Shanghai")
public class AggregationDRIPJob extends BaseJob {

    @Autowired
    private AggregationService aggregationService;

    @Autowired
    private AlertService alertService;
    @Override
    protected void doExecute() {
        try {
            log.info("Start DRIP aggregation job.");
            aggregationService.processAggregation(BusinessSource.DRIP, null, SecType.FUND);
            log.info("Finished DRIP aggregation job.");
        } catch (Exception e) {
            log.error("AggregationDRIPJob {}", e.getMessage(), e);
            alertService.sendMsg("AggregationDRIPJob fail:" + e.getMessage());
        }
    }
}
