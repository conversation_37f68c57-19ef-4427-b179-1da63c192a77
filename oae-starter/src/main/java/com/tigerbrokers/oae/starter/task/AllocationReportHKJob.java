package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
@Slf4j
@ScheduledJob(name = "allocation::report::HK::job", cron = "0 30 16 * * ?", zone = "Asia/Shanghai", planRole = 2, disableCron = true)
public class AllocationReportHKJob extends HKBaseJob {

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        if (allocationReportService.canProcess(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK)) {
            try {
                log.info("Start HK allocation report job.");
                DateTime businessDate = getDateTimeParam(CommonConsts.StockMarket.HK, "businessDate");
                if (businessDate == null) {
                    businessDate = DateTime.now();
                }
                allocationReportService.processAllocationReport(CommonConsts.StockMarket.HK, BusinessSource.RSP, SecType.STK, businessDate);
                log.info("Finished HK allocation report job.");
            } catch (Exception e) {
                log.error("AllocationReportHKJob {}", e.getMessage(), e);
                alertService.sendMsg("AllocationReportHKJob fail:" + e.getMessage());
                throw new RuntimeException("AllocationReportHKJob fail:" + e.getMessage());
            }
        } else {
            String msg = "not finish preAllocation or has unusual trade allocation, AllocationReportHKJob can not sent allocation report.";
            log.error(msg);
            alertService.sendMsg(msg);
            throw new RuntimeException(msg);
        }
    }
}
