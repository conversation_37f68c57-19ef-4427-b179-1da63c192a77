package com.tigerbrokers.oae.starter.util;

import cn.hutool.core.util.ObjectUtil;
import com.tigerbrokers.oae.api.PlaceOrderRequest;
import com.tigerbrokers.oae.api.error.ErrorCode;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.starter.data.RequestValidationDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2023/2/9
 */
@Slf4j
public class RequestValidationUtil {

    public static RequestValidationDTO isValid(PlaceOrderRequest request) {
        if (BigDecimalUtil.isLessOrEqualThan(request.getAmount(), 0.0) &&  BigDecimalUtil.isLessOrEqualThan(request.getQuantity(), 0.0)) {
            return RequestValidationDTO.createError(ErrorCode.PLACE_ORDER_REQUEST_AMOUNT_ERROR);
        }
        if (!EnumUtils.isValidEnum(BusinessSource.class, request.getSource())) {
            return RequestValidationDTO.createError(ErrorCode.PLACE_ORDER_REQUEST_SOURCE_ERROR);
        }
        try {
            for (Field f : request.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                if (ObjectUtil.hasNull(f.get(request)) || ObjectUtil.hasEmpty(f.get(request))) {
                    return RequestValidationDTO.createError(ErrorCode.PLACE_ORDER_REQUEST_PARAMS_ERROR, f.getName());
                }
            }
        } catch (Exception e) {
            log.error("isValid(PlaceOrderRequest:{}) fail:{}", request, e.getMessage(), e);
        }

        return RequestValidationDTO.createValid();
    }
}
