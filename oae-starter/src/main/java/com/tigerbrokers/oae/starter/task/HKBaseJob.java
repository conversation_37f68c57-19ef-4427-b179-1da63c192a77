package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.TradingCalendar;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Slf4j
public abstract class HKBaseJob extends BaseJob {

    @Override
    protected void doExecute() {
        DateTime tradeDateTime = getTradeTime();
        if (!needHandle(tradeDateTime)) {
            log.info("{} is not HK trading date", tradeDateTime);
            return;
        }
        handle(tradeDateTime);
    }

    protected DateTime getTradeTime() {
        return getTradeTime(CommonConsts.StockMarket.HK);
    }

    protected boolean needHandle(DateTime tradeDateTime) {
        return TradingCalendar.isTradingDay(tradeDateTime, CommonConsts.StockMarket.HK);
    }

    protected abstract void handle(DateTime tradeDateTime);
}
