package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.allocation.service.AllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 调用分配请求
 * 未成交订单资产解锁，消息发布
 *
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@ScheduledJob(name = "allocation::HK::job", cron = "0 30 16 * * ?", zone = "Asia/Shanghai", planRole = 2, disableCron = true)
public class AllocationHKJob extends HKBaseJob {

    @Autowired
    private AllocationService allocationService;

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        if (Boolean.FALSE.equals(allocationReportService.canProcess(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK))) {
            String errMsg = "not finish preAllocation or has unusual trade allocation, AllocationHKJob can not process allocation.";
            log.error(errMsg);
            alertService.sendMsg(errMsg);
            return;
        }
        try {
            log.info("Start HK allocation job.");
            allocationService.processAllocation(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK);
            log.info("Finished HK allocation job.");
            log.info("Start HK unlock unfilled asset job.");
            lockedAssetService.unlockUnfilledAsset(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK);
            log.info("Finished HK unlock unfilled asset job.");
        } catch (Exception e) {
            log.error("AllocationHKJob {}", e.getMessage(), e);
            alertService.sendMsg("AllocationHKJob fail:" + e.getMessage());
        }
    }
}
