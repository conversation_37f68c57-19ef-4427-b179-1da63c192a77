package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.trade.service.AggregationService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 用户订单聚合&下单任务
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
@Slf4j
@ScheduledJob(name = "aggregation::HK::job", cron = "0 30 11 * * ?", zone = "Asia/Shanghai")
public class AggregationHKJob extends HKBaseJob {

    @Autowired
    private AggregationService aggregationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            log.info("Start HK aggregation job.");
            aggregationService.processAggregation(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK);
            log.info("Finished HK aggregation job.");
        } catch (Exception e) {
            log.error("AggregationHKJob {}", e.getMessage(), e);
            alertService.sendMsg("AggregationHKJob fail:" + e.getMessage());
        }
    }
}
