package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.starter.task.HKBaseJob;
import com.tigerbrokers.oae.trade.service.PublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/2
 */
@Slf4j
@ScheduledJob(name = "publish::user::order::msg::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai",
        disabled = true, justRunOnStart = true)
public class PublishUserOrderMsgPatchJob extends HKBaseJob {

    @Autowired
    private PublishService publishService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
//            {"userOrderId" : [69]}
            List<Long> userOrderIds = getListParam("userOrderId", Long.class);
            log.info("Start publish UserOrder msg patch job: userOrderIds-{}", userOrderIds);
            if (CollectionUtils.isEmpty(userOrderIds)) {
                log.error("no user order id");
                return;
            }
            publishService.publishUserOrder(userOrderIds);
            log.info("Finished publish UserOrder msg patch job.");
        } catch (Exception e) {
            log.error("PublishUserOrderMsgPatchJob {}", e.getMessage(), e);
        }
    }
}
