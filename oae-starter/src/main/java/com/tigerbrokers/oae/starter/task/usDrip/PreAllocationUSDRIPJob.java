package com.tigerbrokers.oae.starter.task.usDrip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.PreAllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.USBaseJob;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 轮询聚合订单
 * 计算聚合订单成交数据
 * 用户成交数据计算
 * 生成用户分配记录
 *
 * <AUTHOR>
 * @date 2023/1/10
 */
@Slf4j
@ScheduledJob(name = "pre::allocation::us::drip::job", cron = "0 5 19 * * ?", zone = "America/New_York", planRole = 1)
public class PreAllocationUSDRIPJob extends USBaseJob {

    @Autowired
    private PreAllocationService preAllocationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        boolean allSucc = false;
        try {
            log.info("Start US DRIP pre allocation job.");
            allSucc = preAllocationService.processPreAllocation(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK);
            log.info("Finished US DRIP pre allocation job.");
        } catch (Exception e) {
            log.error("PreAllocationUSDRIPJob {}", e.getMessage(), e);
        }
        if (Boolean.FALSE.equals(allSucc)) {
            alertService.sendMsg("PreAllocationUSDRIPJob failed.");
            throw new RuntimeException("PreAllocationUSDRIPJob failed.");
        }
    }
}
