package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.starter.task.HKBaseJob;
import com.tigerbrokers.oae.trade.service.ExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 将执行订单推入延时队列，继续处理改单检查
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Slf4j
@ScheduledJob(name = "execution::order::to::queue::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai",
        disabled = true, justRunOnStart = true)
public class PushExecutionOrderToQueuePatchJob extends HKBaseJob {

    @Autowired
    private ExecutionService executionService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            Long executionOrderId = getLongParam("executionOrderId");
            log.info("Start push execution order to queue patch job: executionOrderId-{}", executionOrderId);
            if (executionOrderId == null) {
                log.error("no execution order id");
                return;
            }
            executionService.pushExecutionOrderQueue(executionOrderId);
            log.info("Finished push execution order to queue patch job.");
        } catch (Exception e) {
            log.error("PushExecutionOrderToQueuePatchJob {}", e.getMessage(), e);
        }
    }
}
