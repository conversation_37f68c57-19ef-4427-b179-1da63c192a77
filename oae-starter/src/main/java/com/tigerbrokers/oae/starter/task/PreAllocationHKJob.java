package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.PreAllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 轮询聚合订单
 * 计算聚合订单成交数据
 * 用户成交数据计算
 * 生成用户分配记录
 *
 * <AUTHOR>
 * @date 2023/1/10
 */
@Slf4j
@ScheduledJob(name = "pre::allocation::HK::job", cron = "0 35 16 * * ?", zone = "Asia/Shanghai", planRole = 1)
public class PreAllocationHKJob extends HKBaseJob {

    @Autowired
    private PreAllocationService preAllocationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        boolean allSucc = false;
        try {
            log.info("Start HK pre allocation job.");
            allSucc = preAllocationService.processPreAllocation(BusinessSource.RSP, CommonConsts.StockMarket.HK, SecType.STK);
            log.info("Finished HK pre allocation job.");
        } catch (Exception e) {
            log.error("PreAllocationHKJob {}", e.getMessage(), e);
        }
        if (Boolean.FALSE.equals(allSucc)) {
            throw new RuntimeException("PreAllocationHKJob failed.");
        }
    }
}
