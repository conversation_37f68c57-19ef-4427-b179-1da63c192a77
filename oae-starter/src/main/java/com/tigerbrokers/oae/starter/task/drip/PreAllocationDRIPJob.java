package com.tigerbrokers.oae.starter.task.drip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.PreAllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.BaseJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 轮询聚合订单
 * 计算聚合订单成交数据
 * 用户成交数据计算
 * 生成用户分配记录
 *
 * <AUTHOR>
 * @date 2023/1/10
 */
@Slf4j
@ScheduledJob(name = "pre::allocation::drip::job", cron = "0 0 0/1 * * ?", zone = "Asia/Shanghai", planRole = 1)
public class PreAllocationDRIPJob extends BaseJob {

    @Autowired
    private PreAllocationService preAllocationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void doExecute() {
        boolean allSucc = false;
        try {
            log.info("Start DRIP pre allocation job.");
            allSucc = preAllocationService.processPreAllocation(BusinessSource.DRIP, null, SecType.FUND);
            log.info("Finished DRIP pre allocation job.");
        } catch (Exception e) {
            log.error("PreAllocationDRIPJob {}", e.getMessage(), e);
        }
        if (Boolean.FALSE.equals(allSucc)) {
            throw new RuntimeException("PreAllocationDRIPJob failed.");
        }
    }
}
