package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.brokerage.sdk.trade.dto.OmnibusOrderDTO;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.facade.OrderApiFacade;
import com.tigerbrokers.oae.starter.task.BaseJob;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.kafka.data.PrimeTradeKafka;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 修复用户订单交易消息补丁任务
 */
@Slf4j
@ScheduledJob(name = "fix::user::order::trade::msg::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai",
        disabled = true, justRunOnStart = true)
public class FixUserOrderTradeMsgPatchJob extends BaseJob {

    @Autowired
    private UserOrderRepository userOrderRepository;

    @Autowired
    private OrderApiFacade orderApiFacade;

    @Override
    protected void doExecute() {
        try {
            List<FixParam> fixParams = getListParam("fixParam", FixParam.class);
            if (CollectionUtils.isEmpty(fixParams)) {
                log.error("no user order id");
                return;
            }
            for (FixParam fixParam : fixParams) {
                fixUserOrderTradeMsg(fixParam);
            }
            log.info("Finished fix UserOrder msg patch job.");
        } catch (Exception e) {
            log.error("FixUserOrderTradeMsgPatchJob {}", e.getMessage(), e);
        }
    }

    @Data
    public static class FixParam {
        private Long id;
        private String tradeId;
    }

    private void fixUserOrderTradeMsg(FixParam fixParam) {
        // 修复用户订单交易消息
        log.info("Fix user order trade msg: fixParam-{}", fixParam);
        if (fixParam.getId() == null || fixParam.getTradeId() == null) {
            log.error("fixParam is null, fixParam:{}", fixParam);
            return;
        }
        UserOrder userOrder = userOrderRepository.getUserOrder(fixParam.getId());
        if (userOrder == null) {
            log.error("userOrder is null with id:{}", fixParam.getId());
            return;
        }
        OmnibusOrderDTO omnibusOrderDTO = orderApiFacade.queryOrderById(fixParam.getTradeId(), userOrder.getAccount(),
                BusinessSource.valueOf(userOrder.getSource()));
        if (omnibusOrderDTO == null) {
            log.error("omnibusOrderDTO is null with tradeId:{}", fixParam.getTradeId());
            return;
        }
        log.info("fixUserOrderTradeMsg omnibusOrderDTO:{}", JacksonUtil.writeValueAsString(omnibusOrderDTO));
        PrimeTradeKafka primeTradeKafka = convertToPrimeTradeKafka(omnibusOrderDTO);
        if (primeTradeKafka == null) {
            log.error("primeTradeKafka is null with tradeId:{}", fixParam.getTradeId());
            return;
        }
        userOrder.setTradeId(fixParam.getTradeId());
        userOrder.setTradeMsg(JacksonUtil.writeValueAsString(primeTradeKafka));
        userOrder.setStatus(UserOrderStatus.COMPLETED.name());
        userOrderRepository.update(userOrder);
    }

    private PrimeTradeKafka convertToPrimeTradeKafka(OmnibusOrderDTO omnibusOrderDTO) {
        PrimeTradeKafka primeTradeKafka = new PrimeTradeKafka();
        try {
            BeanUtils.copyProperties(omnibusOrderDTO, primeTradeKafka);
            primeTradeKafka.setAccountId(Long.parseLong(omnibusOrderDTO.getAccountId()));
            return primeTradeKafka;
        } catch (Exception e) {
            log.error("convertToPrimeTradeKafka error:{}", e.getMessage(), e);
            return null;
        }
    }

}
