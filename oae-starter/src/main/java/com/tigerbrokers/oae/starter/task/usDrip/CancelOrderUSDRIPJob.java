package com.tigerbrokers.oae.starter.task.usDrip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.USBaseJob;
import com.tigerbrokers.oae.trade.service.ExecutionService;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * T日16:00仍未成交的订单，系统自动撤单
 *
 */
@Slf4j
@ScheduledJob(name = "cancel::order::us::drip::job", cron = "0 0 16 * * ?", zone = "America/New_York", disabled = true)
public class CancelOrderUSDRIPJob extends USBaseJob {

    @Autowired
    private ExecutionService executionService;

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            log.info("Start US cancel trade job.");
            executionService.cancelOrder(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK);
            log.info("Finished US cancel trade job.");
        } catch (Exception e) {
            log.error("CancelTradeUSDRIPJob {}", e.getMessage(), e);
            alertService.sendMsg("CancelTradeUSDRIPJob fail:" + e.getMessage());
        }
    }
}
