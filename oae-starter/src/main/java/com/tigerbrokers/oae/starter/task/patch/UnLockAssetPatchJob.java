package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.starter.task.BaseJob;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: chentao
 * Created on 8/6/2023
 */
@Slf4j
@ScheduledJob(name = "unlock::asset::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai", disabled = true, justRunOnStart = true)
public class UnLockAssetPatchJob extends BaseJob {

    public static final String JOB_PARAM_ASSET_ID = "unlockIds";

    @Autowired
    private LockedAssetService lockedAssetService;

    @Override
    protected void doExecute() {
        try {
            List<Long> ids = this.getListParam(JOB_PARAM_ASSET_ID, Long.class);
            log.info("start UnLockAssetPatchJob ids:{}", ids);
            lockedAssetService.unlockByIdList(ids);
            log.info("end UnLockAssetPatchJob ids:{}", ids);
        } catch (Exception e) {
            log.error("UnLockAssetPatchJob params:{}, error:{} ", this.jobParams, e.getMessage(), e);
        }
    }
}
