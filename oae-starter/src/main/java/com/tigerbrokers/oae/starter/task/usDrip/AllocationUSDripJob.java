package com.tigerbrokers.oae.starter.task.usDrip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.allocation.service.AllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.USBaseJob;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 调用分配请求
 * 未成交订单资产解锁，消息发布
 *
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@ScheduledJob(name = "allocation::us::drip::job", cron = "0 0 19 * * ?", zone = "America/New_York", planRole = 2, disableCron = true)
public class AllocationUSDripJob extends USBaseJob {

    @Autowired
    private AllocationService allocationService;

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        if (Boolean.FALSE.equals(allocationReportService.canProcess(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK))) {
            String errMsg = "not finish preAllocation or has unusual trade allocation, AllocationUSDripJob can not process allocation.";
            log.error(errMsg);
            alertService.sendMsg(errMsg);
            return;
        }
        try {
            log.info("Start US allocation job.");
            allocationService.processAllocation(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK);
            log.info("Finished US allocation job.");
            log.info("Start US unlock unfilled asset job.");
            lockedAssetService.unlockUnfilledAsset(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK);
            log.info("Finished US unlock unfilled asset job.");
        } catch (Exception e) {
            log.error("AllocationUSDripJob {}", e.getMessage(), e);
            alertService.sendMsg("AllocationUSDripJob fail:" + e.getMessage());
        }
    }
}
