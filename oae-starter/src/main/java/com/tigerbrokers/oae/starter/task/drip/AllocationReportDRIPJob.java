package com.tigerbrokers.oae.starter.task.drip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.BaseJob;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ScheduledJob(name = "allocation::report::drip::job", cron = "0 59 23 * * ?", zone = "Asia/Shanghai")
public class AllocationReportDRIPJob extends BaseJob {

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void doExecute() {
        try {
            log.info("Start DRIP allocation report job.");
            DateTime businessDate = getDateTimeParam(CommonConsts.StockMarket.SI, "businessDate");
            if (businessDate == null) {
                businessDate = DateTime.now();
            }
            allocationReportService.processAllocationReport(CommonConsts.StockMarket.SI, BusinessSource.DRIP, SecType.FUND, businessDate);
            log.info("Finished DRIP allocation report job.");
        } catch (Exception e) {
            log.error("AllocationReportDRIPJob {}", e.getMessage(), e);
            alertService.sendMsg("AllocationReportDRIPJob fail:" + e.getMessage());
            throw new RuntimeException("AllocationReportDRIPJob fail:" + e.getMessage());
        }
    }
}
