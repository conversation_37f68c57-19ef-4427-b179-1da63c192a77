package com.tigerbrokers.oae.starter.task.drip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.allocation.service.AllocationService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.BaseJob;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ScheduledJob(name = "allocation::drip::job", cron = "0 0 0/1 * * ?", zone = "Asia/Shanghai", planRole = 2, disableCron = true)
public class AllocationDRIPJob extends BaseJob {

    @Autowired
    private AllocationService allocationService;

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void doExecute() {
        try {
            log.info("Start DRIP allocation job.");
            allocationService.processAllocation(BusinessSource.DRIP, null, SecType.FUND);
            log.info("Finished DRIP allocation job.");
            log.info("Start DRIP unlock unfilled asset job.");
            lockedAssetService.unlockUnfilledAsset(BusinessSource.DRIP, null, SecType.FUND);
            log.info("Finished DRIP unlock unfilled asset job.");
        } catch (Exception e) {
            log.error("AllocationDRIPJob {}", e.getMessage(), e);
            alertService.sendMsg("AllocationDRIPJob fail:" + e.getMessage());
        }
    }

}
