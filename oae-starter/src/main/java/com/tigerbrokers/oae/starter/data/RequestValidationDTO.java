package com.tigerbrokers.oae.starter.data;

import com.tigerbrokers.oae.api.error.ErrorCode;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9
 */
@Data
@Builder
public class RequestValidationDTO {

    private Boolean isValid;

    private ErrorCode errorCode;

    private Object[] params;

    public static RequestValidationDTO createValid() {
        return RequestValidationDTO.builder().isValid(true).build();
    }

    public static RequestValidationDTO createError(ErrorCode errorCode, Object... params) {
        return RequestValidationDTO.builder().isValid(false).errorCode(errorCode).params(params).build();
    }
}
