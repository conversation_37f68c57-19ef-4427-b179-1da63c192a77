package com.tigerbrokers.oae.starter.api;

import com.tigerbrokers.ams.microservice.management.aop.RpcAuth;
import com.tigerbrokers.oae.api.IOaeTradeApi;
import com.tigerbrokers.oae.api.PlaceOrderReply;
import com.tigerbrokers.oae.api.PlaceOrderRequest;
import com.tigerbrokers.oae.api.error.ErrorCode;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.starter.consts.RpcAuthScope;
import com.tigerbrokers.oae.starter.data.RequestValidationDTO;
import com.tigerbrokers.oae.starter.util.RequestValidationUtil;
import com.tigerbrokers.oae.trade.mapper.PlaceOrderReplyMapper;
import com.tigerbrokers.oae.trade.service.UserOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Slf4j
@RpcAuth
@DubboService(registry = "defaultRegistry", filter = "dubboMicroProvider")
public class OaeTradeApiService implements IOaeTradeApi {

    @Autowired
    private UserOrderService userOrderService;

    /**
     * 下单
     */
    @Override
    @RpcAuth(scope = RpcAuthScope.OAE_TRADE_WRITE, description = RpcAuthScope.OAE_TRADE_WRITE_DESCRIPTION)
    public PlaceOrderReply placeOrder(PlaceOrderRequest request) {
        try {
            if (BusinessSource.RSP.name().equalsIgnoreCase(request.getSource())) {
                request = request.toBuilder().setPortfolioId(-1).build();
                request = request.toBuilder().setDividendId(-1).build();
            }
            log.info("place order request: {}", request);
            RequestValidationDTO validationDTO = RequestValidationUtil.isValid(request);
            if (validationDTO.getIsValid()) {
                return userOrderService.placeOrder(request);
            }
            return PlaceOrderReplyMapper.createErrorReply(validationDTO.getErrorCode(), validationDTO.getParams());
        } catch (Exception e) {
            log.error("placeOrder {}", e.getMessage(), e);
        }

        return PlaceOrderReplyMapper.createErrorReply(ErrorCode.PLACE_ORDER_ERROR);
    }
}
