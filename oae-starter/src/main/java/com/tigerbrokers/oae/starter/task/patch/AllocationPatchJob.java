package com.tigerbrokers.oae.starter.task.patch;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationService;
import com.tigerbrokers.oae.starter.task.HKBaseJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/2
 */
@Slf4j
@ScheduledJob(name = "allocation::patch::job", cron = "0 0 12 ? * SUN", zone = "Asia/Shanghai",
        disabled = true, justRunOnStart = true)
public class AllocationPatchJob extends HKBaseJob {

    @Autowired
    private AllocationService allocationService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            List<Long> allocationIds = getListParam("allocationId", Long.class);
            log.info("Start allocation patch job: allocationIds-{}", allocationIds);
            if (CollectionUtils.isEmpty(allocationIds)) {
                log.error("no allocation id");
                return;
            }
            allocationService.processAllocationById(allocationIds);
            log.info("Finished allocation patch job.");
        } catch (Exception e) {
            log.error("AllocationPatchJob {}", e.getMessage(), e);
        }
    }
}
