package com.tigerbrokers.oae.starter.task.usDrip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.allocation.service.AllocationReportService;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.USBaseJob;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ScheduledJob(name = "allocation::report::us::drip::job", cron = "0 59 23 * * ?", zone = "America/New_York", disabled = true)
public class AllocationReportUSDRIPJob extends USBaseJob {

    @Autowired
    private AllocationReportService allocationReportService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        if (allocationReportService.canProcess(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK)) {
            try {
                log.info("Start US allocation report job.");
                DateTime businessDate = getDateTimeParam(CommonConsts.StockMarket.US, "businessDate");
                if (businessDate == null) {
                    businessDate = DateTime.now();
                }
                allocationReportService.processAllocationReport(CommonConsts.StockMarket.US, BusinessSource.DRIP, SecType.STK, businessDate);
                log.info("Finished US allocation report job.");
            } catch (Exception e) {
                log.error("AllocationReportUSDRIPJob {}", e.getMessage(), e);
                alertService.sendMsg("AllocationReportUSDRIPJob fail:" + e.getMessage());
                throw new RuntimeException("AllocationReportUSDRIPJob fail:" + e.getMessage());
            }
        } else {
            String msg = "not finish preAllocation or has unusual trade allocation, AllocationReportUSDRIPJob can not sent allocation report.";
            log.error(msg);
            alertService.sendMsg(msg);
            throw new RuntimeException(msg);
        }
    }
}
