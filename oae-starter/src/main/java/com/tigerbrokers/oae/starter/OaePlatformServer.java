package com.tigerbrokers.oae.starter;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.tigerbrokers.ams.microservice.management.auth.EnableInternalAuth;
import com.tigerbrokers.infra.schedule.annotation.EnableJobScheduling;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@ComponentScan(basePackages = {"com.tigerbrokers.oae", "com.tigerbrokers.brokerage.sdk", "com.tigerbrokers.ams.microservice.management.aop"})
@EnableInternalAuth
@EnableApolloConfig
@EnableJobScheduling
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableRabbit
@EnableDubbo
@Slf4j
public class OaePlatformServer implements CommandLineRunner {

    @Autowired
    private Environment env;

    public OaePlatformServer(Environment env) {
        this.env = env;
    }

    public static void main(String[] args) {
        SpringApplication server = new SpringApplication(OaePlatformServer.class);
        server.setWebApplicationType(WebApplicationType.NONE);
        server.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("====== LOG QAX property:");
        log.info("schedule.reg.namespace = " + env.getProperty("schedule.reg.namespace"));
        log.info("schedule.reg.serverLists = " + env.getProperty("schedule.reg.serverLists"));
        Thread.currentThread().join();

    }
}
