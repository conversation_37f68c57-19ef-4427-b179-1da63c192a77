package com.tigerbrokers.oae.starter.task.usDrip;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.starter.task.USBaseJob;
import com.tigerbrokers.oae.trade.service.AggregationService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ScheduledJob(name = "aggregation::us::drip::job", cron = "0 30 13 * * ?", zone = "America/New_York")
public class AggregationUSDRIPJob extends USBaseJob {

    @Autowired
    private AggregationService aggregationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            log.info("Start US DRIP aggregation job.");
            aggregationService.processAggregation(BusinessSource.DRIP, CommonConsts.StockMarket.US, SecType.STK);
            log.info("Finished US DRIP aggregation job.");
        } catch (Exception e) {
            log.error("AggregationUSDRIPJob {}", e.getMessage(), e);
            alertService.sendMsg("AggregationUSDRIPJob fail:" + e.getMessage());
        }
    }
}
