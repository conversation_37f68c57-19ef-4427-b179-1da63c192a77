package com.tigerbrokers.oae.starter.task;

import com.tigerbrokers.infra.schedule.annotation.ScheduledJob;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.trade.service.AggregationService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 碎股单任务
 *
 * @author: chentao
 * Created on 17/3/2023
 */
@Slf4j
@ScheduledJob(name = "aggregationOdd::HK::job", cron = "0 0 15 * * ?", zone = "Asia/Shanghai")
public class AggregationOddHKJob extends HKBaseJob {

    @Autowired
    private AggregationService aggregationService;

    @Autowired
    private AlertService alertService;

    @Override
    protected void handle(DateTime tradeDateTime) {
        try {
            log.info("Start HK ODD aggregation job.");
            aggregationService.processODDAggregation(BusinessSource.RSP, CommonConsts.StockMarket.HK);
            log.info("Finished HK ODD aggregation job.");
        } catch (Exception e) {
            log.error("AggregationOddHKJob {}", e.getMessage(), e);
            alertService.sendMsg("AggregationOddHKJob fail:" + e.getMessage());
        }
    }
}
