package com.tigerbrokers.oae.starter.config;

import com.tigerbrokers.ams.microservice.management.MicroserviceAuthConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: chentao
 * Created on 7/2/2023
 */
@Configuration
public class VerifyManagementConfig {

    @Bean
    public MicroserviceAuthConfig verifyConfig() {
        return new MicroserviceAuthConfig();
    }

}
