<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>zeus-waiter</id>
    <formats>
        <format>tar.gz</format>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <!-- app bin -->
        <fileSet>
            <directory>src/main/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>target/classes/temp-conf</directory>
            <outputDirectory>conf/</outputDirectory>
            <fileMode>0644</fileMode>
            <excludes>
                <exclude>*yml</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/starlink</directory>
            <!-- 压缩包包含目录starlink -->
            <outputDirectory>starlink</outputDirectory>
        </fileSet>
    </fileSets>

    <dependencySets>
        <dependencySet>
            <outputDirectory>lib</outputDirectory>
            <directoryMode>0755</directoryMode>
        </dependencySet>
    </dependencySets>

</assembly>
