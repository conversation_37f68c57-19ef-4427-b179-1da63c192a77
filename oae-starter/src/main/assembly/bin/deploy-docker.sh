#!/bin/bash

USAGE="USAGE: $0 {start|stop|restart}"
COMMAND=$1  # 操作命令

# wings env variable
ENV_FILE=/data0/config/env.list
if [ -f "$ENV_FILE" ]; then
    echo "env file: $ENV_FILE exist."
    cat $ENV_FILE
    source $ENV_FILE
    ENV=$RUN_ENV
else
    echo "env file: $ENV_FILE does not exist."
    ENV="test"
fi

echo "exec deploy-docker.sh, COMMAND: $COMMAND, ENV: $ENV"

source /etc/profile
SERVER_NAME=oae-starter

#directory
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
CONF_DIR=$DEPLOY_DIR/conf
LIB_DIR=$DEPLOY_DIR/lib
DATA_DIR=$DEPLOY_DIR/data
LOG_DIR=$DATA_DIR/logs
PIDDIR=$DATA_DIR/pids
mkdir -p $LOG_DIR
mkdir -p $PIDDIR
PIDFILE=$PIDDIR/$SERVER_NAME
GC_LOG=$LOG_DIR/gc.log
DUMP=$LOG_DIR/dump
DEBUG_PORT=40011

#CONFIG_PROFILE=${CONF_DIR}/profiles/${ENV}
#CONFIG_META=${CONF_DIR}/META-INF
#CONFIG_CLASSPATH=${CONFIG_PROFILE}:${CONFIG_META}

if [[ ! -d "$CONF_DIR/profiles/$ENV" ]];
then
  echo "$CONF_DIR/profiles/$ENV not existed" > 2
  exit 1
fi
echo "copy profile from [$CONF_DIR/profiles/$ENV/] to [$CONF_DIR/]"
cp -rf $CONF_DIR/profiles/$ENV/* $CONF_DIR/

#jvm config
PRODUCTION_ARGS=" -Xms2048m -Xmx2048m  -Xmn1024m -XX:MaxMetaspaceSize=256m -Xss256K -XX:+DisableExplicitGC -XX:+UseFastAccessorMethods -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$DUMP "
TEST_ARGS=" -Xms256m -Xmx256m  -Xmn128m -XX:MaxMetaspaceSize=256m -Xss256K -XX:+DisableExplicitGC -XX:+UseFastAccessorMethods -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$DUMP "

JAVA_GC_OPTS=" -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintHeapAtGC -XX:+PrintGCApplicationStoppedTime -Xloggc:$GC_LOG "

JAVA_CP=" -classpath $CONF_DIR:$LIB_DIR/* "

if [ $ENV = "online" ]; then
    JAVA_ARGS="$PRODUCTION_ARGS"
    CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.meta=https://config-qw.tigerfintech.com/ -Dschedule_env=prod"
elif [ $ENV = "staging" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.meta=https://config-qw.tigerfintech.com/fat-meta -Dschedule_env=prod"
elif [ $ENV = "qa" ]; then
    JAVA_ARGS="$TEST_ARGS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$DEBUG_PORT,server=y,suspend=n "
    CONF_ARGS="-Dapp.id=oae_platform -Denv=dev -Dapollo.cluster=qa -Dapollo.meta=https://config-dev.tigerfintech.com/dev-meta"
elif [ $ENV = "qax" ]; then
    JAVA_ARGS="$TEST_ARGS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$DEBUG_PORT,server=y,suspend=n "
    CONF_ARGS="-Dapp.id=oae_platform -Denv=dev -Dapollo.cluster=qax -Dapollo.meta=https://config-dev.tigerfintech.com/dev-meta -Dbrokerage-profile=qax"
else
    JAVA_ARGS="$TEST_ARGS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$DEBUG_PORT,server=y,suspend=n "
    CONF_ARGS="-Dapp.id=oae_platform -Denv=dev -Dapollo.meta=https://config-dev.tigerfintech.com/dev-meta"
fi

APM_OPTS=""
if [[ -d "${DEPLOY_DIR}/starlink" ]];then
  APM_OPTS=" -javaagent:$DEPLOY_DIR/starlink/opentelemetry-javaagent-2.6.0.jar -Dotel.javaagent.configuration-file=$DEPLOY_DIR/conf/profiles/$ENV/starlink.properties -Dotel.javaagent.extensions=$DEPLOY_DIR/starlink/ext"
fi

echo "APM_OPTS: $APM_OPTS, ENV: $ENV, JAVA_ARGS:$JAVA_ARGS"

JAVA_OPTS="$JAVA_ARGS $JAVA_GC_OPTS $JAVA_CP $JAVA_EXTRA_OPTS $CONF_ARGS $APM_OPTS"

case $1 in
start)
    PIDS=`ps  --no-heading -C java -f --width 2000 |grep "$DEPLOY_DIR" |awk '{print $2}'`
    if [ -n "$PIDS" ]; then
        echo "$SERVER_NAME server instance already started!"
        exit 1
    fi

    java $JAVA_OPTS com.tigerbrokers.oae.starter.OaePlatformServer

    exit 0
    ;;
stop)
    PIDS=`ps --no-heading -C java -f --width 2000 |grep "$DEPLOY_DIR" |awk '{print $2}'`
    if [[ -n "$PIDS" ]]; then
       # 重试3次kill进程，如果进程未正常退出，使用强制性退出kill -9
      killTimes=3
      while [[ ${killTimes} -ge 0 ]]; do
        kill -0  $PIDS >/dev/null 2>&1
        if [[ $? -gt 0 ]]; then
          echo -e "$SERVER_NAME server stopped, pid:${PIDS} "
          break
        else
            kill -15 ${PIDS}
            echo -e "start kill -15 $SERVER_NAME, pid:${PIDS} "
            sleep 3
        fi
        killTimes=`expr ${killTimes} - 1`
      done

      # 再次检查进程是否存在
      kill -0  $PIDS >/dev/null 2>&1
      if [[ $? -gt 0 ]]; then
          echo -e "$SERVER_NAME server stopped, pid:${PIDS} "
          break
      else
          # 进程依然存在，强制kill进程
          echo -e "force kill -9 $SERVER_NAME, pid:${PIDS} "
          kill -9 ${PIDS}
          sleep 3
      fi
    fi
    ;;
restart)
    shift
    sh "$BIN_DIR/`basename $0`" stop ${@}
    sleep 3s
    sh "$BIN_DIR/`basename $0`" start ${@}
    ;;
*)
    echo $USAGE
    ;;

esac