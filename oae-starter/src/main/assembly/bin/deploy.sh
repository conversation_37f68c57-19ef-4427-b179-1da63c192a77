#!/bin/bash

USAGE="USAGE: $0 {start|stop|restart} {local|test|staging|online|online-tbsg|online-tfnz|online-tbhk|online-tbau|online-tbms}"
if [ $# != 2 ]; then
    echo $USAGE
    exit 3
fi
source /etc/profile

#directory
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
LIB_DIR=$DEPLOY_DIR/lib
DATA_DIR=$DEPLOY_DIR/data
LOG_DIR=/data0/logs/oae-starter
PIDDIR=$DATA_DIR/pids
mkdir -p $LOG_DIR
mkdir -p $PIDDIR
PIDFILE=$PIDDIR/oae-starter
GC_LOG=$LOG_DIR/gc.log
DUMP=$LOG_DIR/dump
DEBUG_PORT=40011

CONF_DIR=$DEPLOY_DIR/conf
ENV=$2
if [[ ! -d "$CONF_DIR/profiles/$ENV" ]];
then
  echo "$CONF_DIR/profiles/$ENV not existed" > 2
  exit 1
fi
echo "copy profile from [$CONF_DIR/profiles/$ENV/] to [$CONF_DIR/]"
cp -rf $CONF_DIR/profiles/$ENV/* $CONF_DIR/

#jvm config
PRODUCTION_ARGS=" -Xms512m -Xmx1024m -XX:PermSize=128m -XX:MaxPermSize=128m -Xss256K -XX:+DisableExplicitGC -XX:+UseFastAccessorMethods -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=80 -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCApplicationStoppedTime -XX:HeapDumpPath=$DUMP "
TEST_ARGS=" -Xms512m -Xmx1024m -XX:PermSize=128m -XX:MaxPermSize=128m -Xss256K -XX:+DisableExplicitGC -XX:+UseFastAccessorMethods -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=80 -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCApplicationStoppedTime -XX:HeapDumpPath=$DUMP "

LIB_JARS=`ls $LIB_DIR|grep .jar|awk '{print "'$LIB_DIR'/"$0}'|tr "\n" ":"`
JAVA_CP=" -classpath $CONF_DIR:$LIB_JARS "

if [ $2 = "online" ]; then
    JAVA_ARGS="$PRODUCTION_ARGS"
    GC_LOG=/data0/logs/oae-starter-nz/gc.log
    CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online"
elif [ $2 = "online-tbhk" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-hk/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=tbhk -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online-hk"
elif [ $2 = "online-tbsg" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-sg/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=tbsg -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/
  -Dschedule_env=prod -Dbrokerage-profile=online-sg"
elif [ $2 = "online-tbau" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-tbau/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=tbau -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online-au"
elif [ $2 = "online-tfnz" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-tfnz/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=tfnz -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online-tfnz"
elif [ $2 = "online-tbms" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-tbms/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=tbms -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online-tbms"
elif [ $2 = "online-sg-jja" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-sg-jja/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.cluster=sg-jja -Dapollo.meta=https://config-qw.tigerfintech.com/,https://config-jja.tigerfintech.com/ -Dschedule_env=prod -Dbrokerage-profile=online-sg"
elif [ $2 = "staging" ]; then
  JAVA_ARGS="$PRODUCTION_ARGS"
  GC_LOG=/data0/logs/oae-starter-staging/gc.log
  CONF_ARGS="-Dapp.id=oae_platform -Denv=pro -Dapollo.meta=https://config-qw.tigerfintech.com/fat-meta,https://config-jja.tigerfintech.com/fat-meta -Dschedule_env=prod"
elif [ $2 = "test-sg" ]; then
      GC_LOG=/data0/logs/oae-starter-test-sg/gc.log
      JAVA_ARGS="$TEST_ARGS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$DEBUG_PORT,server=y,suspend=n "
      CONF_ARGS="-Dapp.id=oae_platform -Denv=dev -Dapollo.meta=https://config-dev.tigerfintech.com/dev-meta/"
else
    GC_LOG=/data0/logs/oae-starter/gc.log
    JAVA_ARGS="$TEST_ARGS -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=$DEBUG_PORT,server=y,suspend=n "
    CONF_ARGS="-Dapp.id=oae_platform -Denv=dev -Dapollo.meta=https://config-dev.tigerfintech.com/dev-meta/ -Djava.io.tmpdir=/data0/tmp"
fi

JAVA_GC_OPTS=" -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintHeapAtGC -Xloggc:$GC_LOG "

APM_OPTS=""
if [[ -d "${DEPLOY_DIR}/starlink" ]];then
  APM_OPTS=" -javaagent:$DEPLOY_DIR/starlink/opentelemetry-javaagent-2.6.0.jar -Dotel.javaagent.configuration-file=$DEPLOY_DIR/conf/profiles/$2/starlink.properties -Dotel.javaagent.extensions=$DEPLOY_DIR/starlink/ext"
fi

JAVA_OPTS="$JAVA_ARGS $JAVA_GC_OPTS $JAVA_CP $JAVA_EXTRA_OPTS $CONF_ARGS $APM_OPTS"


case $1 in
start)
    PIDS=`ps  --no-heading -C java -f --width 2000 |grep "$DEPLOY_DIR" |awk '{print $2}'`
    if [ -n "$PIDS" ]; then
        echo "The oae platform server instance already started!"
        exit -1
    fi

    nohup java $JAVA_OPTS com.tigerbrokers.oae.starter.OaePlatformServer 1>/dev/null 2>nohup.out &

    if [ $? -eq 0 ]; then
        if echo $! > $PIDFILE
        then
            sleep 2
            echo "oae platform started config $2 pid $!"
        else
            echo "Failed to write pid $! to pid file!!!"
            exit 1
        fi
    else
        echo "Failed to start oae platform server , error no $?"
        exit -1
    fi
    exit 0
    ;;
stop)
    PIDS=`ps --no-heading -C java -f --width 2000 |grep "$DEPLOY_DIR" |awk '{print $2}'`
    if [ -n "$PIDS" ]; then
       kill -9 "$PIDS"
       echo "oae platform server stopped"
    fi
    ;;
restart)
    shift
    sh "$BIN_DIR/`basename $0`" stop ${@}
    sleep 3s
    sh "$BIN_DIR/`basename $0`" start ${@}
    ;;
*)
    echo $USAGE
    ;;

esac