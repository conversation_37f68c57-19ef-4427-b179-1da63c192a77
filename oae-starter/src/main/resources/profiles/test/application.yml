spring:
  web:
    enable: false
  application:
    name: oae-platform

nacos:
  service-address: ************:8848,************:8848,************:8848

zk:
  quote:
    address: ************:2181,************:2181,************:2181

dubbo:
  scan:
    base-packages: com.tigerbrokers.oae.starter.api
  application:
    name: ${spring.application.name}
    parameters:
      machine-room: qw,jja
  protocol:
    name: tri
    port: 8087
    serialization: protobuf
  consumer:
    timeout: 30000
    retries: 0
  registries:
    defaultRegistry:
      address: nacos://${nacos.service-address}?username=nacos&password=YcfMdqcfExbcvQlEe3jc0kzl
      group: test
      zone: test
      default: true
      parameters:
        contextPath: /
    quoteRegistry:
      address: zookeeper://${zk.quote.address}
      default: false

schedule:
  userdefault:
    overwrite: true
    defaultPropertiesDisabled: false
    statisticsEvent: true
  reg:
    serverLists: ************:2181,************:2181,************:2181
    namespace: oae_platform_test
    relatedGroupName: OAE Platform
    serverTcpAddress: tiger-schedule-console.platform-schedule-test.hy-test.dns-test.tiger:9925

microservice:
  auth:
    apollo_host: "https://config-dev.tigerfintech.com/dev-meta" # apollo地址
    client_id: "oae.platform"   # 用户自己服务的client id
    encryption_key: "dsZslMtOBQwosWtEsrZbDjlQnNCVVatRPNOtbfFlVB0"  # 用户自己服务的encryption key
    register_enable: true  # 是否启动自动上报
    rpc_host: "ams-auth-cn-test.tigerbrokers.net:10080" # 自动上报scope的地址，固定
    callee_client_id: "60fff16cb3584bb9a55f79a6441e9dba" # 自动上报服务id，固定

tiger-alarm:
  appName: oae-platform
  domain: https://alarm-dev.tigerbrokers.net # 测试环境
  token: d1sWR6lahyNMCnwqjPyA # 认证token，登录告警平台生成
  environment: dev