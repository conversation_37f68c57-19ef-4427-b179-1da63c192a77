# required, service name
otel.service.name=platform.oae-platform
# required, endpoint address
otel.exporter.otlp.traces.endpoint=http://tracing-analysis-dc-hk-internal.aliyuncs.com/adapt_eczp7ijd7o@04c5541b1f2df41_eczp7ijd7o@53df7ad2afe8301/api/otlp/traces
# required, disable metrics for otel
otel.metrics.exporter=none
# required, disable logs for otel
otel.logs.exporter=none
#optional, javaagent debug or not
otel.javaagent.debug=false
#optional, javaagent enable or disable
otel.javaagent.enable=true