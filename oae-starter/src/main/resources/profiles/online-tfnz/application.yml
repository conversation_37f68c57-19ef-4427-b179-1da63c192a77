spring:
  web:
    enable: false
  application:
    name: oae-platform-tfnz

nacos:
  service-address: ***********:8848,***********:8848,***********:8848

zk:
  quote:
    address: **********:2181,**********:2181,**********:2181

dubbo:
  scan:
    base-packages: com.tigerbrokers.oae.starter.api
  application:
    name: ${spring.application.name}
  protocol:
    name: tri
    port: 8087
    serialization: protobuf
  consumer:
    timeout: 30000
    retries: 0
  registries:
    defaultRegistry:
      address: nacos://${nacos.service-address}
      group: oae-online-tfnz
      default: true
    quoteRegistry:
      address: zookeeper://${zk.quote.address}
      default: false

schedule:
  userdefault:
    overwrite: true
    defaultPropertiesDisabled: false
    statisticsEvent: true
  reg:
    serverLists: **********:2181,**********:2181,**********:2181
    namespace: oae_platform_tfnz
    relatedGroupName: OAE TFNZ
    serverTcpAddress: *************:9925

microservice:
  auth:
    apollo_host: "http://**********:8080" # apollo地址
    client_id: "oae.platform"   # 用户自己服务的client id
    encryption_key: "LwhNImjkO4zMntmDMFxrgLzQC4uFb8F_14EBfIdQahk"  # 用户自己服务的encryption key
    register_enable: true  # 是否启动自动上报
    rpc_host: "oauth-internal.tigerfintech.com:3002" # 自动上报scope的地址，固定
    callee_client_id: "cbdd86e63a6447b9b7c739d05fd4e0e0" # 自动上报服务id，固定

tiger-alarm:
  appName: oae-platform
  domain: https://alarm.tigerfintech.com
  token: qU464tjm6LvVxEIA1buX
  environment: tfnz