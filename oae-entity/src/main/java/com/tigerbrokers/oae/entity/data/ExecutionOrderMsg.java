package com.tigerbrokers.oae.entity.data;

import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
public class ExecutionOrderMsg extends DelayedQueueItem {

    /**
     * 订单参考金额
     * 根据改金额计算的quantity
     */
    @Getter
    private Double refAmount;

    @Getter
    private Long executionOrderId;

    public ExecutionOrderMsg(Long executionOrderId, Double refAmount, long timeout, TimeUnit unit) {
        super(timeout, unit);
        this.refAmount = refAmount;
        this.executionOrderId = executionOrderId;
    }
    public void expire(long timeout, TimeUnit unit) {
        super.expire(timeout, unit);
    }

    public String toString() {
        return JacksonUtil.writeValueAsString(this);
    }
}
