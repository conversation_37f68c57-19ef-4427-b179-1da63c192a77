package com.tigerbrokers.oae.entity.data;

import com.tigerbrokers.infra.alarm.common.enums.MsgTypeEnum;
import com.tigerbrokers.oae.entity.consts.AlertMsgChannel;
import com.tigerbrokers.oae.entity.consts.AlertStyle;
import com.tigerbrokers.oae.entity.consts.AlertType;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.tigerbrokers.hermes.utils.TimesUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
@Data
@Builder
public class AlertMsg {

    private AlertType alertType;

    private String description;

    /**
     * 默认使用 EVENT，不会提醒负责人
     */
    private MsgTypeEnum msgType;

    private AlertMsgChannel channel;

    private List<Pair<String, String>> items;

    public String getContentMsg(String title, boolean isDingding) {
        StringBuilder msgSb = new StringBuilder();
        if (isDingding) {
            msgSb.append(AlertStyle.formatHeading2(title));
        }

        msgSb.append(AlertStyle.getItems(isDingding)).append(AlertStyle.itemsTitle(AlertStyle.TIME_TITLE))
                .append(TimesUtil.printDateTime(System.currentTimeMillis(), CommonConsts.StockMarket.CN.name()))
                .append("\n");
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(item ->
                    msgSb.append(AlertStyle.getItems(isDingding)).append(AlertStyle.itemsTitle(item.getLeft()))
                            .append(item.getRight()).append("\n"));
        }
        if (StringUtils.isNotEmpty(description)) {
            msgSb.append(AlertStyle.getItems(isDingding)).append(AlertStyle.itemsTitle(AlertStyle.MESSAGE_TITLE))
                    .append(description);
        }

        return msgSb.toString();
    }

    public static AlertMsg buildTigerAlarmMsg(String description, AlertType alertType, MsgTypeEnum msgType,
                                              AlertMsgChannel channel) {
        return AlertMsg.builder().description(description)
                .alertType(alertType)
                .msgType(msgType)
                .channel(channel)
                .build();
    }
}
