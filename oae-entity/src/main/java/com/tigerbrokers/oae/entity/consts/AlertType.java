package com.tigerbrokers.oae.entity.consts;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
public enum AlertType {
    BACKGROUND("am.background", "后台任务相关的通知"),
    BUSINESS("am.business", "业务相关的通知");

    /**
     * 指标名，表示业务系统某一类告警事件定义
     * 注：指标名是 英文字符、数字、下划线、横线 字符串组合
     * 例如：指标名 quote.message.dropped 表示 行情消息丢包告警事件
     */
    @Getter
    private String alarmMeterName;

    @Getter
    private String desc;

    AlertType(String alarmMeterName, String desc) {
        this.alarmMeterName = alarmMeterName;
        this.desc = desc;
    }
}
