package com.tigerbrokers.oae.entity.consts;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
public enum AlertMsgChannel {

    DEFAULT_CHANNEL("oae_platform_default_channel", "默认通道");

    /**
     * key对应告警平台中的通道名称
     */
    @Getter
    private String key;

    @Getter
    private String desc;

    AlertMsgChannel(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
