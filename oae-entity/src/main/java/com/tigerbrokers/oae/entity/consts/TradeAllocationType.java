package com.tigerbrokers.oae.entity.consts;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public enum TradeAllocationType {

    USER, TRADER, BREAK;

    public static TradeAllocationType getTradeAllocationType(String allocationType) {
        for (TradeAllocationType type : TradeAllocationType.values()) {
            if (type.name().equalsIgnoreCase(allocationType)) {
                return type;
            }
        }
        return null;
    }
}
