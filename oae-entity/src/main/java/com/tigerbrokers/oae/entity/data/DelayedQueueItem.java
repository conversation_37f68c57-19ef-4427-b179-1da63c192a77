package com.tigerbrokers.oae.entity.data;

import org.springframework.data.redis.core.TimeoutUtils;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
public class DelayedQueueItem implements Delayed {

    private Long deadLine;

    public DelayedQueueItem(long timeout, TimeUnit unit) {
        expire(timeout, unit);
    }

    protected void expire(long timeout, TimeUnit unit) {
        long rawTimeout = TimeoutUtils.toMillis(timeout, unit);
        this.deadLine = System.currentTimeMillis() + rawTimeout;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return deadLine - System.currentTimeMillis();
    }

    @Override
    public int compareTo(Delayed o) {
        return (int) (getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
    }
}
