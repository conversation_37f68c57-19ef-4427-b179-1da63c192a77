package com.tigerbrokers.oae.entity.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021-09-18
 */
public class AmountUtil {

    public static Long toCent(Double amount) {
        if (amount != null) {
            amount = DecimalUtil.formatDecimal(amount, 2);
            Double amountInCent = BigDecimalUtil.multiply(amount, 100.0).doubleValue();
            return amountInCent.longValue();
        }

        return null;
    }

    public static Double fromCent(Long amountInCent) {
        return fromCent(Double.valueOf(amountInCent));
    }

    public static Double fromCent(Double amountInCent) {
        if (amountInCent != null) {
            BigDecimal amountBd = BigDecimalUtil.divide(amountInCent, 100.0);
            if (amountBd != null) {
                return DecimalUtil.formatDecimal(amountBd.doubleValue(), 2);
            }
        }

        return null;
    }
}
