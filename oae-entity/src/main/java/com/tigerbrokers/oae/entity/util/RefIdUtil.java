package com.tigerbrokers.oae.entity.util;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
public class RefIdUtil {


    private static final String SEPARATOR = "_";

    public static final List<String> ALL_REF_ID_PREFIX = Arrays.stream(BusinessSource.values()).map(t -> t.name() + SEPARATOR)
            .collect(Collectors.toList());

    private static final String TRADER_REF_ID_INFIX = "_TRADER_";

    private static final String DRIP_TRADE_REF_ID_INFIX = "DIV";

    /**
     * 1.用户资产锁定
     * 2.后台账本分配
     * 3.后台调用中台生成订单
     * 都需要用此RefId
     * 示例： RSP_1, DRIP_1
     */
    public static String getUserRefId(Long id, String businessSource) {
        return businessSource + SEPARATOR + id;
    }

    /**
     * TraderAccount小资产分配
     * 需要用此RefId
     * 示例：RSP_TRADER_1
     */
    public static String getTraderRefId(Long id, String businessSource) {
        return getTraderPrefix(businessSource) + id;
    }

    /**
     * 结算，后台账本分配，生成refID
     * @param id
     * @param dividendId
     * @param allocationType
     * @param businessSource
     * @return
     */
    public static String getAllocationRefId(Long id, Long dividendId, String allocationType, String businessSource) {
        if (TradeAllocationType.BREAK.name().equals(allocationType)) {
            return null;
        }
        if (TradeAllocationType.USER.name().equals(allocationType)) {
            //drip trade refId生成逻辑变化，兼容历史数据
            if (BusinessSource.DRIP.name().equals(businessSource) && dividendId != null && BigDecimalUtil.isMoreThan(dividendId, 0)) {
                return getDripTradeRefId(id, dividendId, businessSource);
            } else {
                return getUserRefId(id, businessSource);
            }
        } else {
            return getTraderRefId(id, businessSource);
        }
    }

    /**
     * DRIP调用后台账本生成refID，增加分红id
     * eg: DIV_{dividendId}_DRIP_{userOrderId}
     */
    public static String getDripTradeRefId(Long id, Long dividendId, String businessSource) {
        return String.join(SEPARATOR, DRIP_TRADE_REF_ID_INFIX, String.valueOf(dividendId), businessSource, String.valueOf(id));
    }

    public static boolean isTraderSubOrder(String refId) {
        return Arrays.stream(BusinessSource.values()).anyMatch(anEnum -> refId.startsWith(getTraderPrefix(anEnum.name())));
    }

    private static String getTraderPrefix(String businessSource) {
        return businessSource + TRADER_REF_ID_INFIX;
    }

    public static String removePrefix(String refId) {
        for (String prefix : ALL_REF_ID_PREFIX) {
            if (refId.contains(prefix)) {
                refId = Arrays.stream(refId.split(SEPARATOR)).reduce((first, second) -> second).orElse(null);
                break;
            }
        }
        return refId;
    }
}
