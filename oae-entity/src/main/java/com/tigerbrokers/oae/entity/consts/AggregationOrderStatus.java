package com.tigerbrokers.oae.entity.consts;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public enum AggregationOrderStatus {

    /**
     * NEW: 新建记录
     */
    NEW,
    /**
     * WAITING_ODD: 等待下碎股单
     */
    WAITING_ODD,
    /**
     * FILLED: filled_quantity!=0,对应所有execution_order到最终状态
     */
    FILLED,
    /**
     * REJECTED：filled_quantity=0,对应所有execution_order到最终状态
     */
    REJECTED;

    public static final List<String> PROCESSING_STATUS = Arrays.asList(NEW.name(), WAITING_ODD.name());

}
