package com.tigerbrokers.oae.entity.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public class BigDecimalUtil {

    public static BigDecimal add(Double d1, Double d2) {
        d1 = d1 == null ? 0 : d1;
        d2 = d2 == null ? 0 : d2;
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.add(b2);
    }

    public static BigDecimal add(List<Double> ds) {
        return ds.stream()
                .filter(item -> item != null)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal subtract(Double d1, Double d2) {
        d1 = d1 == null ? 0 : d1;
        d2 = d2 == null ? 0 : d2;
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.subtract(b2);
    }

    public static BigDecimal multiply(Double d1, Double d2) {
        d1 = d1 == null ? 0 : d1;
        d2 = d2 == null ? 0 : d2;
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.multiply(b2);
    }

    public static BigDecimal divide(Double d1, Double d2) {
        d1 = d1 == null ? 0 : d1;
        if (d2 == null || isEqual(d2, 0.0)) {
            return null;
        }
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.divide(b2);
    }

    public static BigDecimal divide(Double d1, Double d2, int scale, RoundingMode roundingMode) {
        d1 = d1 == null ? 0 : d1;
        if (d2 == null || isEqual(d2, 0.0)) {
            return null;
        }
        BigDecimal b1 = BigDecimal.valueOf(d1);
        BigDecimal b2 = BigDecimal.valueOf(d2);
        return b1.divide(b2, scale, roundingMode);
    }

    public static boolean isEqual(double var1, double var2) {
        return BigDecimal.valueOf(var1).compareTo(BigDecimal.valueOf(var2)) == 0;
    }

    public static boolean isNotEqual(double var1, double var2) {
        return !isEqual(var1, var2);
    }

    public static boolean isMoreThan(double var1, double var2) {
        return BigDecimal.valueOf(var1).compareTo(BigDecimal.valueOf(var2)) > 0;
    }

    public static boolean isLessThan(double var1, double var2) {
        return BigDecimal.valueOf(var1).compareTo(BigDecimal.valueOf(var2)) < 0;
    }

    public static boolean isMoreOrEqualThan(double var1, double var2) {
        return BigDecimal.valueOf(var1).compareTo(BigDecimal.valueOf(var2)) >= 0;
    }

    public static boolean isLessOrEqualThan(double var1, double var2) {
        return BigDecimal.valueOf(var1).compareTo(BigDecimal.valueOf(var2)) <= 0;
    }

    public static int scale(Double var) {
        int scale = BigDecimal.valueOf(var).stripTrailingZeros().scale();
        if (scale <= 0) {
            return 0;
        } else {
            return scale;
        }
    }

    public static int toInt(Double var) {
        if (var == null) {
            return 0;
        }
        int scale = scale(var);
        if (scale == 0) {
            return var.intValue();
        }
        Double varInInt = multiply(var, Math.pow(10, scale)).doubleValue();
        return varInInt.intValue();
    }

    public static long toLong(Double var) {
        if (var == null) {
            return 0;
        }
        int scale = scale(var);
        if (scale == 0) {
            return var.intValue();
        }
        Double varInInt = multiply(var, Math.pow(10, scale)).doubleValue();
        return varInInt.longValue();
    }

    public static double toDouble(Integer var, Integer scale) {
        if (var == null || scale == null) {
            return 0;
        }
        BigDecimal bd =  divide((double) var, Math.pow(10, scale));
        return bd != null ? bd.doubleValue() : 0;
    }

    public static double toDouble(Long var, Integer scale) {
        if (var == null || scale == null) {
            return 0;
        }
        BigDecimal bd =  divide((double) var, Math.pow(10, scale));
        return bd != null ? bd.doubleValue() : 0;
    }
}
