package com.tigerbrokers.oae.entity.consts;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public enum LockedAssetStatus {
    /**
     * LOCKED_PENDING: 新建记录
     */
    LOCKED_PENDING,
    /**
     * LOCKED：om trade返回锁定成功
     */
    LOCKED,
    /**
     * LOCKED_ERROR: 锁定失败
     */
    LOCKED_ERROR,
    /**
     * UNLOCKED_PENDING：需要解锁，尚未发送解锁请求
     */
    UNLOCKED_PENDING,
    /**
     * UNLOCKED：om trade解锁完成
     */
    UNLOCKED,
    /**
     * UNLOCKED_ERROR: 解锁失败
     */
    UNLOCKED_ERROR;
}
