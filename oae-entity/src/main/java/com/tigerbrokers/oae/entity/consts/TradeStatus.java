package com.tigerbrokers.oae.entity.consts;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 交易中台订单类型
 * AggregationExecutionOrder.status
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public enum TradeStatus {

    PENDING_NEW("PendingNew"),
    NEW("New"),
    FILLED("Filled"),
    CANCELED("Canceled"),
    REJECTED("Rejected"),
    EXPIRED("Expired");

    @Getter
    String value;

    TradeStatus(String value) {
        this.value = value;
    }

    private static final List<TradeStatus> FINISHED_STATUS = Arrays.asList(FILLED, CANCELED, REJECTED, EXPIRED);

    public static final List<String> FINISHED_STATUS_VALUE = FINISHED_STATUS.stream()
            .map(TradeStatus::getValue).collect(Collectors.toList());

    public static final List<String> UNFINISHED_STATUS_VALUE = Stream.of(PENDING_NEW, NEW).map(TradeStatus::getValue)
            .collect(Collectors.toList());

    public static Boolean isFinishedStatus(String tradeStatus) {
        return FINISHED_STATUS_VALUE.contains(tradeStatus);
    }
}
