package com.tigerbrokers.oae.entity.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class DecimalUtil {

    public static final String ROUND_UP = "ROUND_UP";

    public static final String ROUND_DOWN = "ROUND_DOWN";

    public static final String ROUND_HALF_UP = "ROUND_HALF_UP";

    public static final String ROUND_CEILING = "ROUND_CEILING";

    public static final String ROUND_FLOOR = "ROUND_FLOOR";

    public static Double formatDecimal(Double value, int scale) {
        if (value != null) {
            BigDecimal decimal = BigDecimal.valueOf(value);
            return decimal.setScale(scale, RoundingMode.HALF_EVEN).doubleValue();
        }

        return null;
    }

    public static double formatDecimal(double value, int scale, String roundingType) {
        int roundingTypeValue = getRoundingValue(roundingType);

        return formatDecimal(value, scale, roundingTypeValue);
    }

    public static double formatDecimal(double value, int scale, int roundingTypeValue) {
        BigDecimal decimal = BigDecimal.valueOf(value);

        return decimal.setScale(scale, roundingTypeValue).doubleValue();
    }

    public static int getRoundingValue(String roundingType) {
        switch (roundingType) {
            case ROUND_UP:
                return BigDecimal.ROUND_UP;
            case ROUND_DOWN:
                return BigDecimal.ROUND_DOWN;
            case ROUND_CEILING:
                return BigDecimal.ROUND_CEILING;
            case ROUND_FLOOR:
                return BigDecimal.ROUND_FLOOR;
            default:
                return BigDecimal.ROUND_HALF_UP;
        }
    }
}
