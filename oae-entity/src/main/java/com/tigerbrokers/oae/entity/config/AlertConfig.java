package com.tigerbrokers.oae.entity.config;

import com.ctrip.framework.apollo.spring.annotation.TigerConfigBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
public class AlertConfig {

    private static final String ALERT_CONFIG_NAMESPACE = "AlertConfig";

    @TigerConfigBean(namespace = ALERT_CONFIG_NAMESPACE, prefix = "alert.tiger.alarm.client")
    @Data
    public static class TigerAlarmClientConfig {
        private Boolean enable;
    }
}
