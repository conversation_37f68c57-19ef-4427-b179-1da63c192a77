package com.tigerbrokers.oae.entity.util;

import com.tigerbrokers.ams.microservice.management.context.MicroserviceContext;
import com.tigerbrokers.hermes.utils.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
@Slf4j
public class ServerUtil {

    public static String getClient() {
        log.info("TOKEN_STORE:{}, IP_STORE:{}, SOURCE:{}, SERVICE_NAME:{}",
                MicroserviceContext.getToken(), MicroserviceContext.getIp(),
                MicroserviceContext.getSource(), MicroserviceContext.getServiceName());
        return MicroserviceContext.getServiceName();
    }

    public static String getLang(String lang) {
        if (StringUtils.isEmpty(lang)) {
            lang = MessageUtil.EN;
        }
        return lang;
    }
}
