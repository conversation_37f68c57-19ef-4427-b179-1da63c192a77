package com.tigerbrokers.oae.entity.service;

import com.tigerbrokers.infra.alarm.client.AlarmParam;
import com.tigerbrokers.infra.alarm.client.AlarmPayload;
import com.tigerbrokers.infra.alarm.client.TigerAlarmClient;
import com.tigerbrokers.infra.alarm.client.entity.SendResult;
import com.tigerbrokers.infra.alarm.common.enums.MsgTypeEnum;
import com.tigerbrokers.oae.entity.config.AlertConfig;
import com.tigerbrokers.oae.entity.consts.AlertMsgChannel;
import com.tigerbrokers.oae.entity.consts.AlertType;
import com.tigerbrokers.oae.entity.data.AlertMsg;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
@Slf4j
@Service
public class AlertService {

    private static final String TITLE = "OAE Platform";

    @Autowired
    private TigerAlarmClient tigerAlarmClient;

    @Autowired
    private AlertConfig.TigerAlarmClientConfig tigerAlarmClientConfig;

    private LinkedBlockingQueue<AlertMsg> alertMsgQueue;

    private ExecutorService executorService;

    @Autowired
    public AlertService() {
        this.alertMsgQueue = new LinkedBlockingQueue<>(1000);
        this.executorService = Executors.newSingleThreadExecutor();
        this.executorService.submit(() -> sendMsg());
    }

    public void sendMsg(String content) {
        AlertMsg alertMsg = AlertMsg.buildTigerAlarmMsg(content, AlertType.BACKGROUND, MsgTypeEnum.EVENT,
                AlertMsgChannel.DEFAULT_CHANNEL);
        sendMsg(alertMsg);
    }

    public void sendMsg(AlertMsg alertMsg) {
        if (isTigerClientEnable()) {
            String msg = JacksonUtil.writeValueAsString(alertMsg);
            log.info("add alert to msg queue: {}", msg);
            alertMsgQueue.add(alertMsg);
        }
    }

    private void sendMsg() {
        while (true) {
            try {
                AlertMsg alertMsg = alertMsgQueue.take();
                log.info("take alert msg queue: {}", JacksonUtil.writeValueAsString(alertMsg));
                sendMsgWithTigerAlarmClient(alertMsg);
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("sendMsg {}", e.getMessage(), e);
            }
        }
    }

    private void sendMsgWithTigerAlarmClient(AlertMsg msg) {
        AlarmParam alarmParam = new AlarmParam();
        alarmParam.setTitle(getTitle());
        alarmParam.setMsgType(msg.getMsgType() != null ? msg.getMsgType() : MsgTypeEnum.EVENT);
        AlarmPayload<String> alarmPayload = new AlarmPayload<>();
        alarmPayload.setData(msg.getContentMsg(getTitle(), false));

        AlertMsgChannel channel = msg.getChannel() == null
                ? AlertMsgChannel.DEFAULT_CHANNEL
                : msg.getChannel();
        Future<SendResult> future = tigerAlarmClient.sendMessage(
                channel.getKey(),
                msg.getAlertType().getAlarmMeterName(),
                alarmPayload,
                alarmParam);
        // 注：为不堵塞业务现场，告警消息发送异步执行，默认失败重试3次
        // future.get(10, TimeUnit.SECONDS); 非必须操作项，业务可以不关注SendResult
        try {
            SendResult sendResult = future.get(10, TimeUnit.SECONDS);
            log.info("sendAlarm result={}", sendResult);
        } catch (Exception e) {
            log.error("sendMsgWithTigerAlarmClient error, message {}", e.getMessage(), e);
            Thread.currentThread().interrupt();
        }
    }

    private String getTitle() {
        return TITLE;
    }

    private boolean isTigerClientEnable() {
        return BooleanUtils.isTrue(tigerAlarmClientConfig.getEnable());
    }
}
