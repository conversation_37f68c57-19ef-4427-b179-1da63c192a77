package com.tigerbrokers.oae.entity.consts;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
public enum ExecutionErrorCode {

    /**
     * 不满足最小执行数量
     */
    NOT_SATISFY_MINIMIZE_QUANTITY("not satisfy minimize quantity"),
    /**
     * 不满足下碎股单条件
     */
    NOT_SATISFY_ODD("not satisfy odd condition"),
    /**
     * 需要下碎股单
     */
    NEED_PLACE_ODD("need place odd order"),
    /**
     * 缺少行情合约相关数据
     */
    NO_CONTRACT_QUOTE_DATA("no contract quote data"),
    /**
     * 没有成交订单
     */
    NO_FILLED_ORDERS("no filled orders due to market liquidity"),
    /**
     * 没有成交数量
     */
    NO_FILLED_QUANTITY("no filled quantity due to market liquidity"),

    /**
     * 交易金额不足以买入1股
     */
    NOT_SATISFY_PRICE("trade amount(%s) cannot buy 1 share at avg price(%s)"),
    ;

    final String msg;

    ExecutionErrorCode(String msg) {
        this.msg = msg;
    }

    public String getMsg(Object... params) {
        return String.format(msg, params);
    }
}
