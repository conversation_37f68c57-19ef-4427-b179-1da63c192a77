package com.tigerbrokers.oae.entity.consts;

/**
 * <AUTHOR>
 * @date 2023/2/15
 */
public class AlertStyle {

    private static final String HEADING_2 = "## ";

    private static final String BOLD = "**";

    public static final String ITEMS = "- ";

    public static final String NEW_LINE = "\n\n";

    public static final String MESSAGE_TITLE = "Message";

    public static final String TIME_TITLE = "Time";

    public static String formatHeading2(String heading) {
        return formatHeading(HEADING_2, heading);
    }

    public static String itemsTitle(String title) {
        return new StringBuilder(BOLD).append(title).append(":").append(BOLD).append(" ").toString();
    }

    private static String formatHeading(String headingFormat, String heading) {
        return new StringBuilder(headingFormat).append(heading).append("\n").toString();
    }

    public static String getItems(boolean isDingding) {
        if (isDingding) {
            return ITEMS;
        }

        return "";
    }
}
