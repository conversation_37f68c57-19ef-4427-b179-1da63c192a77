package com.tigerbrokers.oae.entity.consts;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public enum UserOrderStatus {

    /**
     * PENDING：收到rsp management订单状态
     */
    PENDING,
    /**
     * NEW：pre trade&锁定金额完成，返回rsp management结果
     */
    NEW,
    /**
     * FILLED：计算完filled_quantity&avg_price
     */
    FILLED,
    /**
     * COMPLETED：中台订单id同步完成
     */
    COMPLETED,
    /**
     * REJECTED：pre trade/锁定金额/未成交
     */
    REJECTED;

    private static final List<UserOrderStatus> FINISHED_STATUS_LIST = Arrays.asList(COMPLETED, REJECTED);

    public static boolean isFinishedStatus(String statusStr) {
        UserOrderStatus status = valueOf(statusStr);
        return FINISHED_STATUS_LIST.contains(status);
    }
}
