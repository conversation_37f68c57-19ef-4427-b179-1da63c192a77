<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oae-platform</artifactId>
        <groupId>com.tigerbrokers.oae.platform</groupId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oae-entity</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.infra.config</groupId>
            <artifactId>tiger-apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.commons</groupId>
            <artifactId>storage</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers</groupId>
            <artifactId>stock-quote-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.commons</groupId>
            <artifactId>extra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.infra</groupId>
            <artifactId>alarm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.infra</groupId>
            <artifactId>alarm-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.hermes</groupId>
            <artifactId>hermes-common</artifactId>
        </dependency>
        <!-- rpc服务鉴权 -->
        <dependency>
            <groupId>com.tigerbrokers.ams</groupId>
            <artifactId>microservice-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.ams</groupId>
            <artifactId>microservice-apache-dubbo</artifactId>
        </dependency>
        <!-- rpc服务鉴权scope自动注册 -->
        <dependency>
            <groupId>com.tigerbrokers.ams</groupId>
            <artifactId>grpc-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven.deploy.plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>