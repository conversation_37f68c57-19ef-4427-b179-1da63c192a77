package com.tigerbrokers.oae.api.error;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public enum ErrorCode {
    /**
     * 下单参数：金额参数需要大于0
     */
    PLACE_ORDER_REQUEST_AMOUNT_ERROR("amount or quantity is not positive"),
    /**
     * 下单参数：下单来源不支持
     */
    PLACE_ORDER_REQUEST_SOURCE_ERROR("source not support"),
    /**
     * 下单参数：请求参数不能未空
     */
    PLACE_ORDER_REQUEST_PARAMS_ERROR("%s is null"),
    /**
     * 下单失败
     */
    PLACE_ORDER_ERROR("place order error"),
    /**
     * 获取合约信息失败
     */
    QUERY_CONTRACT_ERROR("query contract info error"),
    /**
     * 获取上手和deal account失败
     */
    QUERY_EXECUTION_MASTER_AND_DEAL_ACCOUNT_ERROR("query execution master and deal account error"),

    /**
     * PreTrade检查未通过
     */
    PRE_TRADE_NOT_PASS("pre trade not pass: %s"),

    CALCULATE_MARGIN_ERROR("calculate margin error: %s"),
    /**
     * 锁定资产失败
     */
    LOCK_ASSET_FAIL("lock asset fail"),

    /**
     * 不支持的执行上手
     */
    UNSUPPORTED_EXECUTING_BROKER("unsupported executing broker");

    String msg;

    ErrorCode(String msg) {
        this.msg = msg;
    }

    public String getMsg(Object... params) {
        return String.format(msg, params);
    }
}
