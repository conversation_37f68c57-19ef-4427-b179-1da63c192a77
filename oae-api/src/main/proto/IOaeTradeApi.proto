syntax = "proto3";

package com.tigerbrokers.oae.api;

option java_multiple_files = true;
option java_package = "com.tigerbrokers.oae.api";
option java_outer_classname = "IOaeTradeApiProto";
option objc_class_prefix = "DEMOSRV";

message PlaceOrderRequest {
  string external_id = 1;
  string account = 2;
  string symbol = 3;
  string currency = 4;
  string action = 5;
  double amount = 6;
  string market = 7;
  string sec_type = 8;
  string seg_type = 9;
  string order_type = 10;
  double quantity = 11;
  string source = 12;
  bool apply_margin = 13;
  int64 portfolio_id = 14;
  int64 dividend_id = 15;
}

message PlaceOrderReply {
  bool is_succ = 1;
  string error_code = 2;
  string error_msg = 3;
  PlaceOrderInfo place_order_info = 4;
}

message PlaceOrderInfo {
  int64 id = 1;
  string external_id = 2;
}

service IOaeTradeApi {
  rpc placeOrder (PlaceOrderRequest) returns (PlaceOrderReply) {}
}