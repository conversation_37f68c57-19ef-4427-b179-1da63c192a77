# oae-platform

Order aggregation execution platform

[港股定投方案设计-飞书文档](https://tigertech.feishu.cn/wiki/wikcnlRsH2YpPjIfd9fVuUIqvMg)

## 1. 港股定投任务

| 运行时间  | 任务                | 说明                                                                                                 |
|-------|-----------------------|----------------------------------------------------------------------------------------------------|
| 11:30 | `AggregationHKJob`    | 用户订单聚合&下单任务                                                                                        |
| 15:30 | `AggregationOddHKJob` | 碎股单下单任务                                                                                            |
| 16:30 | `PreAllocationHKJob`  | 后台账本预分配任务 任务编排，执行成功后顺序执行 <br/>`AllocationReportHKJob` <br/>生成后台账本分配报表<br/>`AllocationHKJob` 后台账本分配 |

## 2. 上线部署文档
* [oae部署-飞书文档](https://tigertech.feishu.cn/wiki/wikcnYQKAWcZ4gY5qNyYMYRCO4g)

## 3. 测试环境
* [配置中心](https://config-dev.tigerfintech.com/config.html?#/appid=oae_platform)
* [jenkins](https://jks.tigerfintech.com/job/scrum1-oae-platform/)
* [调用服务权限申请](https://test-oauth.tigerfintech.com/developer/static/index.html#/application/clients/646c9228-6c7b-4666-a90a-df8ea03208f7) `oae.trade:write 股票聚合下单写操作`

## 4. 生产环境
* [配置中心](https://config.tigerfintech.com/config.html?#/appid=oae_platform)
* [deploy]()