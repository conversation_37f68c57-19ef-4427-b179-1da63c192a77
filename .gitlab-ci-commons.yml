stages:
  - test

variables:
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end"
  MAVEN_SKIP_FAILURE: "-Dmaven.test.failure.ignore=true"
  SONAR_PROPERTIES: "-Dsonar.host.url=http://sonar.tigerfintech.com
            -Dsonar.login=$SONAR_TOKEN
            -Dsonar.projectKey=$CI_PROJECT_NAME
            -Dsonar.projectName=$CI_PROJECT_NAME
            -Dsonar.sources=src/main/java
            -Dsonar.exclusions=**/thrift/**/*,**/generated/**/*,**src/test/**/*
            -Dsonar.coverage.exclusions=**/thrift/**/*,**/model/**/*,**/controller/**/*,**/ctrl/**/*,**/config/**/*,**/data/**/*,**/fitnesse/**/*
            -Dsonar.gitlab.project_id=$CI_PROJECT_ID
            -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA
            -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
            -Dsonar.gitlab.only_issue_from_commit_line=true
            -Dsonar.gitlab.only_issue_from_commit_file=true
          "

mvn_install:
  stage: test
  script:
    - mvn $MAVEN_CLI_OPTS $MAVEN_SKIP_FAILURE -DskipTests install
  only:
    - master

sonar_analyze:
  stage: test
  script:
    - mvn $MAVEN_CLI_OPTS verify sonar:sonar $SONAR_PROPERTIES
    ## sonar coverage 计算公式是 coverage = (branch_covered + line_covered) / (branch_total + line_total) =  (7 + 9) / (6 + 7 + 8 + 9)
    - jacoco=`find . -name "jacoco.csv"`; if [ "$jacoco" != "" ]; then awk -F"," '{ b += $6 + $7; bc += $7; l += $8 + $9; lc += $9 } END { printf "Total %.1f%% covered", 100.0*(bc+lc)/(b+l) }' $jacoco; fi
  coverage: "/Total.*?([0-9]{1,3}.[0-9])%/"
  rules:
    # push to master
    - if: $CI_COMMIT_BRANCH == 'master'
    # merge request to master only
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
  artifacts:
    reports:
      junit:
        - ./**/target/surefire-reports/TEST-*.xml