
ALTER TABLE user_order ADD COLUMN `filled_time` datetime DEFAULT NULL COMMENT '成交时间' after `trade_time`;

ALTER TABLE aggregation_execution_order ADD COLUMN `settle_date` date DEFAULT NULL COMMENT '结算日期' after `trade_time`;
ALTER TABLE aggregation_execution_order ADD COLUMN `business_date` date DEFAULT NULL COMMENT '记账日期' after `trade_time`;
ALTER TABLE aggregation_execution_order ADD COLUMN `filled_time` datetime DEFAULT NULL COMMENT '成交时间' after `trade_time`;

ALTER TABLE trade_allocation ADD COLUMN `settle_date` date DEFAULT NULL COMMENT '结算日期' after `trade_time`;
ALTER TABLE trade_allocation ADD COLUMN `business_date` date DEFAULT NULL COMMENT '记账日期' after `trade_time`;
ALTER TABLE trade_allocation ADD COLUMN `filled_time` datetime DEFAULT NULL COMMENT '成交时间' after `trade_time`;