-- MySQL dump 10.13  Distrib 5.7.17, for macos10.12 (x86_64)
--
-- Host: ************    Database: oae_platform
-- ------------------------------------------------------
-- Server version	5.7.21-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `aggregation_execution_order`
--

DROP TABLE IF EXISTS `aggregation_execution_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `aggregation_execution_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `trade_id` varchar(45) DEFAULT NULL COMMENT '中台下单id',
  `aggregation_id` bigint(20) NOT NULL COMMENT '聚合订单id，aggregation_order.id',
  `deal_account` varchar(45) NOT NULL COMMENT '交易员账号',
  `execution_master` varchar(45) DEFAULT NULL COMMENT '执行上手',
  `symbol` varchar(45) NOT NULL COMMENT '代码',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `action` varchar(45) NOT NULL COMMENT 'BUY/SELL',
  `total_quantity` double DEFAULT NULL COMMENT '下单数量',
  `filled_quantity` double DEFAULT NULL COMMENT '成交数量',
  `price` double DEFAULT NULL COMMENT '下单限价',
  `avg_price` double DEFAULT NULL COMMENT '平均成本',
  `order_type` varchar(45) DEFAULT NULL COMMENT '订单类型',
  `seg_type` varchar(45) DEFAULT NULL COMMENT 'seg_type',
  `sec_type` varchar(45) DEFAULT NULL COMMENT 'sec_type',
  `trade_time` datetime DEFAULT NULL COMMENT '下单时间',
  `status` varchar(45) DEFAULT NULL COMMENT '状态',
  `trade_status` varchar(45) DEFAULT NULL COMMENT '中台交易状态',
  `error_code` varchar(45) DEFAULT NULL COMMENT '错误码',
  `error_msg` varchar(100) DEFAULT NULL COMMENT '错误信息',
  `type` varchar(20) DEFAULT 'COMMON' NULL COMMENT '执行类型：COMMON/ODD',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `trade_id_UNIQUE` (`trade_id`),
  KEY `trade_id_INDEX` (`trade_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合执行订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `aggregation_execution_order_history`
--

DROP TABLE IF EXISTS `aggregation_execution_order_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `aggregation_execution_order_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `aggregation_execution_id` bigint(20) DEFAULT NULL COMMENT '聚合执行订单id，aggregation_execution_order.id',
  `trade_id` varchar(45) DEFAULT NULL COMMENT '中台下单id',
  `aggregation_id` bigint(20) DEFAULT NULL COMMENT '聚合订单id，aggregation_order.id',
  `deal_account` varchar(45) DEFAULT NULL COMMENT '交易员账号',
  `execution_master` varchar(45) DEFAULT NULL COMMENT '执行上手',
  `symbol` varchar(45) DEFAULT NULL COMMENT '代码',
  `market` varchar(45) DEFAULT NULL COMMENT '市场',
  `currency` varchar(45) DEFAULT NULL COMMENT '货币类型',
  `action` varchar(45) DEFAULT NULL COMMENT 'BUY/SELL',
  `total_quantity` double DEFAULT NULL COMMENT '下单数量',
  `filled_quantity` double DEFAULT NULL COMMENT '成交数量',
  `price` double DEFAULT NULL COMMENT '下单限价',
  `avg_price` double DEFAULT NULL COMMENT '平均成本',
  `order_type` varchar(45) DEFAULT NULL COMMENT '订单类型',
  `seg_type` varchar(45) DEFAULT NULL COMMENT 'seg_type',
  `sec_type` varchar(45) DEFAULT NULL COMMENT 'sec_type',
  `trade_time` datetime DEFAULT NULL COMMENT '下单时间',
  `status` varchar(45) DEFAULT NULL COMMENT '状态',
  `trade_status` varchar(45) DEFAULT NULL COMMENT '中台交易状态',
  `error_code` varchar(45) DEFAULT NULL COMMENT '错误码',
  `error_msg` varchar(100) DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合执行订单数据变化';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `aggregation_execution_order_record`
--

DROP TABLE IF EXISTS `aggregation_execution_order_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `aggregation_execution_order_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `aggregation_execution_id` bigint(20) NOT NULL COMMENT '聚合执行订单id，aggregation_execution_order.id',
  `aggregation_id` bigint(20) NOT NULL COMMENT '聚合订单id，aggregation_order.id',
  `symbol` varchar(45) NOT NULL COMMENT '代码',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `action` varchar(45) NOT NULL COMMENT 'BUY/SELL',
  `operation` varchar(45) NOT NULL COMMENT '操作类型，PLACE/MODIFY',
  `amount` double NOT NULL COMMENT '金额',
  `ref_price` double NOT NULL COMMENT '参考价格',
  `quantity` double NOT NULL COMMENT '数量',
  `lot_size_unit` int(11) NOT NULL COMMENT '每手股数',
  `pre_filled_quantity` double DEFAULT NULL COMMENT '订单已经成交数量',
  `pre_avg_price` double DEFAULT NULL COMMENT '订单已经成交部分平均成本',
  `status` varchar(45) NOT NULL COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合执行订单，订单变更记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `aggregation_order`
--

DROP TABLE IF EXISTS `aggregation_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `aggregation_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `deal_account` varchar(45) NOT NULL COMMENT '交易员账号',
  `execution_master` varchar(45) DEFAULT NULL COMMENT '执行上手',
  `symbol` varchar(45) NOT NULL COMMENT '代码',
  `seg_type` varchar(45) NOT NULL COMMENT 'seg_type',
  `sec_type` varchar(45) NOT NULL COMMENT 'sec_type',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `action` varchar(45) NOT NULL COMMENT 'BUY/SELL',
  `trade_amount` double NOT NULL COMMENT '交易金额',
  `filled_amount` double DEFAULT NULL COMMENT '成交金额',
  `filled_quantity` double DEFAULT NULL COMMENT '成交数量',
  `avg_price` double DEFAULT NULL COMMENT '平均成本',
  `order_type` varchar(45) NOT NULL COMMENT '订单类型',
  `error_code` varchar(45) DEFAULT NULL COMMENT '错误码',
  `status` varchar(45) NOT NULL COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聚合订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `locked_asset`
--

DROP TABLE IF EXISTS `locked_asset`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locked_asset` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `user_order_id` bigint(20) NOT NULL COMMENT '用户订单id，user_order.id',
  `account` varchar(45) NOT NULL COMMENT '账号',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `amount` double NOT NULL COMMENT '锁定金额',
  `trade_time` datetime NOT NULL,
  `status` varchar(45) NOT NULL COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_order_id_UNIQUE` (`user_order_id`),
  KEY `market_status_INDEX` (`market`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产锁定';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trade_allocation`
--

DROP TABLE IF EXISTS `trade_allocation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trade_allocation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `ref_id` bigint(20) NOT NULL COMMENT 'user_order.id / aggregation_order.id',
  `seg_type` varchar(45) NOT NULL COMMENT 'seg_type',
  `contract_id` bigint(20) DEFAULT NULL COMMENT '合约id',
  `symbol` varchar(45) NOT NULL COMMENT '代码',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `type` varchar(45) NOT NULL COMMENT 'USER/TRADR',
  `source_account` varchar(45) DEFAULT NULL COMMENT '转出账号',
  `target_account` varchar(45) DEFAULT NULL COMMENT '目标账号',
  `filled_quantity` double DEFAULT NULL COMMENT '成交数量',
  `avg_price` double DEFAULT NULL COMMENT '平均成本',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `trade_time` datetime NOT NULL COMMENT '交易时间',
  `status` varchar(45) NOT NULL COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `market_status_INDEX` (`market`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='交易分配';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_order`
--

DROP TABLE IF EXISTS `user_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `external_id` varchar(45) NOT NULL COMMENT '请求方id',
  `trade_id` varchar(45) DEFAULT NULL COMMENT '中台订单id',
  `aggregation_id` bigint(20) DEFAULT NULL COMMENT '聚合订单id，aggregation_order.id',
  `account` varchar(45) NOT NULL COMMENT '账号',
  `deal_account` varchar(45) NOT NULL COMMENT '交易员账号',
  `execution_master` varchar(45) NOT NULL COMMENT '执行上手',
  `symbol` varchar(45) NOT NULL COMMENT '代码',
  `seg_type` varchar(45) NOT NULL COMMENT 'seg_type',
  `sec_type` varchar(45) NOT NULL COMMENT 'sec_type',
  `market` varchar(45) NOT NULL COMMENT '市场',
  `currency` varchar(45) NOT NULL COMMENT '货币类型',
  `action` varchar(45) NOT NULL COMMENT 'BUY/SELL',
  `amount` double DEFAULT NULL COMMENT '金额',
  `trade_amount` double DEFAULT NULL COMMENT '交易金额',
  `filled_quantity` double DEFAULT NULL COMMENT '成交数量',
  `avg_price` double DEFAULT NULL COMMENT '平均成本',
  `trade_time` datetime NOT NULL COMMENT '下单时间',
  `order_type` varchar(45) NOT NULL COMMENT '订单类型',
  `client` varchar(45) DEFAULT NULL COMMENT '订单来源',
  `status` varchar(45) NOT NULL COMMENT '状态',
  `error_code` varchar(45) DEFAULT NULL COMMENT '错误码',
  `error_msg` varchar(100) DEFAULT NULL COMMENT '错误信息',
  `trade_msg` text COMMENT '中台推送用户订单消息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `external_id_UNIQUE` (`external_id`),
  KEY `aggregation_id_INDEX` (`aggregation_id`),
  KEY `symbol_status_INDEX` (`symbol`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户订单';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-03-01 14:18:51
