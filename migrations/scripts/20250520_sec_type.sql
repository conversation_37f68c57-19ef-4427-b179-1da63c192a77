alter table trade_allocation
add `sec_type` varchar(45) DEFAULT NULL COMMENT 'sec_type' after `seg_type`;

alter table locked_asset
add `sec_type` varchar(45) DEFAULT NULL COMMENT 'sec_type' after `seg_type`;

update trade_allocation
set sec_type = 'FUND'
where seg_type = 'FUND';

update trade_allocation
set sec_type = 'STK'
where seg_type = 'SEC';

update locked_asset
set sec_type = 'FUND'
where seg_type = 'FUND';

update locked_asset
set sec_type = 'STK'
where seg_type = 'SEC';