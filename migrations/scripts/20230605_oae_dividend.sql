
--- user_order
-- 字段变更
alter table user_order add column `quantity` double DEFAULT NULL COMMENT '股数' after `amount`;
alter table user_order add column `filled_amount` double DEFAULT NULL COMMENT '卖出成交金额' after `filled_quantity`;
alter table user_order add column `source` varchar(20) NOT NULL DEFAULT 'RSP' COMMENT '订单来源 DRIP/RSP' after `status`;
-- 修改字段长度
alter table user_order modify column `error_msg` varchar(300) DEFAULT NULL COMMENT '错误信息';
-- 索引
ALTER TABLE user_order DROP INDEX external_id_UNIQUE;
ALTER TABLE user_order ADD UNIQUE KEY external_id_UNIQUE (source, external_id);

--- locked_asset
-- 字段变更
alter table locked_asset add column `lock_step` varchar(20) DEFAULT 'PLACE_ORDER' COMMENT '锁定节点' after `status`;
alter table locked_asset add column `quantity` double DEFAULT NULL COMMENT '锁定股数' after `amount`;
alter table locked_asset add column `type` varchar(20) DEFAULT NULL COMMENT '锁定类型' after `trade_time`;
alter table locked_asset add column `seg_type` varchar(20) DEFAULT NULL COMMENT 'segType' after `trade_time`;
alter table locked_asset add column `contract_id` bigint(20) DEFAULT NULL COMMENT '合约id' after `status`;
alter table locked_asset add column `source` varchar(20) NOT NULL DEFAULT 'RSP' COMMENT '订单来源 DRIP/RSP' after `status`;
-- 默认值修改
alter table locked_asset modify column `amount` double DEFAULT NULL COMMENT '锁定金额';
-- 索引
ALTER TABLE locked_asset DROP INDEX user_order_id_UNIQUE;
ALTER TABLE locked_asset ADD UNIQUE KEY order_step_UNIQUE (`user_order_id`, `lock_step`);

--- aggregation_order
-- 字段变更
alter table aggregation_order add column `trade_quantity` double DEFAULT NULL COMMENT '交易股数' after `trade_amount`;
alter table aggregation_order add column `source` varchar(20) NOT NULL DEFAULT 'RSP' COMMENT '订单来源 DRIP/RSP' after `status`;
-- 默认值
alter table aggregation_order modify column `trade_amount` double DEFAULT NULL COMMENT '交易金额';

--- aggregation_execution_order
-- 字段变更
alter table aggregation_execution_order add column `trade_amount` double DEFAULT NULL COMMENT '下单金额' after `total_quantity`;
alter table aggregation_execution_order add column `filled_amount` double DEFAULT NULL COMMENT '成交金额' after `filled_quantity`;
alter table aggregation_execution_order add column `source` varchar(20) NOT NULL DEFAULT 'RSP' COMMENT '订单来源 DRIP/RSP' after `status`;
-- 修改字段长度
alter table aggregation_execution_order modify column `error_msg` varchar(300) DEFAULT NULL COMMENT '错误信息';

--- aggregation_execution_order_record
-- 字段变更（默认值）
alter table aggregation_execution_order_record modify column `amount` double DEFAULT NULL COMMENT '金额';
alter table aggregation_execution_order_record modify column `ref_price` double DEFAULT NULL COMMENT '参考价格';
alter table aggregation_execution_order_record modify column `quantity` double DEFAULT NULL COMMENT '数量';
alter table aggregation_execution_order_record modify column `lot_size_unit` int(11) DEFAULT NULL COMMENT '每手股数';

--- trade_allocation
-- 字段变更
alter table trade_allocation add column `source` varchar(20) NOT NULL DEFAULT 'RSP' COMMENT '订单来源 DRIP/RSP' after `status`;

--- 洗数
-- update user_order set source =  'RSP' where source is null;
-- update locked_asset set source = 'RSP' where source is null;
-- update locked_asset set lock_step = 'PLACE_ORDER' where lock_step is null;
-- update aggregation_order set source =  'RSP' where source is null;
-- update aggregation_execution_order set source =  'RSP' where source is null;
-- update trade_allocation set source =  'RSP' where source is null;