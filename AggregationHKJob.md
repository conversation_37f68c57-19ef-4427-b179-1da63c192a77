```mermaid
graph TD
    A[Start AggregationHKJob] --> B{handle};
    B --> C{log.info("Start HK aggregation job.")};
    C --> D{aggregationService.processAggregation};
    D --> E{userOrderRepository.getUserOrders};
    E --> F{buildUserAggregationOrderList};
    F --> G{Filter processing aggregation orders};
    G --> H{log.info("no user order need to be aggregated...")};
    H --> I[Return];
    G --> J{aggregationRepository.insert};
    J --> K{Loop userAggregationOrders};
    K --> L{executionService.executeAggregationOrder};
    L --> M{execution aggregation order fail?};
    M -- Yes --> N{log.info("execution aggregation order fail...")};
    N --> O{updateAfterExecutionFail};
    O --> P{aggregationRepository.update};
    P --> Q{AggregationOrderStatus.REJECTED?};
    Q -- Yes --> R{lockedAssetService.unlockPending};
    R --> S[End Loop];
    Q -- No --> S;
    M -- No --> S;
    K --> T{log.info("Finished HK aggregation job.")};
    T --> U[End];
    B --> V{Exception?};
    V -- Yes --> W{log.error("AggregationHKJob {}")};
    W --> X{alertService.sendMsg};
    X --> U;
```

## AggregationHKJob Execution Flow

The `AggregationHKJob` is a scheduled task that runs at a specific time to aggregate user orders for the Hong Kong market.

### Key Steps:

1.  **Start Job**: The job starts, logging "Start HK aggregation job.".
2.  **Process Aggregation**: It calls `aggregationService.processAggregation` to handle the main aggregation logic.
3.  **Fetch User Orders**: Retrieves new user orders from the database.
4.  **Build Aggregation Orders**: Aggregates the user orders into a list of `UserAggregationOrderValObj`.
5.  **Filter Processing Orders**: Filters out any orders that are already being processed.
6.  **No Orders to Aggregate**: If there are no new orders, it logs "no user order need to be aggregated..." and the job finishes.
7.  **Insert Aggregation Orders**: Inserts the new aggregation orders into the database.
8.  **Execute Aggregation Orders**: Loops through the aggregation orders and executes them.
    *   **Execution Fails**: If an order execution fails, it logs the error, updates the order status, and if the order is rejected, it unlocks the user's assets.
9.  **Finish Job**: After processing all orders, it logs "Finished HK aggregation job.".
10. **Error Handling**: If any exception occurs during the process, it logs the error and sends an alert.

### Key Log Messages:

-   `log.info("Start HK aggregation job.");`
-   `log.info("no user order need to be aggregated: source-{} secType-{}", source, secType);`
-   `log.info("execution aggregation order fail: UserAggregationOrderId-{}, ErrorCode-{}", ...);`
-   `log.info("Finished HK aggregation job.");`
-   `log.error("AggregationHKJob {}", e.getMessage(), e);`
