#!/bin/bash

echo "$1取值s,r
        为空或s表示snapshot,如果pom是snapshot则直接发布，其他情况不发布。
        r表示release，如果pom是snapshot则发布当前release版本，如果pom是正式版本，则直接发布;如果$2参数不为空则优先
        使用指定的$2版本发布;最后升级到下一snapshot版本"

snapshot_suffix='-SNAPSHOT'
version_split='.'
project_version=$(mvn -q -N -Dexec.executable="echo"  -Dexec.args='${project.version}'  org.codehaus.mojo:exec-maven-plugin:1.3.1:exec)

# public_version:本次将要发布的版本，updated_version:本次发布后要升级到哪个版本
if [[ $1 == '' || $1 == 's' ]]; then
   if [[ $(echo $project_version | grep -i 'SNAPSHOT') != "" ]]; then
      public_version=$project_version
      updated_version=$project_version
   else
      echo "版本号不是snapshot，请确认pom中版本号"
      exit
   fi
else
   if [[ $2 != '' ]]; then
      public_version=$2
   else
      public_version=${project_version/$snapshot_suffix/''}
   fi
   version_arr=(${public_version//$version_split/' '})
   updated_version=${version_arr[0]}$version_split${version_arr[1]}$version_split$((${version_arr[2]}+1))$snapshot_suffix

fi

echo "即将发布版本号为:"$public_version",升级的版本号为:"$updated_version
mvn versions:set -DnewVersion=$public_version
echo "开始发包，请稍候..."

deploy_result=$(mvn clean deploy -U -Dmaven.test.skip=true | grep "BUILD FAILURE")

if [[ $deploy_result == '' ]]; then
   mvn versions:set -DnewVersion=$updated_version
   mvn versions:commit
   echo  $public_version"发布成功"
else
   mvn clean deploy -U -Dmaven.test.skip=true
   mvn versions:revert
   echo "发布失败"
fi