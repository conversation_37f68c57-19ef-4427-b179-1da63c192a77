<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tigerbrokers.oae.platform</groupId>
    <artifactId>oae-platform</artifactId>
    <packaging>pom</packaging>
    <version>1.0.5-SNAPSHOT</version>
    <modules>
        <module>oae-starter</module>
        <module>oae-api</module>
        <module>oae-trade</module>
        <module>oae-allocation</module>
        <module>oae-storage</module>
        <module>oae-facade</module>
        <module>oae-entity</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <maven.compile.source>1.8</maven.compile.source>
        <maven.compile.target>1.8</maven.compile.target>
        <maven.resources.plugin.version>2.5</maven.resources.plugin.version>
        <maven.source.plugin.version>2.4</maven.source.plugin.version>
        <maven.jar.plugin.version>2.6</maven.jar.plugin.version>
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven.deploy.plugin.version>2.8.2</maven.deploy.plugin.version>
        <maven.assembly.plugin.version>3.1.1</maven.assembly.plugin.version>
        <spring.boot.version>2.2.7.RELEASE</spring.boot.version>
        <dubbo.serialization.protobuf>2.7.13</dubbo.serialization.protobuf>
        <dubbo.version>3.1.3</dubbo.version>
        <nacos.client>2.0.3</nacos.client>
        <nacos.spring.context>1.1.1</nacos.spring.context>
        <tiger-schedule.version>2.1.7</tiger-schedule.version>
        <tiger-schedule-alarm.version>2.0.6</tiger-schedule-alarm.version>
        <mysql.connector.java.version>5.1.49</mysql.connector.java.version>
        <mybatis.spring.version>2.0.1</mybatis.spring.version>
        <mybatis.version>3.5.1</mybatis.version>
        <mybatis.generator.version>1.3.7</mybatis.generator.version>
        <mybatis.generator.lombok.plugin.version>1.0</mybatis.generator.lombok.plugin.version>
        <pagehelper.spring.version>1.2.5</pagehelper.spring.version>
        <pagehelper.version>5.3.1</pagehelper.version>
        <lombok.version>1.18.2</lombok.version>
        <log4j.version>1.2.16</log4j.version>
        <apollo.tiger.version>1.9.2.1</apollo.tiger.version>
        <commons.tools.version>1.0.3</commons.tools.version>
        <stock.quote.utils.version>8.0.1</stock.quote.utils.version>
        <stock.quote.rpc.version>9.0.2-rc</stock.quote.rpc.version>
        <commons.extra.version>1.0.4</commons.extra.version>
        <jetcache.starter.redis.version>2.7.4</jetcache.starter.redis.version>
        <alpha.commons.version>3.13.2</alpha.commons.version>
        <alpha.account.client.version>2.2.1</alpha.account.client.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <alpha.account.rich.version>2.0.10</alpha.account.rich.version>
        <asset-client.version>2.4.0</asset-client.version>
        <contract-client.version>3.17.17</contract-client.version>
        <contract-data-client.version>3.17.5</contract-data-client.version>
        <hermes.common.version>2.0.16</hermes.common.version>
        <alpha.model.version>0.0.2-SNAPSHOT</alpha.model.version>
        <zkclient.version>0.11</zkclient.version>
        <kafka.version>1.1.0</kafka.version>
        <kafka-clients.version>2.3.1</kafka-clients.version>
        <microservice-auth.version>1.1.4</microservice-auth.version>
        <microservice-apache-dubbo.version>1.0.2</microservice-apache-dubbo.version>
        <grpc-service-api.version>1.12.17</grpc-service-api.version>
        <protobuf-java.version>3.21.1</protobuf-java.version>
        <grpc.version>1.46.0</grpc.version>
        <apache.commons.lang3.version>3.12.0</apache.commons.lang3.version>
        <alpha.core.data.version>1.8.2</alpha.core.data.version>
        <ledger-client.version>3.8.4</ledger-client.version>
        <brokerage.sdk.version>2.4.7</brokerage.sdk.version>
        <alarm.client.version>1.0.1</alarm.client.version>
        <easyexcel.version>2.2.6</easyexcel.version>
        <!--<netty.all.version>4.1.38.Final</netty.all.version>-->
        <apache.thrift.libthrift.version>0.9.3</apache.thrift.libthrift.version>
        <grpc.client.spring.boot.starter.version>2.8.0.RELEASE</grpc.client.spring.boot.starter.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>
        <logstash.logback.encoder.version>6.0</logstash.logback.encoder.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--project-->
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-trade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-allocation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-storage</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.oae.platform</groupId>
                <artifactId>oae-entity</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.hermes</groupId>
                <artifactId>hermes-common</artifactId>
                <version>${hermes.common.version}</version>
            </dependency>

            <!--spring-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- dubbo 3.0 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-serialization-protobuf</artifactId>
                <version>${dubbo.serialization.protobuf}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Dubbo Registry Nacos -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>${nacos.spring.context}</version>
            </dependency>

            <!--schedule-->
            <dependency>
                <groupId>com.tigerbrokers.infra.schedule</groupId>
                <artifactId>tiger-schedule-spring-starter</artifactId>
                <version>${tiger-schedule.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.infra.schedule</groupId>
                <artifactId>tiger-schedule-core</artifactId>
                <version>${tiger-schedule.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>curator-client</artifactId>
                        <groupId>org.apache.curator</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.infra.schedule</groupId>
                <artifactId>tiger-schedule-alarm</artifactId>
                <version>${tiger-schedule-alarm.version}</version>
            </dependency>

            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.java.version}</version>
            </dependency>

            <!--mybatis-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.softwareloop</groupId>
                <artifactId>mybatis-generator-lombok-plugin</artifactId>
                <version>${mybatis.generator.lombok.plugin.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!--log-->
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!--apollo-->
            <dependency>
                <groupId>com.tigerbrokers.infra.config</groupId>
                <artifactId>tiger-apollo-client</artifactId>
                <version>${apollo.tiger.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.infra.config</groupId>
                <artifactId>apollo-openapi</artifactId>
                <version>${apollo.tiger.version}</version>
            </dependency>

            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!--tiger-->
            <dependency>
                <groupId>com.tigerbrokers.commons</groupId>
                <artifactId>storage</artifactId>
                <version>${commons.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers</groupId>
                <artifactId>stock-quote-utils</artifactId>
                <version>${stock.quote.utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.commons</groupId>
                <artifactId>extra</artifactId>
                <version>${commons.extra.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers</groupId>
                <artifactId>quote-rpc-api</artifactId>
                <version>${stock.quote.rpc.version}</version>
            </dependency>

            <!--jetcache-->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.starter.redis.version}</version>
            </dependency>

            <!--alpha-->
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>commons</artifactId>
                <version>${alpha.commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>asset-client</artifactId>
                <version>${asset-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.arakelian</groupId>
                        <artifactId>jackson-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-netflix-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-archaius</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-zookeeper-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-zookeeper-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.module</groupId>
                        <artifactId>jackson-module-scala_2.11</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.module</groupId>
                        <artifactId>jackson-module-paranamer</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.module</groupId>
                        <artifactId>jackson-module-paranamer-names</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>contract-client</artifactId>
                <version>${contract-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>contract-advanced-data-client</artifactId>
                <version>${contract-data-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>contract-advanced-data</artifactId>
                <version>${contract-data-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>contract-data</artifactId>
                <version>${contract-data-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>ledger-client</artifactId>
                <version>${ledger-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>core-data</artifactId>
                <version>${alpha.core.data.version}</version>
            </dependency>

            <!--zk-->
            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_2.12</artifactId>
                <version>${kafka.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.101tec</groupId>
                        <artifactId>zkclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tigerbrokers.alpha</groupId>
                <artifactId>account-rich-client</artifactId>
                <version>${alpha.account.client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.devh</groupId>
                <artifactId>grpc-client-spring-boot-starter</artifactId>
                <version>${grpc.client.spring.boot.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>4.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>5.1.0</version>
            </dependency>
            <!--openapi-rpc-->
            <dependency>
                <groupId>com.tigerbrokers.brokerage.sdk</groupId>
                <artifactId>brokerage-trade</artifactId>
                <version>${brokerage.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.framework.apollo</groupId>
                        <artifactId>apollo-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ctrip.framework.apollo</groupId>
                        <artifactId>apollo-openapi</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.brokerage.sdk</groupId>
                <artifactId>brokerage-common</artifactId>
                <version>${brokerage.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.framework.apollo</groupId>
                        <artifactId>apollo-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ctrip.framework.apollo</groupId>
                        <artifactId>apollo-openapi</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- rpc服务鉴权 -->
            <dependency>
                <groupId>com.tigerbrokers.ams</groupId>
                <artifactId>microservice-auth</artifactId>
                <version>${microservice-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.ams</groupId>
                <artifactId>microservice-apache-dubbo</artifactId>
                <version>${microservice-apache-dubbo.version}</version>
            </dependency>
            <!-- rpc服务鉴权scope自动注册 -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.ams</groupId>
                <artifactId>grpc-service-api</artifactId>
                <version>${grpc-service-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>

            <!--alarm-->
            <dependency>
                <groupId>com.tigerbrokers.infra</groupId>
                <artifactId>alarm-client</artifactId>
                <version>${alarm.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tigerbrokers.infra</groupId>
                <artifactId>alarm-spring-boot-starter</artifactId>
                <version>${alarm.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache.commons.lang3.version}</version>
            </dependency>

            <!--excel-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>${apache.thrift.libthrift.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>${guava-retrying.version}</version>
            </dependency>

            <!-- logback json -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.logback.encoder.version}</version>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.40.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <conf-dir>local</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <conf-dir>test</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>qa</id>
            <properties>
                <conf-dir>qa</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online</id>
            <properties>
                <conf-dir>online</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online-tbhk</id>
            <properties>
                <conf-dir>online-tbhk</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online-tbau</id>
            <properties>
                <conf-dir>online-tbau</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online-tbms</id>
            <properties>
                <conf-dir>online-tbms</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online-tbsg</id>
            <properties>
                <conf-dir>online-tbsg</conf-dir>
            </properties>
        </profile>
        <profile>
            <id>online-tfnz</id>
            <properties>
                <conf-dir>online-tfnz</conf-dir>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven.jar.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compile.source}</source>
                    <target>${maven.compile.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven.resources.plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven.deploy.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>com.googlecode.maven-download-plugin</groupId>
                <artifactId>download-maven-plugin</artifactId>
                <version>1.9.0</version>
                <executions>
                    <execution>
                        <id>download-starlink-agent</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>wget</goal>
                        </goals>
                        <configuration>
                            <url>https://nexus.tigerbrokers.net/repository/maven-central/io/opentelemetry/javaagent/opentelemetry-javaagent/2.6.0/opentelemetry-javaagent-2.6.0.jar</url>
                            <unpack>false</unpack>
                            <outputDirectory>${project.build.directory}/starlink</outputDirectory>
                            <md5>ea927293754fc7b53fc78ae84157e1e7</md5>
                        </configuration>
                    </execution>
                    <execution>
                        <id>download-starlink-ext</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>wget</goal>
                        </goals>
                        <configuration>
                            <url>https://nexus.tigerbrokers.net/repository/maven-releases/com/tigerbrokers/infrastructure/tigerbrokers-opentelemetry-agent-extension/1.0.0/tigerbrokers-opentelemetry-agent-extension-1.0.0.jar</url>
                            <unpack>false</unpack>
                            <outputDirectory>${project.build.directory}/starlink/ext</outputDirectory>
                            <md5>589b3da5d678792765205c2c65fc0deb</md5>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.11.0.3922</version>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>tiger-oss</id>
            <url>http://nexus.tigerbrokers.net/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>tiger-oss</id>
            <url>http://nexus.tigerbrokers.net/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus-snapshot</id>
            <url>http://nexus.tigerbrokers.net/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>nexus-inner</id>
            <url>http://alpha-repo.tigerbrokers.net/nexus/repository/maven-public/</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus-inner</id>
            <url>http://alpha-repo.tigerbrokers.net/nexus/repository/maven-public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </pluginRepository>
        <pluginRepository>
            <id>nexus-snapshot</id>
            <url>http://nexus.tigerbrokers.net/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>
</project>