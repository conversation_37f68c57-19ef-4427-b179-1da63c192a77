#!/bin/sh
set -e

NAME=$1
if [ "$NAME" == "" ]; then
    echo "Packaging failed, name not specified"
    exit 1
fi

## 这里默认 使用 这个仓库 registry-gz.tigerbrokers.net/platform
REGISTRY=${IMAGE_PREFIX}
if [ "$REGISTRY" == "" ]; then
    echo "use shell gen REGISTRY info"
    REGISTRY="aliheyuan-registry-vpc.cn-heyuan.cr.aliyuncs.com/platform"
fi

echo " REGISTRY is  $REGISTRY "

if ! [ -f "Dockerfile" ]; then
    echo ""
    echo "Packaging failed, no Dockerfile ($NAME)"
    echo ""
    exit 1
fi
BRANCH=$(git rev-parse --abbrev-ref HEAD)
COMMIT=$(git log -n 1 --pretty=format:"%H")
if [ $BRANCH == "HEAD" ]; then
    ## 如果不能直接获取到当前所在 branch，需要传入环境变量 CURRENT_BRANCH
    BRANCH=$CURRENT_BRANCH
fi
if [ "$BRANCH" == "" ]; then
    echo "cannot get current branch name, please checkout a branch or specify env CURRENT_BRANCH"
    exit 1
fi
if echo "$BRANCH" | grep -v -Eq '^[-._a-zA-Z0-9]+$'; then
    echo "invalid branch name (only [a-zA-Z0-9._-] allowed"
    exit 1
fi
TAG=$BRANCH-$COMMIT
TAG_LATEST=$BRANCH-latest

isProductFlag=no
if [ $isProductFlag = "yes" ] ; then
   # 正式环境
   echo " prod env "
   BRANCH=master
   docker login -u wings_push -p 2Lvqi6ycJTtPm9wfREyUDwQS3e6ig9Iw registry-bj.tigerbrokers.net
   REGISTRY=${DOCKER_REGISTRY:-"registry-bj.tigerbrokers.net/platform"}
else
   # 测试环境
   echo " test env "
   docker login -u wings_push -p 2Lvqi6ycJTtPm9wfREyUDwQS3e6ig9Iw registry-gz.tigerbrokers.net

fi


if [ "$REGISTRY" == "" ]; then
    echo "----------------------"
    echo " build $NAME docker image ($NAME:$TAG), won't push (registry is empty)"
    echo "----------------------"
    docker build -t $NAME:$TAG_LATEST -t $NAME:$TAG .
else
    echo "----------------------"
    echo " build and push $NAME docker image ($REGISTRY/$NAME:$TAG)"
    echo "----------------------"
    docker build -t $REGISTRY/$NAME:$TAG_LATEST -t $REGISTRY/$NAME:$TAG .
    docker push $REGISTRY/$NAME:$TAG
    docker push $REGISTRY/$NAME:$TAG_LATEST
fi
