package com.tigerbrokers.oae.facade.context.placeOrder;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Data
@Builder
public class PlaceOrderFacadeContext {

    private String account;

    private String symbol;

    private String currency;

    private String secType;

    private String segType;

    private String externalId;

    private String orderType;

    private String action;

    private Double amount;

    private Double price;

    private Double quantity;

    private String timeInForce;

    private List<String> attrs;

    private BusinessSource source;

    private String tradingSessionType;
}
