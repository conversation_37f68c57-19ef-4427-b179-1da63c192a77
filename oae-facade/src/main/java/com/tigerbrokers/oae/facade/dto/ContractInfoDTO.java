package com.tigerbrokers.oae.facade.dto;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Data
public class ContractInfoDTO implements Serializable {

    private static final long serialVersionUID = -2463168716693488520L;

    private Long contractId;

    private Double tickerSize;

    private Integer lotSize;

    private String market;

    private String secType;

    private int shareScale = 0;

    public Boolean valid() {
        if (SecType.FUND.name().equals(secType)) {
            return true;
        }
        return tickerSize != null && lotSize != null;
    }
}
