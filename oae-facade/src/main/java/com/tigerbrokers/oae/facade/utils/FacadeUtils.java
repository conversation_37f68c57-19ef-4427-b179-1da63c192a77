package com.tigerbrokers.oae.facade.utils;

import com.tigerbrokers.alpha.finagle.client.RetryBudgetParams;
import com.tigerbrokers.alpha.model.ledger.AssetFundSubType;
import com.tigerbrokers.ams.common.Market;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;

public class FacadeUtils {

    public static final RetryBudgetParams DEFAULT_RETRY_BUDGET_PARAMS = getDefaultRetryBudgetParams();

    public static String getAssetFundSubType(BusinessSource businessSource) {
        switch (businessSource) {
            case RSP:
                return AssetFundSubType.RSP.name();
            case DRIP:
                return AssetFundSubType.DRIP.name();
            default:
                return "";
        }
    }

    public static RetryBudgetParams getDefaultRetryBudgetParams() {
        RetryBudgetParams retryBudgetParams = new RetryBudgetParams();
        retryBudgetParams.setTtlSeconds(10);
        retryBudgetParams.setMinRetriesPerSec(100);
        retryBudgetParams.setPercentCanRetry(0.1);
        return retryBudgetParams;
    }

    public static Boolean isUSDrip(String source, String secType, String market) {
        return BusinessSource.DRIP.name().equals(source) && SecType.STK.name().equals(secType)
                && Market.US.name().equals(market);
    }

}
