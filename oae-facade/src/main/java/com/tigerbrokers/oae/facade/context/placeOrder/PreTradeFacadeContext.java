package com.tigerbrokers.oae.facade.context.placeOrder;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
@Builder
public class PreTradeFacadeContext {

    private String orderReqType;

    private String account;

    private String symbol;

    private String currency;

    private String secType;

    private String segType;

    private String externalId;

    private String orderType;

    private String action;

    private Double amount;

    private String timeInForce;

    private Double quantity;

    private List<String> orderAttrs;

    private BusinessSource source;

    private Long portfolioId;

}
