package com.tigerbrokers.oae.facade.config;

import com.ctrip.framework.apollo.spring.annotation.TigerConfigBean;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @author: chentao
 * Created on 28/1/2023
 */
public class FacadeConfig {

    private static final String FACADE_CONFIG_NAMESPACE = "FacadeConfig";

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "account.client.rpc", injectEnvVars = true)
    @Component(value = "accountClientRpcConfig")
    @Data
    public static class AccountClientRpcConfig {
        private String label;
        private Long requestTimeout;
        private String zkAddress;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "prime.asset.client.rpc", injectEnvVars = true)
    @Data
    public static class PrimeAssetClientRpcConfig {
        private String label;
        private String zkAddress;
        private Long requestTimeout;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "prime.order.rpc", injectEnvVars = true)
    @Data
    public static class PrimeOrderRpcConfig {
        private String label;
        private String zkAddress;
        private Long requestTimeout;
    }

    @Data
    public static class PrimeConfig {
        protected String apiKey;
        protected String clientId;
        protected String privateKeyPath;
        protected String privateKey;
        protected String language;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "prime.rsp")
    @Data
    public static class PrimeRspConfig extends PrimeConfig {
        protected String apiKey;
        protected String clientId;
        protected String privateKeyPath;
        protected String privateKey;
        protected String language;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "prime.drip")
    @Data
    public static class PrimeDripConfig extends PrimeConfig {
        protected String apiKey;
        protected String clientId;
        protected String privateKeyPath;
        protected String privateKey;
        protected String language;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "contract.client.rpc", injectEnvVars = true)
    @Data
    public static class PrimeContractClientRpcConfig {
        private String label;
        private Long requestTimeout;
        private String zkAddress;
        private String contractServer;
        private String calendarServer;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "contract.data.client.rpc", injectEnvVars = true)
    @Data
    public static class PrimeContractDataClientRpcConfig {
        private String label;
        private Long requestTimeout;
        private String zkAddress;
        private String tradingRestrictionServer;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "ledger.client.rpc", injectEnvVars = true)
    @Data
    public static class PrimeLedgerClientRpcConfig {
        private String label;
        private String zkAddress;
        private Long requestTimeout;
    }

    @Component(value = "primeTradeKafkaConfig")
    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "prime.trade.kafka")
    @Data
    public static class PrimeTradeKafkaConfig {
        private String bootstrapServers;
        private String group;
        private String tradeTopic;
        private Integer concurrency;
        private Boolean autoStartup;
        private String dataFilterMarket;
        private String tradeMsgType;
    }

    @Component(value = "allocationReportFtpConfig")
    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "allocation.report.ftp")
    @Data
    public static class AllocationReportFtpConfig {
        private String host;
        private Integer port;
        private String username;
        private String password;
        private String allocationPath;
    }

    @TigerConfigBean(namespace = FACADE_CONFIG_NAMESPACE, prefix = "order.client.rpc", injectEnvVars = true)
    @Data
    public static class PrimeOrderClientRpcConfig {
        private String label;
        private String zkAddress;
        private Long requestTimeout;
    }
}
