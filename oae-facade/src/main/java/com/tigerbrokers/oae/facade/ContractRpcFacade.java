package com.tigerbrokers.oae.facade;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.tigerbrokers.alpha.commons.utils.Json;
import com.tigerbrokers.alpha.contract.advanced.data.fund.FundTradingRestriction;
import com.tigerbrokers.alpha.contract.advanced.thrift.model.RpcMergedTradingRestriction;
import com.tigerbrokers.alpha.contract.advanced.thrift.service.TradingRestrictionService;
import com.tigerbrokers.alpha.contract.client.util.ContractConverter;
import com.tigerbrokers.alpha.contract.client.util.TickSizeUtils;
import com.tigerbrokers.alpha.contract.thrift.model.RpcContract;
import com.tigerbrokers.alpha.contract.thrift.service.ContractService;
import com.tigerbrokers.alpha.core.data.contract.Contract;
import com.tigerbrokers.alpha.finagle.client.FinagleClientBuilder;
import com.tigerbrokers.alpha.finagle.client.FinagleClientConfig;
import com.tigerbrokers.ams.common.Market;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.facade.context.ContractFacadeContext;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.twitter.util.Future;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class ContractRpcFacade {

    private FacadeConfig.PrimeContractClientRpcConfig primeContractClientRpcConfig;

    private FacadeConfig.PrimeContractDataClientRpcConfig primeContractDataClientRpcConfig;

    private ContractService.FutureIface contractService;

    private TradingRestrictionService.FutureIface tradingRestrictionService;

    private LoadingCache<String, Contract> contractCache;

    private LoadingCache<Long, Integer> sharesPrecisionCache;


    @Autowired
    public ContractRpcFacade(FacadeConfig.PrimeContractClientRpcConfig primeContractClientRpcConfig,
                             FacadeConfig.PrimeContractDataClientRpcConfig primeContractDataClientRpcConfig) {
        this.primeContractClientRpcConfig = primeContractClientRpcConfig;
        this.primeContractDataClientRpcConfig = primeContractDataClientRpcConfig;
        init();
    }

    private void init() {
        FinagleClientConfig contractClientConfig = new FinagleClientConfig();
        contractClientConfig.setServerAddr(primeContractClientRpcConfig.getZkAddress() + primeContractClientRpcConfig.getContractServer());
        contractClientConfig.setRequestTimeoutMillis(primeContractClientRpcConfig.getRequestTimeout());
        contractClientConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        contractService = FinagleClientBuilder.build(primeContractClientRpcConfig.getLabel(),
                ContractService.FutureIface.class, contractClientConfig);

        FinagleClientConfig tradingRestrictionConfig = new FinagleClientConfig();
        tradingRestrictionConfig.setServerAddr(primeContractDataClientRpcConfig.getZkAddress() + primeContractDataClientRpcConfig.getTradingRestrictionServer());
        tradingRestrictionConfig.setRequestTimeoutMillis(primeContractDataClientRpcConfig.getRequestTimeout());
        tradingRestrictionConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        tradingRestrictionService = FinagleClientBuilder.build(primeContractDataClientRpcConfig.getLabel(),
                TradingRestrictionService.FutureIface.class, tradingRestrictionConfig);

        this.contractCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).build(
                new CacheLoader<String, Contract>() {
                    @Override
                    public Contract load(String symbol) throws Exception {
                        return queryContract(symbol);
                    }
                }
        );

        this.sharesPrecisionCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES).build(
                new CacheLoader<Long, Integer>() {
                    @Override
                    public Integer load(Long contractId) throws Exception {
                        return getFractionShareScaleForFund(contractId);
                    }
                }
        );
    }

    public ContractInfoDTO queryContractInfo(ContractFacadeContext context) {
        try {
            Contract contract = contractCache.get(context.getSymbol());
            if (Objects.nonNull(contract)) {
                int shareScale = 0;
                if (SecType.FUND.name().equals(contract.getSecType())) {
                    shareScale = sharesPrecisionCache.getUnchecked(contract.getId());
                }
                if (SecType.STK.name().equals(contract.getSecType()) && Market.US.name().equals(contract.getMarket())) {
                    shareScale = 5;
                }
                return buildContractInfo(contract, context.getPrice(), shareScale);
            }
        } catch (Exception e) {
            log.error("queryContractInfo {}", e.getMessage(), e);
        }

        return null;
    }

    private int getFractionShareScaleForFund(Long contractId) {
        try {
            RpcMergedTradingRestriction tradingRestriction = tradingRestrictionService.getMergedTradingRestrictionByContractId(contractId).apply();
            if (tradingRestriction != null && StringUtils.isNotEmpty(tradingRestriction.getExtendFeatures())) {
                FundTradingRestriction fundTradingRestriction = Json.as(tradingRestriction.getExtendFeatures(), FundTradingRestriction.class);
                return fundTradingRestriction.getSharesPrecision();
            } else {
                throw new RuntimeException("getFractionShareScaleForFund null, tradingRestriction:" + tradingRestriction);
            }
        } catch (Exception e) {
            log.error("getFractionShareScaleForFund error", e);
            throw new RuntimeException("getFractionShareScaleForFund error, contractId:" + contractId);
        }
    }

    public Contract queryContract(String symbol) {
        try {
            Future<RpcContract> rpcContractFuture = contractService.getBySymbol(symbol);
            RpcContract rpcContract = rpcContractFuture.toJavaFuture().get();
            return ContractConverter.convertToStock(rpcContract);
        } catch (Exception e) {
            log.error("queryContract {}", e.getMessage(), e);
        }

        return new Contract();
    }

    private ContractInfoDTO buildContractInfo(Contract contract, BigDecimal price, int shareScale) {
        ContractInfoDTO result = new ContractInfoDTO();
        if (Objects.nonNull(contract)) {
            result.setContractId(contract.getId());
            if (contract.getLotSize() != null) {
                Integer lotSize = contract.getLotSize().intValue();
                result.setLotSize(lotSize);
            }
            result.setMarket(contract.getMarket());
            result.setSecType(contract.getSecType());
            if (Objects.nonNull(price)) {
                BigDecimal tickSize = TickSizeUtils.getTickSize(contract, price);
                result.setTickerSize(tickSize.doubleValue());
            }
            result.setShareScale(shareScale);
        }
        return result;
    }
}
