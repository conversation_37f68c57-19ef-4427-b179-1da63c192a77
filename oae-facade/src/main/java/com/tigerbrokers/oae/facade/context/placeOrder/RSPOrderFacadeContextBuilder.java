package com.tigerbrokers.oae.facade.context.placeOrder;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.OrderAttr;
import com.tigerbrokers.brokerage.sdk.common.enums.TimeInForce;

import java.util.List;

public class RSPOrderFacadeContextBuilder extends OrderFacadeContextBuilder {

    public static final List<String> ORDER_ATTRS_SUPER_ORDER =
            Lists.newArrayList(OrderAttr.SUPER_ORDER.name(), "POST_TRADE_ALLOCATION");

    public static final List<String> ORDER_ATTRS_SUB_ORDER = Lists.newArrayList(OrderAttr.SUB_ORDER.name(),
            OrderAttr.REGULAR_SAVINGS_PLAN.name(), "FORBIDDEN_MARGIN", OrderAttr.MONETARY.name());

    public static final List<String> ORDER_ATTRS_SUB_ORDER_MARGIN = Lists.newArrayList(OrderAttr.SUB_ORDER.name(),
            OrderAttr.REGULAR_SAVINGS_PLAN.name(), OrderAttr.MONETARY.name());


    @Override
    protected List<String> buildOrderAttrs(OrderOperation orderOperation) {
        switch (orderOperation) {
            case PRE_TRADE:
                if (isApplyMargin()) {
                    return ORDER_ATTRS_SUB_ORDER_MARGIN;
                } else {
                    return ORDER_ATTRS_SUB_ORDER;
                }
            case PLACE_ORDER:
                return ORDER_ATTRS_SUPER_ORDER;
            default:
                return null;
        }
    }

    @Override
    protected String timeInForce() {
        return TimeInForce.DAY.name();
    }
}
