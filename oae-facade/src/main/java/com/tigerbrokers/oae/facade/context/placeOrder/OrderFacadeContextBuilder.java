package com.tigerbrokers.oae.facade.context.placeOrder;

import com.tigerbrokers.brokerage.sdk.common.enums.OrderType;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.consts.OrderConst;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

public abstract class OrderFacadeContextBuilder {

    private static final Long NORMAL_NO_PORTFOLIO_ID = -1L;
    private static final Long PRE_TRADE_NO_PORTFOLIO_ID = 0L;

    enum OrderOperation{
        PRE_TRADE,
        PLACE_ORDER;
    }

    private UserOrder userOrder;

    public static OrderFacadeContextBuilder orderContextBuilder(String source, String market, String secType) {
        BusinessSource businessSource = BusinessSource.valueOf(source);
        switch (businessSource) {
            case DRIP:
                if (SecType.FUND.name().equals(secType)) {
                    return new DRIPOrderFacadeContextBuilder();
                } else if (FacadeUtils.isUSDrip(source, secType, market)) {
                    return new USDRIPOrderFacadeContextBuilder();
                }
            case RSP:
                return new RSPOrderFacadeContextBuilder();
            default:
                return null;
        }
    }

    public PreTradeFacadeContext createPreTradeFacadeContext(UserOrder userOrder) {
        this.userOrder = userOrder;
        return PreTradeFacadeContext.builder()
                .orderReqType(OrderConst.PLACE_REQ_TYPE)
                .account(userOrder.getAccount())
                .symbol(userOrder.getSymbol())
                .currency(userOrder.getCurrency())
                .segType(userOrder.getSegType())
                .secType(userOrder.getSecType())
                .externalId(String.valueOf(userOrder.getId()))
                .orderType(OrderType.MKT.name())
                .action(userOrder.getAction())
                .amount(userOrder.getTradeAmount())
                .quantity(userOrder.getQuantity())
                .timeInForce(timeInForce())
                .orderAttrs(buildOrderAttrs(OrderOperation.PRE_TRADE))
                .source(BusinessSource.valueOf(userOrder.getSource()))
                .portfolioId(NORMAL_NO_PORTFOLIO_ID.equals(userOrder.getPortfolioId()) ? PRE_TRADE_NO_PORTFOLIO_ID : userOrder.getPortfolioId())
                .build();
    }

    public PlaceOrderFacadeContext create(AggregationExecutionOrder executionOrder) {
        return PlaceOrderFacadeContext.builder()
                .account(executionOrder.getDealAccount())
                .symbol(executionOrder.getSymbol())
                .currency(executionOrder.getCurrency())
                .secType(executionOrder.getSecType())
                .segType(executionOrder.getSegType())
                .externalId(String.valueOf(executionOrder.getId()))
                .orderType(executionOrder.getOrderType())
                .timeInForce(timeInForce())
                .price(executionOrder.getPrice())
                .amount(executionOrder.getTradeAmount())
                .quantity(executionOrder.getTotalQuantity())
                .action(executionOrder.getAction())
                .attrs(buildOrderAttrs(OrderOperation.PLACE_ORDER))
                .source(BusinessSource.valueOf(executionOrder.getSource()))
                .tradingSessionType(tradingSessionType())
                .build();
    }

    protected String tradingSessionType() {
        return null;
    }

    public PlaceOrderFacadeContext createOdd(AggregationExecutionOrder executionOrder) {
        return PlaceOrderFacadeContext.builder()
                .account(executionOrder.getDealAccount())
                .symbol(executionOrder.getSymbol())
                .currency(executionOrder.getCurrency())
                .externalId(String.valueOf(executionOrder.getId()))
                .orderType(OrderConst.ORDER_TYPE_MKT)
                .price(executionOrder.getPrice())
                .quantity(executionOrder.getTotalQuantity())
                .action(executionOrder.getAction())
                .attrs(buildOrderAttrs(OrderOperation.PLACE_ORDER))
                .source(BusinessSource.valueOf(executionOrder.getSource()))
                .build();
    }

    public boolean isApplyMargin() {
        return BooleanUtils.isTrue(userOrder.getApplyMargin());
    }

    protected abstract List<String> buildOrderAttrs(OrderOperation orderOperation);

    protected abstract String timeInForce();

}
