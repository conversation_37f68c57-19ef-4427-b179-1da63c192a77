package com.tigerbrokers.oae.facade.context.placeOrder;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.OrderAttr;
import com.tigerbrokers.brokerage.sdk.common.enums.TimeInForce;
import com.tigerbrokers.brokerage.sdk.common.enums.TradingSessionType;

import java.util.List;

public class USDRIPOrderFacadeContextBuilder extends OrderFacadeContextBuilder {

    public static final List<String> STK_DIVIDEND_SUB_ATTRS =
            Lists.newArrayList(OrderAttr.SUB_ORDER.name(), "DRIP");

    public static final List<String> STK_DIVIDEND_SUPER_ATTRS = Lists.newArrayList(
            OrderAttr.SUPER_ORDER.name(), "POST_TRADE_ALLOCATION");




    @Override
    protected List<String> buildOrderAttrs(OrderOperation orderOperation) {
        switch (orderOperation) {
            case PRE_TRADE:
                return STK_DIVIDEND_SUB_ATTRS;
            case PLACE_ORDER:
                return STK_DIVIDEND_SUPER_ATTRS;
            default:
                return null;
        }

    }

    @Override
    protected String timeInForce() {
        return TimeInForce.DAY.name();
    }

    @Override
    protected String tradingSessionType() {
        return TradingSessionType.RTH.name();
    }
}
