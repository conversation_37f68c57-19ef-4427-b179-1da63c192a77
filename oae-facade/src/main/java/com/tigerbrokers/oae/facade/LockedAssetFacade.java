package com.tigerbrokers.oae.facade;

import com.google.common.collect.Lists;
import com.tigerbrokers.alpha.asset.client.thrift.*;
import com.tigerbrokers.alpha.finagle.client.FinagleClientBuilder;
import com.tigerbrokers.alpha.finagle.client.FinagleClientConfig;
import com.tigerbrokers.alpha.order.client.thrift.*;
import com.tigerbrokers.brokerage.sdk.trade.biz.OrderBiz;
import com.tigerbrokers.alpha.model.ledger.BookkeepingType;
import com.tigerbrokers.oae.entity.consts.UpdateAssetType;
import com.tigerbrokers.oae.entity.util.AmountUtil;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.facade.context.CalculateLockAssetContext;
import com.tigerbrokers.oae.facade.context.LockedAssetFacadeContext;
import com.tigerbrokers.oae.facade.dto.CalLockInfoDTO;
import com.tigerbrokers.oae.facade.dto.LockedAssetFacadeDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Slf4j
@Service
public class LockedAssetFacade {

    private FacadeConfig.PrimeAssetClientRpcConfig rpcConfig;

    private RealtimeAssetService.FutureIface realtimeAssetRpcService;

    @Autowired
    private OrderBiz orderBiz;

    @Autowired
    public LockedAssetFacade(FacadeConfig.PrimeAssetClientRpcConfig rpcConfig) {
        this.rpcConfig = rpcConfig;
        init();
    }

    private void init() {
        FinagleClientConfig clientConfig = new FinagleClientConfig();
        clientConfig.setServerAddr(rpcConfig.getZkAddress());
        clientConfig.setRequestTimeoutMillis(rpcConfig.getRequestTimeout());
        realtimeAssetRpcService = FinagleClientBuilder.build(rpcConfig.getLabel(),
                RealtimeAssetService.FutureIface.class, clientConfig);
    }

    public LockedAssetFacadeDTO lockAsset(LockedAssetFacadeContext context) {
        try {
            AssetCompactJournal assetCompactJournal = buildAssetCompactJournal(context, UpdateAssetType.LOCK);
            log.info("lock asset: lockedRefId {}, param {}", context.getLockedRefId(), assetCompactJournal);
            realtimeAssetRpcService.update(assetCompactJournal).toJavaFuture().get();
            return LockedAssetFacadeDTO.builder().isSucc(true).build();
        } catch (Exception e) {
            log.error("lockAsset error, lockedRefId:{}, error:{}", context.getLockedRefId(), e.getMessage(), e);
        }
        return LockedAssetFacadeDTO.builder().isSucc(false).build();
    }

    public LockedAssetFacadeDTO lockPosition(LockedAssetFacadeContext context) {
        try {
            AssetCompactJournal assetCompactJournal = buildPositionCompactJournal(context, UpdateAssetType.LOCK);
            log.info("lock position: lockedRefId {}, param {}", context.getLockedRefId(), assetCompactJournal);
            realtimeAssetRpcService.update(assetCompactJournal).toJavaFuture().get();
            log.info("lock position success, lockedRefId:{}", context.getLockedRefId());
            return LockedAssetFacadeDTO.builder().isSucc(true).build();
        } catch (Exception e) {
            log.error("lockPosition error, lockedRefId:{}, error:{}", context.getLockedRefId(), e.getMessage(), e);
        }
        return LockedAssetFacadeDTO.builder().isSucc(false).build();

    }

    public LockedAssetFacadeDTO unlockAsset(LockedAssetFacadeContext context) {
        try {
            AssetCompactJournal assetCompactJournal = buildAssetCompactJournal(context, UpdateAssetType.UNLOCK);
            log.info("unlock asset: lockedRefId {}, param {}", context.getLockedRefId(), assetCompactJournal);
            realtimeAssetRpcService.update(assetCompactJournal).toJavaFuture().get();
            return LockedAssetFacadeDTO.builder().isSucc(true).build();
        } catch (Exception e) {
            log.error("unlockAsset error, lockedRefId:{}, error:{}", context.getLockedRefId(), e.getMessage(), e);
        }
        return LockedAssetFacadeDTO.builder().isSucc(false).build();
    }

    public LockedAssetFacadeDTO unlockPosition(LockedAssetFacadeContext context) {
        try {
            AssetCompactJournal assetCompactJournal = buildPositionCompactJournal(context, UpdateAssetType.UNLOCK);
            log.info("unlock position: lockedRefId {}, param {}", context.getLockedRefId(), assetCompactJournal);
            realtimeAssetRpcService.update(assetCompactJournal).toJavaFuture().get();
            return LockedAssetFacadeDTO.builder().isSucc(true).build();
        } catch (Exception e) {
            log.error("unlockPosition error, lockedRefId:{}, error:{}", context.getLockedRefId(), e.getMessage(), e);
        }
        return LockedAssetFacadeDTO.builder().isSucc(false).build();
    }

    /**
     * 融资-计算锁定资产
     */
    public CalLockInfoDTO calMarginLockInfo(CalculateLockAssetContext context) {
        TGCalculateLockedReq req = new TGCalculateLockedReq.Builder()
                .orderCreateList(buildTGOderCreate(context))
                .businessType(BusinessType.RSP)
                .build();
        CalLockInfoDTO.CalLockInfoDTOBuilder resultBuilder = CalLockInfoDTO.builder();
        try {
            TGCalculateLockedResp resp = orderBiz.calculateLocked(req);
            log.info("calMarginLockInfo req:{}, resp:{}", req, resp);
            return buildCalMarginLockInfo(resp, resultBuilder);
        } catch (Exception e) {
            log.error("calMarginLockInfo context:{}, fail:{}", context, e.getMessage(), e);
            return CalLockInfoDTO.builder().isSucc(false).errMsg(e.getMessage()).build();
        }
    }

    private AssetCompactJournal buildPositionCompactJournal(LockedAssetFacadeContext context,
                                                         UpdateAssetType updateAssetType) {
        List<LockedPositionJournal> positionJournals = buildPositionJournalList(context, updateAssetType);
        return new AssetCompactJournal.Builder().lockedPositionJournals(positionJournals).build();
    }

    private AssetCompactJournal buildAssetCompactJournal(LockedAssetFacadeContext context,
                                                         UpdateAssetType updateAssetType) {
        List<AssetJournal> assetJournalList = buildAssetJournalList(context, updateAssetType);
        return new AssetCompactJournal.Builder().assets(assetJournalList).build();
    }

    private List<LockedPositionJournal> buildPositionJournalList(LockedAssetFacadeContext context,
                                                           UpdateAssetType updateAssetType) {
        List<LockedPositionJournal> result = Lists.newArrayList();
        for (Map.Entry<BookkeepingType, Double> bookkeepingTypeValue : context.getBookkeepingTypeValue().entrySet()) {
            LockedPositionJournal.Builder positionJournalBuilder = new LockedPositionJournal.Builder()
                    .accountId(Long.parseLong(context.getAccount()))
                    .segType(context.getSegType())
                    .contractId(context.getContractId())
                    .type(bookkeepingTypeValue.getKey().getShortName())
                    .refId(context.getLockedRefId());
            if (context.getPortfolioId() != null) {
                positionJournalBuilder.portfolioId(context.getPortfolioId());
            }
            Double quantity = UpdateAssetType.LOCK.equals(updateAssetType) ? context.getQuantity() : 0.0;
            positionJournalBuilder.quantity(String.valueOf(quantity));
            result.add(positionJournalBuilder.build());
        }
        return result;
    }

    private List<AssetJournal> buildAssetJournalList(LockedAssetFacadeContext context,
                                                     UpdateAssetType updateAssetType) {
        List<AssetJournal> result = Lists.newArrayList();
        for (Map.Entry<BookkeepingType, Double> bookkeepingTypeValue : context.getBookkeepingTypeValue().entrySet()) {
            Double amount = bookkeepingTypeValue.getValue();
            AssetJournalKey.Builder builder = new AssetJournalKey.Builder()
                    .accountId(Long.parseLong(context.getAccount()))
                    .segType(context.getSegType())
                    .currency(context.getCurrency())
                    .bookkeepingType(bookkeepingTypeValue.getKey().getShortName())
                    .subType(FacadeUtils.getAssetFundSubType(context.getBusinessSource()))
                    .refId(context.getLockedRefId())
                    .businessDate(Integer.parseInt(com.tigerbrokers.stock.util.TimeUtils.printDateCN(context.getTradeTime().getTime())));
            if (context.getPortfolioId() != null) {
                builder.portfolioId(context.getPortfolioId());
            }
            Double updateAmount = UpdateAssetType.LOCK.equals(updateAssetType) ? amount : 0.0;
            AssetJournal assetJournal = new AssetJournal.Builder()
                    .amount(String.valueOf(AmountUtil.toCent(updateAmount)))
                    .key(builder.build())
                    .build();
            result.add(assetJournal);
        }
        return result;
    }

    private List<TGOrderCreate> buildTGOderCreate(CalculateLockAssetContext context) {
        TGOrderCreate item = new TGOrderCreate.Builder()
                .accountId(Long.parseLong(context.getAccount()))
                .action(context.getAction())
                .orderType(context.getOrderType())
                .secType(context.getSecType())
                .symbol(context.getSymbol())
                .externalId(context.getExternalId())
                .source(context.getSource())
                .appId(context.getAppId())
                .timeInForce(context.getTimeInForce())
                .cashAmount(context.getAmount())
                .quantity(context.getQuantity())
                .build();
        return Lists.newArrayList(item);
    }

    private CalLockInfoDTO buildCalMarginLockInfo(TGCalculateLockedResp resp,
                                                  CalLockInfoDTO.CalLockInfoDTOBuilder resultBuilder) {
        if ("ok".equalsIgnoreCase(resp.getStatus())) {
            List<CalLockInfoDTO.LockInfo> lockInfos = Lists.newArrayList();
            resp.getOrderLockInfos().get(0).getAssetLockInfos().forEach(orderLockInfo -> {
                CalLockInfoDTO.LockInfo lockInfo = CalLockInfoDTO.LockInfo.builder()
                        .segType(orderLockInfo.getSegType())
                        .currency(orderLockInfo.getCurrency())
                        .bookkeepingType(orderLockInfo.getBookkeepingType())
                        .amount(AmountUtil.fromCent(orderLockInfo.getAmmountCent()))
                        .daysToSettle(orderLockInfo.getDaysToSettle())
                        .build();
                lockInfos.add(lockInfo);
            });
            resultBuilder.isSucc(true).lockInfoList(lockInfos);
        } else {
            resultBuilder.isSucc(false).errMsg(resp.getErrorInfos().get(0).getMsg());
            log.error("calMarginLock fail: {}", resp.getErrorInfos().get(0));
        }
        return resultBuilder.build();
    }
}
