package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.alpha.ledger.thrift.entity.CrossAccountExtraInfo;
import com.tigerbrokers.alpha.ledger.thrift.entity.TransactionType;
import com.tigerbrokers.alpha.model.FundType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.util.RefIdUtil;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class CashTransactionFacadeContext {

    private String refId;

    private String account;

    private Double amount;

    private Long portfolioId;

    private Double quantity;

    private String currency;

    private String segType;

    private Date tradeTime;

    private String refType;

    private Long contractId;

    private Long userOrderId;

    private String symbol;

    private BusinessSource businessSource;

    private CrossAccountExtraInfo extraInfo;

    private TransactionType type;

    public static CashTransactionFacadeContext createDripCashContext(TradeAllocation tradeAllocation, UserOrder userOrder) {
        if (userOrder == null) {
            return null;
        }
        return CashTransactionFacadeContext.builder()
                .refId(RefIdUtil.getAllocationRefId(tradeAllocation.getRefId(), userOrder.getDividendId(), tradeAllocation.getType(), tradeAllocation.getSource()))
                .account(userOrder.getAccount())
                .amount(userOrder.getAmount())
                .userOrderId(userOrder.getId())
                .portfolioId(userOrder.getPortfolioId())
                .currency(userOrder.getCurrency())
                .segType(userOrder.getSegType())
                .symbol(userOrder.getSymbol())
                .tradeTime(userOrder.getTradeTime())
                .contractId(tradeAllocation.getContractId())
                .quantity(userOrder.getQuantity())
                .businessSource(BusinessSource.valueOf(userOrder.getSource()))
                .type(TransactionType.TRADE)
                .refType(FundType.DIVIDEND_REINVEST.name())
                .build();
    }
}
