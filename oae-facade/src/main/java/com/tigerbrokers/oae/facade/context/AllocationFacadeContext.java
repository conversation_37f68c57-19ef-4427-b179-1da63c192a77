package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.alpha.model.ledger.BookkeepingType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockType;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.entity.util.RefIdUtil;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Data
@Builder
public class AllocationFacadeContext {

    public static final String BREAK_REF_TYPE = "BREAK_ALLOCATION";

    private static final String BREAK_DESC = "Break fund due to ISP";

    // 调用后台账本分配接口传参RefId
    private String allocationRefId;

    private String sourceAccount;

    private String targetAccount;

    // break账户
    private Long breakGLAccountId;

    private Long portfolioId;

    private String segType;

    private Long contractId;

    private String market;

    private Double quantity;

    private Double filledPrice;

    private String currency;

    private Date transactTime;

    private Date businessDate;

    private Date settleDate;

    private Date navDate;

    private BusinessSource businessSource;

    private Date updateTime;

    //大订单id
    private Long superOrderId;

    //解锁用户下单时锁定的资金/持仓
    private LockedAssetFacadeContext unLockedContext;

    //DRIP BUY用户分配时持仓需要锁定，等待主单结算时解锁
    private LockedAssetFacadeContext settleLockedContext;

    //DRIP BUY需要新增一条流水，用于账单展示
    private CashTransactionFacadeContext dripCashContext;

    public static AllocationFacadeContext create(TradeAllocation tradeAllocation,
                                                 LockedAsset lockedAsset,
                                                 LockedAsset settleLockedAsset,
                                                 UserOrder userOrder,
                                                 List<AggregationExecutionOrder> executionOrderList,
                                                 CashTransactionFacadeContext cashContext) {
        BusinessSource businessSource = BusinessSource.valueOf(tradeAllocation.getSource());
        Long superOrderId = !CollectionUtils.isEmpty(executionOrderList) && !StringUtils.isEmpty(executionOrderList.get(0).getTradeId()) ? Long.parseLong(executionOrderList.get(0).getTradeId()) : null;
        AllocationFacadeContext allocationContext = AllocationFacadeContext.builder()
                // 调用后台账本分配接口传参RefId
                .allocationRefId(RefIdUtil.getAllocationRefId(tradeAllocation.getRefId(), userOrder != null ? userOrder.getDividendId() : null, tradeAllocation.getType(), tradeAllocation.getSource()))
                .sourceAccount(tradeAllocation.getSourceAccount())
                .targetAccount(tradeAllocation.getTargetAccount())
                .portfolioId(tradeAllocation.getPortfolioId())
                .segType(tradeAllocation.getSegType())
                .contractId(tradeAllocation.getContractId())
                .market(tradeAllocation.getMarket())
                .quantity(tradeAllocation.getFilledQuantity())
                .filledPrice(tradeAllocation.getAvgPrice())
                .currency(tradeAllocation.getCurrency())
                .transactTime(tradeAllocation.getFilledTime())
                .businessDate(tradeAllocation.getBusinessDate())
                .settleDate(tradeAllocation.getSettleDate())
                .navDate(tradeAllocation.getNavDate())
                .businessSource(businessSource)
                .updateTime(tradeAllocation.getUpdatedAt())
                .superOrderId(superOrderId)
                .build();

        // 用户账本解锁，回收账号break账
        LockedAssetFacadeContext lockedContext = null;
        if (TradeAllocationType.USER.name().equals(tradeAllocation.getType())) {
            if (lockedAsset != null) {
                lockedContext = LockedAssetFacadeContext.createUnlockedContext(lockedAsset);
                lockedContext.setRefType(FacadeUtils.getAssetFundSubType(businessSource));
            }
        } else if (TradeAllocationType.BREAK.name().equals(tradeAllocation.getType())) {
            String lockedRefId = RefIdUtil.getTraderRefId(tradeAllocation.getRefId(), tradeAllocation.getSource());
            Map<BookkeepingType, Double> breakBookkeepingTypeMap = new HashMap<>();
            breakBookkeepingTypeMap.put(BookkeepingType.TRADE, tradeAllocation.getAvgPrice());
            lockedContext = LockedAssetFacadeContext.builder()
                    .lockedRefId(lockedRefId)
                    .refType(BREAK_REF_TYPE)
                    .bookkeepingTypeValue(breakBookkeepingTypeMap)
                    .desc(BREAK_DESC)
                    .lockType(LockType.ASSET)
                    .build();
            allocationContext.setBreakGLAccountId(Long.parseLong(tradeAllocation.getTargetAccount()));
        }
        if (Objects.nonNull(lockedContext)) {
            allocationContext.setUnLockedContext(lockedContext);
        }

        LockedAssetFacadeContext settleLockedContext;
        if (settleLockedAsset != null) {
            settleLockedContext = LockedAssetFacadeContext.createLockedContext(settleLockedAsset);
            settleLockedContext.setRefType(FacadeUtils.getAssetFundSubType(businessSource));
            allocationContext.setSettleLockedContext(settleLockedContext);
        }

        if (cashContext != null) {
            allocationContext.setDripCashContext(cashContext);
        }
        return allocationContext;
    }


}
