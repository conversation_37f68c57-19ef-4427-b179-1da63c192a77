package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.alpha.model.ledger.BookkeepingType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockType;
import com.tigerbrokers.oae.entity.util.RefIdUtil;
import com.tigerbrokers.oae.facade.dto.CalLockInfoDTO;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
@Builder
public class LockedAssetFacadeContext {

    // 锁定资产传参RefId
    private String lockedRefId;

    private String account;

    private Double amount;

    private Long portfolioId;

    private Double quantity;

    private String currency;

    private String segType;

    private Date tradeTime;

    private String refType;

    private String desc;

    private Long contractId;

    private Map<BookkeepingType, Double> bookkeepingTypeValue;

    private BusinessSource businessSource;

    private LockType lockType;

    public static LockedAssetFacadeContext createLockedContext(LockedAsset lockedAsset) {
        return createLockedContextBuilder(lockedAsset)
                .bookkeepingTypeValue(buildBookkeepingTypeValue(lockedAsset))
                .build();
    }

    public static LockedAssetFacadeContext createUnlockedContext(LockedAsset lockedAsset) {
        return createLockedContextBuilder(lockedAsset)
                .bookkeepingTypeValue(buildUnlockBookkeepingTypeValue(lockedAsset))
                .build();
    }

    private static LockedAssetFacadeContext.LockedAssetFacadeContextBuilder createLockedContextBuilder(LockedAsset lockedAsset) {
        return LockedAssetFacadeContext.builder()
                .lockedRefId(RefIdUtil.getUserRefId(lockedAsset.getId(), lockedAsset.getSource()))
                .account(lockedAsset.getAccount())
                .amount(lockedAsset.getAmount())
                .portfolioId(lockedAsset.getPortfolioId())
                .currency(lockedAsset.getCurrency())
                .segType(lockedAsset.getSegType())
                .tradeTime(lockedAsset.getTradeTime())
                .contractId(lockedAsset.getContractId())
                .quantity(lockedAsset.getQuantity())
                .businessSource(BusinessSource.valueOf(lockedAsset.getSource()))
                .lockType(LockType.valueOf(lockedAsset.getType()));
    }

    private static Map<BookkeepingType, Double> buildBookkeepingTypeValue(LockedAsset lockedAsset) {
        LockType lockType = LockType.valueOf(lockedAsset.getType());
        Map<BookkeepingType, Double> result = new HashMap<>();
        switch (lockType) {
            case ASSET:
                result.putAll(getAssetBookkeepingTypeValue(true, lockedAsset));
                break;
            case POSITION:
                result.put(BookkeepingType.LOCKED_DRIP_POSITION, lockedAsset.getQuantity());
                break;
        }
        return result;
    }

    private static Map<BookkeepingType, Double> buildUnlockBookkeepingTypeValue(LockedAsset lockedAsset) {
        LockType lockType = LockType.valueOf(lockedAsset.getType());
        Map<BookkeepingType, Double> result = new HashMap<>();
        switch (lockType) {
            case ASSET:
                result.putAll(getAssetBookkeepingTypeValue(false, lockedAsset));
                break;
            case POSITION:
                result.put(BookkeepingType.LOCKED_DRIP_POSITION, 0.0);
                break;
        }
        return result;
    }

    private static Map<BookkeepingType, Double> getAssetBookkeepingTypeValue(boolean isLock, LockedAsset lockedAsset) {
        Map<BookkeepingType, Double> result = new HashMap<>();
        if (Strings.isBlank(lockedAsset.getDetail())) {
            result.put(BookkeepingType.AGGREGATE_LOCK_CASH, isLock ? lockedAsset.getAmount() : 0.0);
            result.put(BookkeepingType.AGGREGATE_LOCK_MARGIN, isLock ? lockedAsset.getAmount() : 0.0);
        } else {
            List<CalLockInfoDTO.LockInfo> lockInfoList = JacksonUtil.readValues(lockedAsset.getDetail(), CalLockInfoDTO.LockInfo.class);
            for (CalLockInfoDTO.LockInfo lockInfo : lockInfoList) {
                result.put(BookkeepingType.of(lockInfo.getBookkeepingType()), isLock ? lockInfo.getAmount() : 0.0);
            }
        }
        return result;
    }
}
