package com.tigerbrokers.oae.facade.dto;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
@Builder
public class AccountDTO {

    private String executionMaster;

    //交易执行账号
    private String dealAccountId;

    //碎股回收账号
    private String traderAccount;

    // GL account, 通用账本账号，用于 break 账
    private Long glAccountId;

    public boolean daAccountInvalid() {
        return StringUtils.isEmpty(dealAccountId);
    }
}
