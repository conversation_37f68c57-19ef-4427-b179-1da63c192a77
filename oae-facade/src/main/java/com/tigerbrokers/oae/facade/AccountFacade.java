package com.tigerbrokers.oae.facade;

import com.google.common.collect.Lists;
import com.tigerbrokers.alpha.account.management.thrift.*;
import com.tigerbrokers.alpha.finagle.client.FinagleClientBuilder;
import com.tigerbrokers.alpha.finagle.client.FinagleClientConfig;
import com.tigerbrokers.alpha.ledger.thrift.entity.ClearingAccountInfo;
import com.tigerbrokers.alpha.ledger.thrift.param.ClearingAccountQryParam;
import com.tigerbrokers.alpha.ledger.thrift.service.LedgerService;
import com.tigerbrokers.alpha.order.client.thrift.*;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.facade.context.QueryAccountFacadeContext;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.AccountDTO;
import com.tigerbrokers.oae.facade.dto.ExecutingBrokerDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.twitter.util.Future;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Slf4j
@Service
public class AccountFacade {

    private RpcAccountService.FutureIface rpcAccountService;

    private LedgerService.FutureIface ledgerService;

    private TGOrderService.FutureIface tgOrderService;

    private FacadeConfig.AccountClientRpcConfig accountClientRpcConfig;

    private FacadeConfig.PrimeLedgerClientRpcConfig primeLedgerClientRpcConfig;

    private FacadeConfig.PrimeOrderRpcConfig primeOrderRpcConfig;

    @Autowired
    AccountFacade(FacadeConfig.AccountClientRpcConfig accountClientRpcConfig,
                  FacadeConfig.PrimeLedgerClientRpcConfig primeLedgerClientRpcConfig,
                  FacadeConfig.PrimeOrderRpcConfig primeOrderRpcConfig) {
        this.accountClientRpcConfig = accountClientRpcConfig;
        this.primeLedgerClientRpcConfig = primeLedgerClientRpcConfig;
        this.primeOrderRpcConfig = primeOrderRpcConfig;
        init();
    }

    private void init() {
        FinagleClientConfig accountClientConfig = new FinagleClientConfig();
        accountClientConfig.setServerAddr(accountClientRpcConfig.getZkAddress());
        accountClientConfig.setRequestTimeoutMillis(accountClientRpcConfig.getRequestTimeout());
        accountClientConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        rpcAccountService = FinagleClientBuilder.build(
                accountClientRpcConfig.getLabel(),
                RpcAccountService.FutureIface.class,
                accountClientConfig);

        FinagleClientConfig ledgerClientConfig = new FinagleClientConfig();
        ledgerClientConfig.setServerAddr(primeLedgerClientRpcConfig.getZkAddress());
        ledgerClientConfig.setRequestTimeoutMillis(primeLedgerClientRpcConfig.getRequestTimeout());
        ledgerClientConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        ledgerService = FinagleClientBuilder.build(primeLedgerClientRpcConfig.getLabel(),
                LedgerService.FutureIface.class, ledgerClientConfig);

        FinagleClientConfig orderClientConfig = new FinagleClientConfig();
        orderClientConfig.setServerAddr(primeOrderRpcConfig.getZkAddress());
        orderClientConfig.setRequestTimeoutMillis(primeOrderRpcConfig.getRequestTimeout());
        orderClientConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        tgOrderService = FinagleClientBuilder.build(primeOrderRpcConfig.getLabel(),
                TGOrderService.FutureIface.class, orderClientConfig);
    }

    /**
     * 查询执行上手和deal account 账号
     */
    public AccountDTO queryAccount(QueryAccountFacadeContext context) {
        if (context.invalid()){
            log.error("queryAccount error, context invalid : {}", context);
            return null;
        }
        try {
            ContractInfoDTO contractInfoDTO = context.getContractInfoDTO();
            Future<RpcTradeExecutionAccount> future = rpcAccountService.getTradeExecutionAccount(
                    Long.parseLong(context.getAccount()),
                    contractInfoDTO.getContractId(), context.getSource());
            RpcTradeExecutionAccount tradeExecutionAccount = future.toJavaFuture().get();
            log.info("query execution master and da: request-{}, response-{}", context, tradeExecutionAccount.toString());
            if (ObjectUtils.anyNull(
                    tradeExecutionAccount,
                    tradeExecutionAccount.getExecutingBrokerAccount(),
                    tradeExecutionAccount.getExecutionAccount())) {
                log.error("queryAccount failed, request = {}, response ={}",
                        context.getAccount() + "," + contractInfoDTO, tradeExecutionAccount);
                return null;
            }
            return AccountDTO.builder()
                    .executionMaster(tradeExecutionAccount.getExecutingBrokerAccount().getExecutingBroker())
                    .dealAccountId(String.valueOf(tradeExecutionAccount.getExecutionAccount()))
                    .traderAccount(tradeExecutionAccount.isSetOddAccount() ? String.valueOf(tradeExecutionAccount.getOddAccount()) : null)
                    .build();
        } catch (Exception e) {
            log.error("queryExecutionMaster error {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取通用账本账户 General Ledger Account
     * @param context
     */
    public ClearingAccountInfo queryGeneralLedgerAccount(QueryAccountFacadeContext context) {
        if (context.invalid()){
            log.error("queryGLAccount error, context invalid : {}", context);
            return null;
        }
        try {
            ContractInfoDTO contractInfoDTO = context.getContractInfoDTO();
            ClearingAccountQryParam clearingAccountQryParam = new ClearingAccountQryParam(Long.parseLong(context.getAccount()),
                    contractInfoDTO.getMarket(), contractInfoDTO.getSecType(), true);
            ClearingAccountInfo clearingAccountInfo = ledgerService.qryClearingAccount(clearingAccountQryParam).toJavaFuture().get();
            log.info("queryClearingAccount: request-{}, response-{}", context, clearingAccountInfo);
            if (!clearingAccountInfo.getExist()) {
                log.error("queryGLAccount failed, request = {}, response ={}", context, clearingAccountInfo);
                return null;
            }
            return clearingAccountInfo;
        } catch (Exception e) {
            log.error("queryGLAccount error {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取上手信息
     */
    public ExecutingBrokerDTO queryExecutingBroker(long accountId, long contractId) {
        TGOrderRouteParam param =
                new TGOrderRouteParam.Builder().accountId(accountId).contractId(contractId).isLong(true).build();
        TGOrderRouteReq req =
                new TGOrderRouteReq.Builder()
                        .orderRouteParams(Lists.newArrayList(param))
                        .build();
        try {
            Future<TGOrderRouteResp> future = tgOrderService.queryOrderRoute(req);
            TGOrderRouteResp resp = future.toJavaFuture().get();
            log.info("queryExecutingBroker: request-{}, response-{}", req, resp);
            if (ObjectUtils.anyNull(resp, resp.getOrderRouteResults(), resp.getOrderRouteResults().get(0))) {
                log.error("queryExecutingBroker failed without result, request = {}, response ={}", req, resp);
                return null;
            }
            TGOrderRouteData routeData = resp.getOrderRouteResults().get(0);
            if (routeData.isSetExecutingBroker()) {
                return ExecutingBrokerDTO.builder()
                        .executingBroker(routeData.getExecutingBroker())
                        .build();
            }
        } catch (Exception e) {
            log.error("queryExecutingBroker account:{}, contract:{} error {}", accountId, contractId, e.getMessage(), e);
        }
        return null;
    }
}
