package com.tigerbrokers.oae.facade.context;

import lombok.Builder;
import lombok.Data;

/**
 * @author: chentao
 * Created on 2/6/2023
 */
@Data
@Builder
public class CalculateLockAssetContext {

    private String account;

    private String action;

    private String orderType;

    private String secType;

    private String symbol;

    private String externalId;

    private String source;

    private String appId;

    private String timeInForce;

    private String amount;

    private int quantity;

}
