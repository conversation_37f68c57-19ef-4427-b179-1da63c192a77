package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
@Builder
public class CancelOrderFacadeContext {

    private Long tradeId;

    private String account;

    private BusinessSource source;

    public static CancelOrderFacadeContext create(AggregationExecutionOrder executionOrder) {
        return CancelOrderFacadeContext.builder()
                .tradeId(Long.valueOf(executionOrder.getTradeId()))
                .account(executionOrder.getDealAccount())
                .source(BusinessSource.valueOf(executionOrder.getSource()))
                .build();
    }
}
