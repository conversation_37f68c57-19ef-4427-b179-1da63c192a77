package com.tigerbrokers.oae.facade;

import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.tools.extra.ftp.FtpParams;
import com.tigerbrokers.tools.extra.ftp.SFtpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@Slf4j
@Service
public class AllocationReportFtpFacade {

    private SFtpClient sFtpClient;

    @Autowired
    public AllocationReportFtpFacade(FacadeConfig.AllocationReportFtpConfig allocationReportFtpConfig) {
        FtpParams ftpParams = new FtpParams();
        ftpParams.setHost(allocationReportFtpConfig.getHost());
        ftpParams.setPort(allocationReportFtpConfig.getPort());
        ftpParams.setUsername(allocationReportFtpConfig.getUsername());
        ftpParams.setPassword(allocationReportFtpConfig.getPassword());
        ftpParams.setDirectory(allocationReportFtpConfig.getAllocationPath());
        sFtpClient = new SFtpClient(ftpParams);
    }

    public void uploadReport(String filename) {
        try {
            sFtpClient.uploadFile(filename, SFtpClient.OVERWRITE);
            log.info("finished upload file: {}", filename);
        } catch (Exception e) {
            log.error("uploadReport {}", e.getMessage(), e);
        }
    }
}
