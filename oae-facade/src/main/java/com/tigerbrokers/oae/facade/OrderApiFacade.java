package com.tigerbrokers.oae.facade;

import com.google.common.collect.Lists;
import com.tigerbrokers.alpha.commons.utils.TimeUtils;
import com.tigerbrokers.alpha.model.TradeBO;
import com.tigerbrokers.brokerage.sdk.common.TigerApiException;
import com.tigerbrokers.brokerage.sdk.common.exception.AgencyHttpException;
import com.tigerbrokers.brokerage.sdk.common.request.OmnibusRequest;
import com.tigerbrokers.brokerage.sdk.common.request.OmnibusRequestConfig;
import com.tigerbrokers.brokerage.sdk.trade.biz.OrderBiz;
import com.tigerbrokers.brokerage.sdk.trade.biz.OrderQueryBiz;
import com.tigerbrokers.brokerage.sdk.trade.dto.OmnibusOrderDTO;
import com.tigerbrokers.brokerage.sdk.trade.dto.OmnibusPreTradeDTO;
import com.tigerbrokers.brokerage.sdk.trade.request.OddOrderRequest;
import com.tigerbrokers.brokerage.sdk.trade.request.OrderPreviewRequest;
import com.tigerbrokers.brokerage.sdk.trade.request.OrderQueryRequest;
import com.tigerbrokers.brokerage.sdk.trade.request.OrderRequest;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.OrderReqType;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.facade.config.PrimeApiConfig;
import com.tigerbrokers.oae.facade.context.CancelOrderFacadeContext;
import com.tigerbrokers.oae.facade.context.ModifyOrderFacadeContext;
import com.tigerbrokers.oae.facade.context.placeOrder.PlaceOrderFacadeContext;
import com.tigerbrokers.oae.facade.context.placeOrder.PreTradeFacadeContext;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.facade.dto.PreTradeFacadeDTO;
import com.tigerbrokers.oae.facade.dto.TradeDTO;
import lombok.extern.slf4j.Slf4j;
import net.jodah.failsafe.function.CheckedFunction;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Slf4j
@Service
public class OrderApiFacade {

    public static final String IP;

    public Map<BusinessSource, OmnibusRequestConfig> source2OmnibusRequestConfigMap;

    static {
        try {
            IP = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.error("get IP fail:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Autowired
    private OrderBiz orderBiz;

    @Autowired
    private OrderQueryBiz orderQueryBiz;

    @Autowired
    public OrderApiFacade(FacadeConfig.PrimeRspConfig rspConfig, FacadeConfig.PrimeDripConfig dripConfig) {
        PrimeApiConfig primeApiConfig = new PrimeApiConfig(rspConfig, dripConfig);
        this.source2OmnibusRequestConfigMap = primeApiConfig.getSource2OmnibusRequestConfigMap();
    }

    public PreTradeFacadeDTO preTrade(PreTradeFacadeContext context) {
        OrderPreviewRequest request = buildOrderPreviewRequest(context);
        try {
            OmnibusPreTradeDTO omnibusPreTradeDTO = executeAndHandleExceptions(
                    (Void v) -> orderBiz.previewOrder(request),
                    "previewOrder",
                    request.toString());
            return coverToPreTradeFacadeDTO(omnibusPreTradeDTO);
        } catch (TigerApiException e) {
            log.error("preTrade {}", e.getMessage(), e);
            return PreTradeFacadeDTO.builder().errorCode(e.getErrorCode()).message(e.getMessage()).build();
        }
    }

    public OrderFacadeDTO placeOrder(PlaceOrderFacadeContext context) {
        OrderRequest request = buildOrderRequest(context);
        try {
            OmnibusOrderDTO omnibusOrderDTO = executeAndHandleExceptions(
                    (Void v) -> orderBiz.placeOrder(request),
                    "placeOrder",
                    request.toString());
            return coverToOrderFacadeDTO(omnibusOrderDTO);
        } catch (TigerApiException e) {
            log.error("placeOrder {}", e.getMessage(), e);
            return OrderFacadeDTO.builder().errorCode(e.getErrorCode()).errorMsg(e.getMessage()).build();
        }
    }

    public OrderFacadeDTO modifyOrder(ModifyOrderFacadeContext context) {
        OrderRequest request = buildModifyOrderRequest(context);
        try {
            OmnibusOrderDTO omnibusOrderDTO = executeAndHandleExceptions(
                    (Void v) -> orderBiz.modifyOrder(request),
                    "modifyOrder",
                    request.toString());
            return coverToOrderFacadeDTO(omnibusOrderDTO);
        } catch (TigerApiException e) {
            log.error("modifyOrder {}", e.getMessage(), e);
            return OrderFacadeDTO.builder().errorCode(e.getErrorCode()).errorMsg(e.getMessage()).build();
        }
    }

    public OrderFacadeDTO cancelOrder(CancelOrderFacadeContext context) {
        OrderQueryRequest request = buildCancelOrderRequest(context);
        try {
            OmnibusOrderDTO omnibusOrderDTO = executeAndHandleExceptions(
                    (Void v) -> orderBiz.cancelOrder(request),
                    "cancelOrder",
                    request.toString());
            return coverToOrderFacadeDTO(omnibusOrderDTO);
        } catch (TigerApiException e) {
            log.error("cancelOrder {}", e.getMessage(), e);
            return OrderFacadeDTO.builder().errorCode(e.getErrorCode()).errorMsg(e.getMessage()).build();
        }
    }

    private OrderQueryRequest buildCancelOrderRequest(CancelOrderFacadeContext context) {
        OrderQueryRequest request = new OrderQueryRequest();
        request.setId(context.getTradeId());
        request.setAccountId(context.getAccount());
        initFixRequest(request, context.getSource());
        return request;
    }

    /**
     * 下碎股单
     * @param context
     */
    public OrderFacadeDTO placeOddOrder(PlaceOrderFacadeContext context) {
        OddOrderRequest request = buildOddOrderRequest(context);
        try {
            OmnibusOrderDTO omnibusOrderDTO = executeAndHandleExceptions(
                    (Void v) -> orderBiz.placeOddOrder(request),
                    "placeOddOrder",
                    request.toString());
            return coverToOrderFacadeDTO(omnibusOrderDTO);
        } catch (TigerApiException e) {
            log.error("placeOrder {}", e.getMessage(), e);
            return OrderFacadeDTO.builder().errorCode(e.getErrorCode()).errorMsg(e.getMessage()).build();
        }
    }

    public TradeDTO getTradeByOrderId(Long orderId) {
        try {
            log.info("getTradesByOrderId orderId: {}", orderId);
            List<TradeBO> trades = orderQueryBiz.queryTradeByOrder(orderId, 1);
            log.info("getTradesByOrderId orderId: {}, trades: {}", orderId, trades);
            if (CollectionUtils.isEmpty(trades)) {
                log.error("getTradesByOrderId empty, orderId:{}", orderId);
                return null;
            }
            return convertTrade(trades.get(0));
        } catch (Exception e) {
            log.error("getTradesByOrderId error, orderId: {}", orderId, e);
        }
        return null;
    }

    public OmnibusOrderDTO queryOrderById(String omOrderId, String accountId, BusinessSource source) {
        OrderQueryRequest request = buildQueryOrderFacadeContext(omOrderId, accountId, source);
        try {
            return executeAndHandleExceptions(
                    (Void v) -> orderBiz.getOrderById(request),
                    "queryOrderById",
                    request.toString());
        } catch (TigerApiException e) {
            log.error("queryOrderById {}", e.getMessage(), e);
            return null;
        }
    }

    private OrderPreviewRequest buildOrderPreviewRequest(PreTradeFacadeContext context) {
        OrderPreviewRequest result = OrderPreviewRequest.builder()
                .orderReqType(OrderReqType.PLACE.name())
                .orderReqs(Lists.newArrayList(buildPreTradeOrderRequest(context)))
                .build();
        result.setAccountId(context.getAccount());
        initFixRequest(result, context.getSource());
        return result;
    }

    private OrderRequest buildPreTradeOrderRequest(PreTradeFacadeContext context) {
        OrderRequest result = new OrderRequest();
        result.setAccountId(String.valueOf(Long.parseLong(context.getAccount())));
        result.setSymbol(context.getSymbol());
        result.setSecType(context.getSecType());
        result.setSegType(context.getSegType());
        result.setExternalId(context.getExternalId());
        result.setOrderType(context.getOrderType());
        result.setPortfolioId(context.getPortfolioId());
        result.setAction(context.getAction());
        if (Objects.nonNull(context.getQuantity()) && BigDecimalUtil.isMoreThan(context.getQuantity(), 0)) {
            result.setQuantity(BigDecimalUtil.toLong(context.getQuantity()));
            result.setQuantityScale(BigDecimalUtil.scale(context.getQuantity()));
        } else if (Objects.nonNull(context.getAmount())) {
            result.setCashAmount(BigDecimal.valueOf(context.getAmount()));
        }
        result.setTimeInForce(context.getTimeInForce());
        result.setAttrs(context.getOrderAttrs());
        return result;
    }

    private OrderRequest buildOrderRequest(PlaceOrderFacadeContext context) {
        OrderRequest result = new OrderRequest();
        result.setAccountId(context.getAccount());
        result.setSymbol(context.getSymbol());
        result.setSegType(context.getSegType());
        result.setSecType(context.getSecType());
        result.setExternalId(context.getExternalId());
        result.setOrderType(context.getOrderType());
        result.setAction(context.getAction());
        if (Objects.nonNull(context.getQuantity()) && BigDecimalUtil.isMoreThan(context.getQuantity(), 0)) {
            result.setQuantity(BigDecimalUtil.toLong(context.getQuantity()));
            result.setQuantityScale(BigDecimalUtil.scale(context.getQuantity()));
            BigDecimal price = context.getPrice() == null ? null : BigDecimal.valueOf(context.getPrice());
            result.setPrice(price);
        } else if (Objects.nonNull(context.getAmount())) {
            result.setCashAmount(BigDecimal.valueOf(context.getAmount()));
        }
        if (context.getTradingSessionType() != null) {
            result.setTradingSessionType(context.getTradingSessionType());
        }
        result.setTimeInForce(context.getTimeInForce());
        result.setAttrs(context.getAttrs());
        initFixRequest(result, context.getSource());
        return result;
    }

    private OrderRequest buildModifyOrderRequest(ModifyOrderFacadeContext context) {
        OrderRequest result = new OrderRequest();
        result.setId(context.getTradeId());
        result.setAccountId(context.getAccount());
        if (Objects.nonNull(context.getQuantity())) {
            result.setQuantity(BigDecimalUtil.toLong(context.getQuantity()));
            result.setQuantityScale(BigDecimalUtil.scale(context.getQuantity()));
        }
        result.setPrice(BigDecimal.valueOf(context.getPrice()));
        initFixRequest(result, context.getSource());
        return result;
    }

    private OddOrderRequest buildOddOrderRequest(PlaceOrderFacadeContext context) {
        OddOrderRequest result = new OddOrderRequest();
        result.setAccountId(context.getAccount());
        result.setSymbol(context.getSymbol());
        result.setExternalId(context.getExternalId());
        result.setOrderType(context.getOrderType());
        result.setAction(context.getAction());
        result.setQuantity(BigDecimalUtil.toLong(context.getQuantity()));
        result.setQuantityScale(BigDecimalUtil.scale(context.getQuantity()));
        result.setPrice(BigDecimal.valueOf(context.getPrice()));
        result.setAttrs(context.getAttrs());
        initFixRequest(result, context.getSource());
        return result;
    }

    private OrderQueryRequest buildQueryOrderFacadeContext(String omOrderId, String accountId, BusinessSource source) {
        OrderQueryRequest result = new OrderQueryRequest();
        result.setId(Long.valueOf(omOrderId));
        result.setAccountId(accountId);
        initFixRequest(result, source);
        return result;
    }

    private PreTradeFacadeDTO coverToPreTradeFacadeDTO(OmnibusPreTradeDTO omnibusPreTradeDTO) {
        return PreTradeFacadeDTO.builder()
                .isPass(omnibusPreTradeDTO.getIsPass())
                .errorCode(omnibusPreTradeDTO.getErrorCode())
                .message(omnibusPreTradeDTO.getMessage())
                .build();
    }

    private OrderFacadeDTO coverToOrderFacadeDTO(OmnibusOrderDTO omnibusOrderDTO) {
        return OrderFacadeDTO.builder()
                .tradeId(omnibusOrderDTO.getId())
                .externalId(omnibusOrderDTO.getExternalId())
                .symbol(omnibusOrderDTO.getSymbol())
                .currency(omnibusOrderDTO.getCurrency())
                .secType(omnibusOrderDTO.getSecType())
                .action(omnibusOrderDTO.getAction())
                .orderType(omnibusOrderDTO.getOrderType())
                .totalQuantity(omnibusOrderDTO.getTotalQuantity())
                .totalQuantityScale(omnibusOrderDTO.getTotalQuantityScale())
                .filledQuantity(omnibusOrderDTO.getFilledQuantity())
                .filledQuantityScale(omnibusOrderDTO.getFilledQuantityScale())
                .totalCashAmount(omnibusOrderDTO.getTotalCashAmount())
                .filledCashAmount(omnibusOrderDTO.getFilledCashAmount())
                .avgFillPrice(omnibusOrderDTO.getAvgFillPrice())
                .realizedPnl(omnibusOrderDTO.getRealizedPnl())
                .commission(omnibusOrderDTO.getCommission())
                .gst(omnibusOrderDTO.getGst())
                .commissionAndFee(omnibusOrderDTO.getCommissionAndFee())
                .canModify(omnibusOrderDTO.getCanModify())
                .canCancel(omnibusOrderDTO.getCanCancel())
                .status(omnibusOrderDTO.getStatus())
                .cancelStatus(omnibusOrderDTO.getCancelStatus())
                .replaceStatus(omnibusOrderDTO.getReplaceStatus())
                .subStatusList(omnibusOrderDTO.getAttrList())
                .isSettled(omnibusOrderDTO.getIsSettled())
                .message(omnibusOrderDTO.getMessage())
                .messageCode(omnibusOrderDTO.getMessageCode())
                .messageType(omnibusOrderDTO.getMessageType())
                .errorCode(omnibusOrderDTO.getErrorCode())
                .attrList(omnibusOrderDTO.getAttrList())
                .createdAt(omnibusOrderDTO.getCreatedAt())
                .statusUpdatedAt(omnibusOrderDTO.getStatusUpdatedAt())
                .build();
    }

    private void initFixRequest(OmnibusRequest request, BusinessSource source) {
        request.setOmnibusRequestConfig(source2OmnibusRequestConfigMap.get(source));
        request.setUserId(-1L);
        request.setRemoteIp(IP);
    }

    private <T> T executeAndHandleExceptions(CheckedFunction<Void, T> function, String functionName, String requestString) throws TigerApiException {
        try {
            T dto = function.apply(null);
            log.info("{}: request-{}, response-{}", functionName, requestString, dto.toString());
            return dto;
        } catch (TigerApiException e) {
            log.error("{} params:{}, fail:{}", functionName, requestString, e.getErrorMessage(), e);
            throw e;
        } catch (AgencyHttpException e) {
            log.error("{} params:{}, fail:{}", functionName, requestString, e.getMessage(), e);
            throw new TigerApiException(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            log.error("{} param:{} fail:{}", functionName, requestString, e.getMessage(), e);
            throw new TigerApiException(0, e.getMessage());
        }

    }


    private TradeDTO convertTrade(TradeBO trade){
        TradeDTO.TradeDTOBuilder builder = TradeDTO.builder();
        if (trade.getTransactedAt() != null) {
            builder.transactedAt(trade.getTransactedAt());
        }
        if (trade.getBusinessDate() != null) {
            builder.businessDate(TimeUtils.toDate(trade.getBusinessDate()));
        }
        if (trade.getSettlementDate() != null) {
            builder.settlementDate(TimeUtils.toDate(trade.getSettlementDate()));
        }
        if (trade.getNavDate() != null) {
            builder.navDate(TimeUtils.toDate(trade.getNavDate()));
        }
        return builder.build();
    }

}
