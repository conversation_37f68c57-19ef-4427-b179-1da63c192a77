package com.tigerbrokers.oae.facade;

import com.google.common.collect.Lists;
import com.tigerbrokers.alpha.commons.data.SecType;
import com.tigerbrokers.alpha.core.data.contract.Contract;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.stock.api.realtime.IBriefQuoteService;
import com.tigerbrokers.stock.api.realtime.quote.*;
import com.tigerbrokers.stock.common.StockMarket;
import com.tigerbrokers.stock.dto.mid.BriefDataDTO;
import com.tigerbrokers.stock.result.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class QuoteFacade {

    @DubboReference(registry = "quoteRegistry")
    private IHkQuoteService iHkQuoteService;

    @DubboReference(registry = "quoteRegistry")
    private IUsQuoteService iUsQuoteService;

    @DubboReference(registry = "quoteRegistry")
    private ICnQuoteService iCnQuoteService;

    @DubboReference(registry = "quoteRegistry")
    private INzQuoteService iNzQuoteService;

    @DubboReference(registry = "quoteRegistry")
    private ISiQuoteService iSiQuoteService;

    @DubboReference(registry = "quoteRegistry")
    private IAuQuoteService iAuQuoteService;

    @Autowired
    private ContractRpcFacade contractRpcFacade;

    private Map<String, IBriefQuoteService> quoteServiceMap;


    @PostConstruct
    private void init() {
        quoteServiceMap = new HashMap<>();
        quoteServiceMap.put(StockMarket.HK.name(), iHkQuoteService);
        quoteServiceMap.put(StockMarket.US.name(), iUsQuoteService);
        quoteServiceMap.put(StockMarket.CN.name(), iCnQuoteService);
        quoteServiceMap.put(StockMarket.NZ.name(), iNzQuoteService);
        quoteServiceMap.put(StockMarket.SI.name(), iSiQuoteService);
        quoteServiceMap.put(StockMarket.AU.name(), iAuQuoteService);
    }

    public QuotePointDTO queryRealTimeQuote(String symbol) {
        try {
            IBriefQuoteService quoteService = getMarketQuoteService(symbol);
            if (Objects.nonNull(quoteService)) {
                ApiResult<List<BriefDataDTO>> rpcResult = quoteService.brief(Lists.newArrayList(symbol));
                log.info("query realtime quote: symbol-{}, response-{}", symbol, rpcResult);
                if (rpcResult.ifError()) {
                    log.error("IBriefQuoteService brief symbol:{} fail code:{}, msg:{}", symbol,
                            rpcResult.getCode(), rpcResult.getMessage());
                } else {
                    return buildQuotePointDTO(rpcResult.getData());
                }
            }
        } catch (Exception e) {
            log.error("queryRealTimeQuote {}", e.getMessage(), e);
        }

        return null;
    }

    private IBriefQuoteService getMarketQuoteService(String symbol) {
        Contract contract = contractRpcFacade.queryContract(symbol);
        if (!SecType.STK.name().equals(contract.getSecType())) {
            log.error("not supported symbol:{}, secType:{}", symbol, contract.getSecType());
        }
        IBriefQuoteService result = quoteServiceMap.get(contract.getMarket());
        if (Objects.isNull(result)) {
            log.error("not supported symbol:{}, market:{}", symbol, contract.getMarket());
        }
        return result;
    }

    private QuotePointDTO buildQuotePointDTO(List<BriefDataDTO> rpcResult) {
        QuotePointDTO result = new QuotePointDTO();
        if (CollectionUtils.isNotEmpty(rpcResult)) {
            BriefDataDTO briefDataDTO = rpcResult.get(0);
            result.setOpen(briefDataDTO.getOpen());
            result.setClose(briefDataDTO.getClose());
            result.setHigh(briefDataDTO.getHigh());
            result.setLow(briefDataDTO.getLow());
        }
        return result;
    }
}
