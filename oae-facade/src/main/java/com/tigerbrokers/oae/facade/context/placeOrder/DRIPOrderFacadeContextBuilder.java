package com.tigerbrokers.oae.facade.context.placeOrder;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.brokerage.sdk.common.enums.OrderAttr;
import com.tigerbrokers.brokerage.sdk.common.enums.TimeInForce;

import java.util.List;

public class DRIPOrderFacadeContextBuilder extends OrderFacadeContextBuilder {

    public static final List<String> FUND_DIVIDEND_SUB_ATTRS =
            Lists.newArrayList(OrderAttr.SUB_ORDER.name(), "DRIP", "FORBIDDEN_MARGIN");

    public static final List<String> FUND_DIVIDEND_SUPER_ATTRS = Lists.newArrayList(
            OrderAttr.SUPER_ORDER.name(), "POST_TRADE_ALLOCATION");




    @Override
    protected List<String> buildOrderAttrs(OrderOperation orderOperation) {
        switch (orderOperation) {
            case PRE_TRADE:
                return FUND_DIVIDEND_SUB_ATTRS;
            case PLACE_ORDER:
                return FUND_DIVIDEND_SUPER_ATTRS;
            default:
                return null;
        }

    }

    @Override
    protected String timeInForce() {
        return TimeInForce.GTC.name();
    }


}
