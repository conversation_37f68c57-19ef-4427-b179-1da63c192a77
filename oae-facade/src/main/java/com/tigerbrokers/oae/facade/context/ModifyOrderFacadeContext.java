package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
@Builder
public class ModifyOrderFacadeContext {

    private Long tradeId;

    private String account;

    private Double quantity;

    private Double price;

    private BusinessSource source;

    public static ModifyOrderFacadeContext create(AggregationExecutionOrder executionOrder,
                                                  AggregationExecutionOrderRecord recordOrder) {
        return ModifyOrderFacadeContext.builder()
                .tradeId(Long.valueOf(executionOrder.getTradeId()))
                .account(executionOrder.getDealAccount())
                .quantity(recordOrder.getQuantity())
                .price(recordOrder.getRefPrice())
                .source(BusinessSource.valueOf(executionOrder.getSource()))
                .build();
    }
}
