package com.tigerbrokers.oae.facade.context;

import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
public class QueryAccountFacadeContext {

    private String account;
    private ContractInfoDTO contractInfoDTO;

    private String source;

    public QueryAccountFacadeContext(String account, ContractInfoDTO contractInfoDTO, String source) {
        this.account = account;
        this.contractInfoDTO = contractInfoDTO;
        this.source = source;
    }

    public boolean invalid() {
        return ObjectUtils.anyNull(account, contractInfoDTO, source);
    }
}
