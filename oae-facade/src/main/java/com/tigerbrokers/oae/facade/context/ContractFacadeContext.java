package com.tigerbrokers.oae.facade.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: chentao
 * Created on 31/1/2023
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractFacadeContext {

    private String symbol;

    private BigDecimal price;

    public ContractFacadeContext(String symbol) {
        this.symbol = symbol;
    }
}
