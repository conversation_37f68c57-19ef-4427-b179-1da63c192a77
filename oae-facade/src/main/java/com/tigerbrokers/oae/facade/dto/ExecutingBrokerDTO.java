package com.tigerbrokers.oae.facade.dto;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 上手信息
 * <AUTHOR>
 * Created on 19/11/24
 */
@Data
@Builder
public class ExecutingBrokerDTO {

    public static final String IB_EXECUTING_BROKER = "IB";

    String executingBroker;

    String executingBrokerAccount;

    public boolean invalid() {
        return StringUtils.isBlank(executingBroker) || IB_EXECUTING_BROKER.equalsIgnoreCase(executingBroker);
    }

}
