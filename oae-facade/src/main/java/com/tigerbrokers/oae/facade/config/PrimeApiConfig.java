package com.tigerbrokers.oae.facade.config;

import ch.qos.logback.classic.Level;
import com.tigerbrokers.brokerage.sdk.common.enums.Language;
import com.tigerbrokers.brokerage.sdk.common.request.OmnibusRequestConfig;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: chentao
 * Created on 5/2/2023
 */
@Slf4j
public class PrimeApiConfig {

    @Getter
    private Map<BusinessSource, OmnibusRequestConfig> source2OmnibusRequestConfigMap = new HashMap<>();

    public PrimeApiConfig(FacadeConfig.PrimeRspConfig rspConfig, FacadeConfig.PrimeDripConfig dripConfig) {
        log.info("==PrimeApiConfig, rspConfig:{}, dripConfig:{}", rspConfig, dripConfig);
        if (rspConfig != null) {
            source2OmnibusRequestConfigMap.put(BusinessSource.RSP, buildOmnibusRequestConfig(rspConfig));
        }
        if (dripConfig != null) {
            source2OmnibusRequestConfigMap.put(BusinessSource.DRIP, buildOmnibusRequestConfig(dripConfig));
        }
        log.info("PrimeApiConfig, source2OmnibusRequestConfigMap:{}", JacksonUtil.writeValueAsString(source2OmnibusRequestConfigMap));
    }


    private OmnibusRequestConfig buildOmnibusRequestConfig(FacadeConfig.PrimeConfig primeConfig) {
        String language = StringUtils.isBlank(primeConfig.getLanguage()) ? Language.en_US.name() :
                primeConfig.getLanguage();
        OmnibusRequestConfig omnibusRequestConfig = new OmnibusRequestConfig(language, Level.INFO);
        omnibusRequestConfig.setApiKey(primeConfig.getApiKey());
        omnibusRequestConfig.setClientId(primeConfig.getClientId());
        omnibusRequestConfig.setPrivateKey(StringUtils.isEmpty(primeConfig.getPrivateKey()) ?
                getPrivateKey(primeConfig.getPrivateKeyPath()) : primeConfig.getPrivateKey());
        return omnibusRequestConfig;
    }

    private String getPrivateKey(String keyPath ) {
        String reuslt = null;
        if (StringUtils.isNotEmpty(keyPath)) {
            reuslt = getKey(keyPath);
        }
        return reuslt;
    }

    public static void main(String[] args) {
        String fileName = System.getProperty("user.dir") + File.separator + "key" + File.separator
                + "oae_private_key_test";
        File file = new File(fileName);
        System.out.println("file : " + file.exists());
    }

    private String getKey(String keyPath) {
        File file = new File(keyPath);
        if (file.exists() && file.isFile()) {
            try {
                String key = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
                if (key != null) {
                    log.info("PrimeApiConfig path:{} get key:{}", keyPath, key);
                    return key.trim();
                }
            } catch (Exception e) {
                log.error("get key : {} error {}", keyPath, e.getMessage(), e);
            }
        }

        return null;
    }
}
