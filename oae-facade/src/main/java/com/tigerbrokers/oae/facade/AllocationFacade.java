package com.tigerbrokers.oae.facade;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Lists;
import com.tigerbrokers.alpha.commons.data.Market;
import com.tigerbrokers.alpha.commons.utils.TimeUtils;
import com.tigerbrokers.alpha.finagle.client.FinagleClientBuilder;
import com.tigerbrokers.alpha.finagle.client.FinagleClientConfig;
import com.tigerbrokers.alpha.ledger.thrift.entity.*;
import com.tigerbrokers.alpha.ledger.thrift.response.LedgerUpdateErrorCode;
import com.tigerbrokers.alpha.ledger.thrift.response.LedgerUpdateResult;
import com.tigerbrokers.alpha.ledger.thrift.service.LedgerService;
import com.tigerbrokers.alpha.model.ledger.BookkeepingType;
import com.tigerbrokers.oae.entity.consts.LockType;
import com.tigerbrokers.oae.entity.util.AmountUtil;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.facade.consts.LedgerBusinessType;
import com.tigerbrokers.oae.facade.context.AllocationFacadeContext;
import com.tigerbrokers.oae.facade.context.CashTransactionFacadeContext;
import com.tigerbrokers.oae.facade.context.LockedAssetFacadeContext;
import com.tigerbrokers.oae.facade.dto.AllocationFacadeDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@Service
public class AllocationFacade {

    private FacadeConfig.PrimeLedgerClientRpcConfig rpcConfig;

    private LedgerService.FutureIface ledgerService;

    @Autowired
    public AllocationFacade(FacadeConfig.PrimeLedgerClientRpcConfig rpcConfig) {
        this.rpcConfig = rpcConfig;
        init();
    }

    private void init() {
        FinagleClientConfig clientConfig = new FinagleClientConfig();
        clientConfig.setServerAddr(rpcConfig.getZkAddress());
        clientConfig.setRequestTimeoutMillis(rpcConfig.getRequestTimeout());
        clientConfig.setRetryBudget(FacadeUtils.DEFAULT_RETRY_BUDGET_PARAMS);
        ledgerService = FinagleClientBuilder.build(rpcConfig.getLabel(),
                LedgerService.FutureIface.class, clientConfig);
    }

    public AllocationFacadeDTO allocation(List<AllocationFacadeContext> context, String businessSource) {
        try {
            DateTime now = DateTime.now();
            Transaction transaction = buildTransaction(Lists.newArrayList(context), now.toDate(), businessSource);
            Retryer<LedgerUpdateResult> retryer = RetryerBuilder.<LedgerUpdateResult>newBuilder()
                    .retryIfResult(this::allocationNotSuccess)
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))
                    .build();
            LedgerUpdateResult updateResult = retryer.call(() -> ledgerService.updateLedger(transaction).toJavaFuture().get());
            log.info("allocation request-{}, response-{}", transaction.toString(), updateResult);
            if (warnErrorCode(updateResult.getErrorCode())) {
                log.warn("ledgerService updateLedger params:{}, result:{}", transaction.toString(), updateResult);
            } else {
                //todo need update
            }
            return coverToAllocationFacadeDTO(updateResult);
        } catch (Exception e) {
            log.error("allocation {}", e.getMessage(), e);
        }

        return null;
    }

    private boolean allocationNotSuccess(LedgerUpdateResult updateResult) {
        try {
            return !updateResult.getSuccess() && !warnErrorCode(updateResult.getErrorCode());
        } catch (Exception e) {
            log.error("allocation result:{}, fail:{}", updateResult, e.getMessage(), e);
            return false;
        }
    }

    private Transaction buildTransaction(List<AllocationFacadeContext> contextList, Date updateTime, String businessSource) {
        List<AllocationTrade> allocationTradeList = Lists.newArrayList();
        List<CashTransaction> cashTransactionList = Lists.newArrayList();
        List<PositionTransaction> positionTransactions = Lists.newArrayList();
        for (AllocationFacadeContext context : contextList) {
            //trade
            AllocationTrade allocationTrade = buildAllocationTrade(context);
            if (allocationTrade != null) {
                allocationTradeList.add(allocationTrade);
            }
            //unlock place order asset(中台解锁)
            LockedAssetFacadeContext unLockContext = context.getUnLockedContext();
            if (unLockContext != null) {
                if (LockType.ASSET.equals(unLockContext.getLockType())) {
                    cashTransactionList.addAll(buildUnLockCashTransaction(context));
                } else if (LockType.POSITION.equals(unLockContext.getLockType())) {
                    positionTransactions.addAll(buildUnLockPositionTransaction(context));
                }
            }
            //drip buy新增流水
            cashTransactionList.addAll(buildDripCashTransaction(context));
            //lock settle position（中台加锁）
            positionTransactions.addAll(buildSettleLockPositionTransaction(context));
        }
        return new Transaction.Builder()
                .businessType(getBusinessType(businessSource).name())
                .transactionUpdatedAt(updateTime.getTime())
                .allocationTrades(allocationTradeList)
                .cash(cashTransactionList)
                .positions(positionTransactions)
                .build();
    }

    private LedgerBusinessType getBusinessType(String source) {
        BusinessSource businessSource = BusinessSource.valueOf(source);
        switch (businessSource) {
            case RSP:
                return LedgerBusinessType.RSP_ALLOCATION;
            case DRIP:
                return LedgerBusinessType.DRIP_ALLOCATION;
            default:
                return null;
        }
    }

    private AllocationTrade buildAllocationTrade(AllocationFacadeContext context) {
        if (null == context.getAllocationRefId()) {
            return null;
        }
        AllocationTrade.Builder builder = new AllocationTrade.Builder()
                .refId(context.getAllocationRefId())
                .sourceAccountId(Long.parseLong(context.getSourceAccount()))
                .targetAccountId(Long.parseLong(context.getTargetAccount()))
                .segType(context.getSegType())
                .contractId(context.getContractId())
                .quantity(String.valueOf(context.getQuantity()))
                .filledPrice(String.valueOf(context.getFilledPrice()))
                .txDateTime(buildTransactionDateTime(context.getTransactTime(), context.getBusinessDate(),
                        context.getSettleDate(), context.getNavDate(), context.getMarket()));
        if (context.getSuperOrderId() != null) {
            builder.superOrderId(context.getSuperOrderId());
        }
        ClassifiedAssetInfo classifiedAssetInfo = buildClassifiedAssetInfo(context.getPortfolioId());
        if (classifiedAssetInfo != null) {
            builder.classifiedAssetInfo(classifiedAssetInfo);
        }
        return builder.build();
    }

    private List<CashTransaction> buildUnLockCashTransaction(AllocationFacadeContext context) {
        List<CashTransaction> result = Lists.newArrayList();
        LockedAssetFacadeContext lockedAssetFacadeContext = context.getUnLockedContext();
        if (Objects.nonNull(lockedAssetFacadeContext) && MapUtils.isNotEmpty(lockedAssetFacadeContext.getBookkeepingTypeValue())) {
            for (Map.Entry<BookkeepingType, Double> entry: context.getUnLockedContext().getBookkeepingTypeValue().entrySet()) {
                long accountId = Objects.nonNull(context.getBreakGLAccountId()) ? context.getBreakGLAccountId() :
                        Long.parseLong(context.getTargetAccount());
                CashTransaction.Builder cashTransactionBuilder = new CashTransaction.Builder()
                        .accountId(accountId)
                        .segType(context.getSegType())
                        .currency(context.getCurrency())
                        .type(TransactionType.valueOf(entry.getKey().name()))
                        .refType(lockedAssetFacadeContext.getRefType())
                        .refId(lockedAssetFacadeContext.getLockedRefId())
                        .amount(AmountUtil.toCent(entry.getValue()))
                        .txDateTime(buildTransactionDateTime(context.getTransactTime(), context.getBusinessDate(),
                                context.getBusinessDate(), context.getNavDate(), context.getMarket()));
                if (lockedAssetFacadeContext.getDesc() != null) {
                    cashTransactionBuilder.description(lockedAssetFacadeContext.getDesc());
                }
                ClassifiedAssetInfo classifiedAssetInfo = buildClassifiedAssetInfo(lockedAssetFacadeContext.getPortfolioId());
                if (classifiedAssetInfo != null) {
                    cashTransactionBuilder.classifiedAssetInfo(classifiedAssetInfo);
                }
                result.add(cashTransactionBuilder.build());
            }
        }
        return result;
    }

    private List<PositionTransaction> buildUnLockPositionTransaction(AllocationFacadeContext context) {
        List<PositionTransaction> result = Lists.newArrayList();
        LockedAssetFacadeContext lockedAssetFacadeContext = context.getUnLockedContext();
        if (Objects.nonNull(lockedAssetFacadeContext) && MapUtils.isNotEmpty(lockedAssetFacadeContext.getBookkeepingTypeValue())) {
            for (Map.Entry<BookkeepingType, Double> entry: context.getUnLockedContext().getBookkeepingTypeValue().entrySet()) {
                PositionTransaction.Builder positionTransaction = new PositionTransaction.Builder()
                        .accountId(Long.parseLong(context.getTargetAccount()))
                        .segType(context.getSegType())
                        .currency(context.getCurrency())
                        .contractId(lockedAssetFacadeContext.getContractId())
                        .type(TransactionType.valueOf(entry.getKey().name()))
                        .quantity(String.valueOf(entry.getValue()))
                        .avgCost("0")
                        .refType(lockedAssetFacadeContext.getRefType())
                        .refId(lockedAssetFacadeContext.getLockedRefId())
                        .txDateTime(buildTransactionDateTime(context.getTransactTime(), context.getBusinessDate(),
                                context.getBusinessDate(), context.getNavDate(), context.getMarket()));
                ClassifiedAssetInfo classifiedAssetInfo = buildClassifiedAssetInfo(lockedAssetFacadeContext.getPortfolioId());
                if (classifiedAssetInfo != null) {
                    positionTransaction.classifiedAssetInfo(classifiedAssetInfo);
                }
                result.add(positionTransaction.build());
            }
        }
        return result;
    }

    private List<CashTransaction> buildDripCashTransaction(AllocationFacadeContext context) {
        List<CashTransaction> cashTransactionList = new ArrayList<>();
        if (Objects.nonNull(context.getDripCashContext())) {
            CashTransactionFacadeContext dripCashContext = context.getDripCashContext();
            long accountId = Objects.nonNull(context.getBreakGLAccountId()) ? context.getBreakGLAccountId() :
                    Long.parseLong(context.getTargetAccount());
            CashTransaction.Builder cashTransactionBuilder = new CashTransaction.Builder()
                    .accountId(accountId)
                    .segType(context.getSegType())
                    .currency(context.getCurrency())
                    .type(dripCashContext.getType())
                    .refType(dripCashContext.getRefType())
                    .refId(dripCashContext.getRefId())
                    .amount(-1 * AmountUtil.toCent(dripCashContext.getAmount()))  //扣款传负数，传0是撤销流水
                    .txDateTime(buildTransactionDateTime(context.getTransactTime(), context.getBusinessDate(),
                            context.getBusinessDate(), context.getNavDate(), context.getMarket()));
            List<ClassifiedAssetInfo> classifiedAssetInfos = buildDripClassifiedAssetInfoList(dripCashContext);
            if (CollectionUtils.isNotEmpty(classifiedAssetInfos)) {
                cashTransactionBuilder.classifiedAssetInfoList(classifiedAssetInfos);
            }
            if (Objects.nonNull(dripCashContext.getExtraInfo())) {
                cashTransactionBuilder.extraInfo(dripCashContext.getExtraInfo());
            }
            cashTransactionList.add(cashTransactionBuilder.build());
        }
        return cashTransactionList;
    }

    private List<PositionTransaction> buildSettleLockPositionTransaction(AllocationFacadeContext context) {
        log.info("AllocationFacadeContext:{}", context);
        List<PositionTransaction> result = Lists.newArrayList();
        LockedAssetFacadeContext lockedAssetFacadeContext = context.getSettleLockedContext();
        if (Objects.nonNull(lockedAssetFacadeContext) && MapUtils.isNotEmpty(lockedAssetFacadeContext.getBookkeepingTypeValue())) {
            for (Map.Entry<BookkeepingType, Double> entry: lockedAssetFacadeContext.getBookkeepingTypeValue().entrySet()) {
                PositionTransaction.Builder positionTransaction = new PositionTransaction.Builder()
                        .accountId(Long.parseLong(context.getTargetAccount()))
                        .segType(context.getSegType())
                        .currency(context.getCurrency())
                        .contractId(lockedAssetFacadeContext.getContractId())
                        .type(TransactionType.valueOf(entry.getKey().name()))
                        .quantity(String.valueOf(entry.getValue()))
                        .avgCost(String.valueOf(context.getFilledPrice()))
                        .refType(lockedAssetFacadeContext.getRefType())
                        .refId(lockedAssetFacadeContext.getLockedRefId())
                        .txDateTime(buildTransactionDateTime(context.getTransactTime(), context.getBusinessDate(),
                                context.getBusinessDate(), context.getNavDate(), context.getMarket()));
                ClassifiedAssetInfo classifiedAssetInfo = buildClassifiedAssetInfo(lockedAssetFacadeContext.getPortfolioId());
                if (classifiedAssetInfo != null) {
                    positionTransaction.classifiedAssetInfo(classifiedAssetInfo);
                }
                result.add(positionTransaction.build());
            }
        }
        log.info("PositionTransactions:{}", result);
        return result;
    }


    private TransactionDateTime buildTransactionDateTime(Date tradeDate, Date businessDate, Date settleDate, Date navDate, String market) {
        if (tradeDate == null) {
            tradeDate = new Date();
        }
        if (businessDate == null) {
            businessDate = tradeDate;
        }
        TransactionDateTime.Builder builder = new TransactionDateTime.Builder();
        builder.transactTime(tradeDate.getTime())
                .tradeDate(TimeUtils.yyyyMMdd(TimeUtils.toLocalDateInZone(businessDate, ZoneId.systemDefault())))
                .businessDate(TimeUtils.yyyyMMdd(TimeUtils.toLocalDateInZone(businessDate, ZoneId.systemDefault())));
        //股票的settleDate可能为null，由后台计算
        if (settleDate != null) {
            builder.settleDate(TimeUtils.yyyyMMdd(TimeUtils.toLocalDateInZone(settleDate, ZoneId.systemDefault())));
        }
        if (navDate != null) {
            builder.navDate(TimeUtils.yyyyMMdd(TimeUtils.toLocalDateInZone(navDate, ZoneId.systemDefault())));
        }
        return builder.build();
    }

    private AllocationFacadeDTO coverToAllocationFacadeDTO(LedgerUpdateResult ledgerUpdateResult) {
        LedgerUpdateErrorCode code = ledgerUpdateResult.getErrorCode();
        return AllocationFacadeDTO.builder()
                .isSucc(ledgerUpdateResult.getSuccess())
                .code(Objects.nonNull(code) ? code.getValue() : LedgerUpdateErrorCode.SUCCESS.getValue())
                .msg(ledgerUpdateResult.getErrorMessage())
                .build();
    }

    private boolean warnErrorCode(LedgerUpdateErrorCode ledgerUpdateErrorCode) {
        return LedgerUpdateErrorCode.ERROR_DUPLICATE_POSITION.equals(ledgerUpdateErrorCode) ||
                LedgerUpdateErrorCode.ERROR_DUPLICATE_CASH.equals(ledgerUpdateErrorCode);
    }

    private ClassifiedAssetInfo buildClassifiedAssetInfo(Long portfolioId) {
        if (portfolioId == null || portfolioId < 0) {
            return null;
        }
        return new ClassifiedAssetInfo.Builder()
                .assetType(ClassifiedAssetType.PORTFOLIO)
                .assetSubType(portfolioId)
                .build();
    }

    private List<ClassifiedAssetInfo> buildDripClassifiedAssetInfoList(CashTransactionFacadeContext dripCashContext) {
        List<ClassifiedAssetInfo> classifiedAssetInfos = new ArrayList<>();
        ClassifiedAssetInfo classifiedAssetInfo = buildClassifiedAssetInfo(dripCashContext.getPortfolioId());
        if (classifiedAssetInfo != null) {
            classifiedAssetInfos.add(classifiedAssetInfo);
        }
        classifiedAssetInfos.add(new ClassifiedAssetInfo.Builder()
                .assetType(ClassifiedAssetType.CONTRACT_DIVIDEND)
                .assetSubType(dripCashContext.getContractId())
                .build());
        return classifiedAssetInfos;
    }
}
