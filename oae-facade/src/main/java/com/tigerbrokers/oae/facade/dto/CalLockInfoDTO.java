package com.tigerbrokers.oae.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: chentao
 * Created on 2/6/2023
 */
@Data
@Builder
public class CalLockInfoDTO {

    private Boolean isSucc;

    private String errMsg;

    private List<LockInfo> lockInfoList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LockInfo {

        private String segType;

        private String currency;

        private String bookkeepingType;

        private double amount;

        private int daysToSettle;

    }

}
