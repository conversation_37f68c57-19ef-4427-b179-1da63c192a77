package com.tigerbrokers.oae.facade.dto;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
@Builder
public class PreTradeFacadeDTO {

    private Boolean isPass;

    private Integer errorCode;

    private String message;

    public String getErrorMsg() {
        return StringUtils.isNotEmpty(message) ? message : String.valueOf(errorCode);
    }
}
