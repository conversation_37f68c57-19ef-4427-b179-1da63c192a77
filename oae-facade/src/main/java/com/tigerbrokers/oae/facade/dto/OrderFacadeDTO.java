package com.tigerbrokers.oae.facade.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Data
@Builder
public class OrderFacadeDTO {

    // om订单id
    private Long tradeId;

    private String externalId;

    private String idStr;

    private String symbol;

    private String currency;

    private String secType;

    private String segType;

    private String action;

    private String orderType;

    private Long totalQuantity;

    private Integer totalQuantityScale;

    private Long filledQuantity;

    private Integer filledQuantityScale;

    private Double totalCashAmount;

    private Double filledCashAmount;

    private Double avgFillPrice;

    private Integer latestPrice;

    private Double realizedPnl;

    private Double commission;

    private Double gst;

    private Double commissionAndFee;

    private Boolean canModify;

    private Boolean canCancel;

    private String status;

    private String cancelStatus;

    private String replaceStatus;

    private List<String> subStatusList;

    private Boolean isSettled;

    private String message;

    private String messageCode;

    private String messageType;

    private Integer errorCode;

    private String errorMsg;

    private List<String> attrList;

    private Long createdAt;

    private Long statusUpdatedAt;

    public Boolean isSucc() {
        return tradeId != null && errorCode == null;
    }

    public String getErrorCodeStr() {
        return errorCode == null ? null : String.valueOf(errorCode);
    }
}
