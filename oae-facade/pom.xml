<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oae-platform</artifactId>
        <groupId>com.tigerbrokers.oae.platform</groupId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oae-facade</artifactId>

    <dependencies>
        <!--project-->
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>commons</artifactId>
        </dependency>

        <!--alpha-->
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>asset-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>core-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>contract-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>ledger-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>contract-advanced-data-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>contract-advanced-data</artifactId>
        </dependency>
        <!--tiger-->
        <dependency>
            <groupId>com.tigerbrokers</groupId>
            <artifactId>quote-rpc-api</artifactId>
        </dependency>

        <!--dubbo-->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.tigerbrokers.brokerage.sdk</groupId>
            <artifactId>brokerage-trade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.brokerage.sdk</groupId>
            <artifactId>brokerage-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>account-rich-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven.deploy.plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>