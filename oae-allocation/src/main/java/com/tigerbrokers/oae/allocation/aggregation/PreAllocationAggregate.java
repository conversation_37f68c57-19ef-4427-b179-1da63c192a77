package com.tigerbrokers.oae.allocation.aggregation;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.*;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.AccountDTO;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import repackaged.com.arakelian.core.com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.tigerbrokers.oae.entity.consts.ExecutionErrorCode.NO_FILLED_ORDERS;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public abstract class PreAllocationAggregate {

    @Getter
    protected AggregationOrder aggregationOrder;

    protected List<AggregationExecutionOrder> executionOrderList;

    @Getter
    protected List<UserOrder> userOrderList;

    protected Date filledTime;

    private Date businessDate;

    private Date settleDate;

    private Date navDate;

    protected BusinessSource businessSource;

    public PreAllocationAggregate(AggregationOrder aggregationOrder,
                                  List<AggregationExecutionOrder> executionOrderList) {
        this.aggregationOrder = aggregationOrder;
        this.executionOrderList = executionOrderList;
        this.filledTime = getFilledTime();
        this.businessDate = getBusinessDate();
        this.settleDate = getSettleDate();
        this.navDate = getNavDate();
    }

    public String getExecutionMaster() {
        return aggregationOrder.getExecutionMaster();
    }

    private Date getFilledTime() {
        return executionOrderList.stream().map(AggregationExecutionOrder::getFilledTime)
                .filter(Objects::nonNull)
                .max(Date::compareTo).orElse(null);
    }

    private Date getBusinessDate() {
        return executionOrderList.stream().map(AggregationExecutionOrder::getBusinessDate)
                .filter(Objects::nonNull)
                .max(Date::compareTo).orElse(null);
    }

    private Date getSettleDate() {
        return executionOrderList.stream().map(AggregationExecutionOrder::getSettleDate)
                .filter(Objects::nonNull)
                .max(Date::compareTo).orElse(null);
    }

    private Date getNavDate() {
        return executionOrderList.stream().map(AggregationExecutionOrder::getNavDate)
                .filter(Objects::nonNull)
                .max(Date::compareTo).orElse(null);
    }

    public Boolean needAllocateSameDay() {
        return SecType.STK.name().equals(aggregationOrder.getSecType());
    }

    public List<Long> getUnLockUserOrderIds() {
        if (CollectionUtils.isNotEmpty(userOrderList)) {
            return userOrderList.stream().filter(item -> UserOrderStatus.REJECTED.name().equals(item.getStatus()))
                    .map(UserOrder::getId).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 是否可以执行分配计算
     * 所有执行订单AggregationExecutionOrder处于最终状态
     */
    public Boolean canAllocation() {
        List<AggregationExecutionOrder> unfinishedOrders = executionOrderList.stream()
                .filter(item -> !TradeStatus.isFinishedStatus(item.getTradeStatus()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(unfinishedOrders);
    }

    /**
     * 分配预计算
     */
    public void preAllocation(List<UserOrder> userOrderList, ContractInfoDTO contractInfoDTO) {
        this.userOrderList = userOrderList;
        // 更新AggregationOrder成交结果
        Boolean isFilled = processAggregationOrderFilled();
        // 用户分配计算
        preAllocationUserOrders(isFilled, contractInfoDTO.getShareScale());
    }

    /**
     * 聚合订单AggregationOrder成交情况计算
     * @return 聚合订单是否成交
     */
    public Boolean processAggregationOrderFilled() {
        List<AggregationExecutionOrder> filledOrders = executionOrderList.stream()
                .filter(item -> item.getFilledQuantity() != null && item.getAvgPrice() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filledOrders)) {
            aggregationOrder.setStatus(AggregationOrderStatus.REJECTED.name());
            aggregationOrder.setErrorCode(NO_FILLED_ORDERS.name());
            return Boolean.FALSE;
        }
        Double totalFilledQuantity = filledOrders.stream()
                .map(AggregationExecutionOrder::getFilledQuantity)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();
        if (BigDecimalUtil.isEqual(totalFilledQuantity, 0.0)) {
            aggregationOrder.setStatus(AggregationOrderStatus.REJECTED.name());
            aggregationOrder.setErrorCode(ExecutionErrorCode.NO_FILLED_QUANTITY.name());
            return Boolean.FALSE;
        }
        Double totalFilledAmount = filledOrders.stream()
                .map(item -> BigDecimalUtil.multiply(item.getFilledQuantity(), item.getAvgPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();
        // 均价精度
        int scale = getPricePrecision(filledOrders);
        Double avgPrice =
                BigDecimalUtil.divide(totalFilledAmount, totalFilledQuantity, scale, RoundingMode.CEILING).doubleValue();
        aggregationOrder.setFilledQuantity(totalFilledQuantity);
        aggregationOrder.setFilledAmount(totalFilledAmount);
        aggregationOrder.setAvgPrice(avgPrice);
        aggregationOrder.setStatus(AggregationOrderStatus.FILLED.name());
        return Boolean.TRUE;
    }

    /**
     * 用户订单分配计算
     */
    private void preAllocationUserOrders(Boolean isFilled, int shareScale) {
        if (BooleanUtils.isTrue(isFilled)) {
            // 用户订单成交分配
            updateFilledUserOrders(shareScale);
        } else {
            // 用户订单未成交更新
            updateUnfilledUserOrders();
        }
    }

    private void updateFilledUserOrders(int shareScale) {
        if (ActionType.BUY.name().equals(aggregationOrder.getAction())) {
            if (BooleanUtils.isTrue(isOverBuy())) {
                // 超买情况分配
                updateOverBuyUserOrders(shareScale);
            } else {
                // 非超买情况分配
                updateNotOverBuyUserOrders(shareScale);
            }
        } else {
            //卖出订单分配
            updateSellUserOrders(shareScale);
        }

    }

    private void updateOverBuyUserOrders(int shareScale) {
        for (UserOrder userOrder : userOrderList) {
            userOrder.setAvgPrice(aggregationOrder.getAvgPrice());
            Double filledQuantity = BigDecimal.valueOf(userOrder.getTradeAmount() / aggregationOrder.getAvgPrice())
                    .setScale(shareScale, RoundingMode.DOWN).doubleValue();
            userOrder.setFilledQuantity(filledQuantity);
            userOrder.setFilledAmount(userOrder.getTradeAmount());
            userOrder.setStatus(BigDecimalUtil.isMoreThan(filledQuantity, 0) ? UserOrderStatus.FILLED.name() : UserOrderStatus.REJECTED.name());
            userOrder.setFilledTime(filledTime);
            setRejectedErrorCode(userOrder);
        }
    }

    private void updateNotOverBuyUserOrders(int shareScale) {
        Double ratio = aggregationOrder.getFilledQuantity() / aggregationOrder.getTradeAmount();
        for (UserOrder userOrder : userOrderList) {
            userOrder.setAvgPrice(aggregationOrder.getAvgPrice());
            Double filledQuantity = BigDecimal.valueOf(userOrder.getTradeAmount() * ratio)
                    .setScale(shareScale, RoundingMode.DOWN).doubleValue();
            userOrder.setFilledQuantity(filledQuantity);
            Double filledAmount = BigDecimal.valueOf(filledQuantity * aggregationOrder.getAvgPrice())
                    .setScale(2, RoundingMode.HALF_UP).doubleValue();
            userOrder.setFilledAmount(filledAmount);
            userOrder.setStatus(BigDecimalUtil.isMoreThan(filledQuantity, 0) ? UserOrderStatus.FILLED.name() : UserOrderStatus.REJECTED.name());
            userOrder.setFilledTime(filledTime);
            setRejectedErrorCode(userOrder);
        }
    }

    private void setRejectedErrorCode(UserOrder userOrder) {
        if (UserOrderStatus.REJECTED.name().equals(userOrder.getStatus())) {
            if (BigDecimalUtil.isMoreThan(userOrder.getAvgPrice(), userOrder.getTradeAmount())) {
                userOrder.setErrorCode(ExecutionErrorCode.NOT_SATISFY_PRICE.name());
                userOrder.setErrorMsg(ExecutionErrorCode.NOT_SATISFY_PRICE.getMsg(userOrder.getTradeAmount(),
                        userOrder.getAvgPrice()));
            }
        }
    }

    private void updateSellUserOrders(int shareScale) {
        Double qtyRatio = aggregationOrder.getFilledQuantity() / aggregationOrder.getTradeQuantity();
        for (UserOrder userOrder : userOrderList) {
            userOrder.setStatus(UserOrderStatus.FILLED.name());
            userOrder.setAvgPrice(aggregationOrder.getAvgPrice());
            Double filledQuantity = BigDecimal.valueOf(userOrder.getQuantity() * qtyRatio)
                    .setScale(shareScale, RoundingMode.DOWN).doubleValue();
            userOrder.setFilledQuantity(filledQuantity);
            Double filledAmount = BigDecimal.valueOf(filledQuantity * aggregationOrder.getAvgPrice())
                    .setScale(2, RoundingMode.HALF_UP).doubleValue();
            userOrder.setFilledAmount(filledAmount);
            userOrder.setFilledTime(filledTime);
        }
    }

    private void updateUnfilledUserOrders() {
        String errMsg = buildUserErrorMsg();
        for (UserOrder userOrder : userOrderList) {
            userOrder.setStatus(UserOrderStatus.REJECTED.name());
            userOrder.setErrorCode(aggregationOrder.getErrorCode());
            userOrder.setErrorMsg(errMsg);
        }
    }

    protected String buildUserErrorMsg() {
        if (aggregationOrder.getErrorCode() == null) {
            return null;
        }
        return Optional.of(ExecutionErrorCode.valueOf(aggregationOrder.getErrorCode())).orElse(NO_FILLED_ORDERS).getMsg();
    }

    /**
     * 是否超买
     */
    private Boolean isOverBuy() {
        if (aggregationOrder.getTradeAmount() != null && aggregationOrder.getFilledAmount() != null) {
            return BigDecimalUtil.isMoreThan(aggregationOrder.getFilledAmount(), aggregationOrder.getTradeAmount());
        }
        return null;
    }

    /**
     * 是否成交
     */
    public Boolean isFilled() {
        return AggregationOrderStatus.FILLED.name().equals(aggregationOrder.getStatus());
    }

    /**
     * 生成分配记录数据
     */
    public List<TradeAllocation> buildAllocationList(AccountDTO accountDto, ContractInfoDTO contractInfoDTO) {
        List<TradeAllocation> tradeAllocationList = Lists.newArrayList();
        List<TradeAllocation> userTradeAllocation = buildUserAllocation(contractInfoDTO);
        if (CollectionUtils.isNotEmpty(userTradeAllocation)) {
            tradeAllocationList.addAll(userTradeAllocation);
        }
        //碎股回收
        TradeAllocation traderAllocation = buildTraderAllocation(accountDto.getTraderAccount(), contractInfoDTO);
        if (traderAllocation != null) {
            tradeAllocationList.add(traderAllocation);
        }
        // break 流水
        TradeAllocation breakAllocation = buildBreakAllocation(userTradeAllocation, traderAllocation, accountDto.getGlAccountId());
        if (breakAllocation != null) {
            tradeAllocationList.add(breakAllocation);
        }

        return tradeAllocationList;
    }

    private List<TradeAllocation> buildUserAllocation(ContractInfoDTO contractInfoDTO) {
        List<TradeAllocation> allocationList = Lists.newArrayList();
        for (UserOrder userOrder : userOrderList) {
            if (BigDecimalUtil.isLessOrEqualThan(userOrder.getFilledQuantity(), 0)) {
                continue;
            }
            Double filledQuantity = ActionType.BUY.name().equals(userOrder.getAction()) ? userOrder.getFilledQuantity() : -userOrder.getFilledQuantity();

            TradeAllocation allocation = TradeAllocation.builder()
                    .type(TradeAllocationType.USER.name())
                    .segType(userOrder.getSegType())
                    .secType(userOrder.getSecType())
                    .contractId(contractInfoDTO.getContractId())
                    .symbol(userOrder.getSymbol())
                    .market(userOrder.getMarket())
                    .refId(userOrder.getId())
                    .sourceAccount(userOrder.getDealAccount())
                    .targetAccount(userOrder.getAccount())
                    .portfolioId(userOrder.getPortfolioId())
                    .filledQuantity(filledQuantity)
                    .avgPrice(getAvgPrice(userOrder))
                    .currency(userOrder.getCurrency())
                    .tradeTime(userOrder.getTradeTime())
                    .filledTime(userOrder.getFilledTime())
                    .businessDate(businessDate)
                    .settleDate(settleDate)
                    .navDate(navDate)
                    .status(TradeAllocationStatus.PENDING.name())
                    .source(userOrder.getSource())
                    .updatedAt(DateTime.now())
                    .createdAt(DateTime.now())
                    .build();
            allocationList.add(allocation);
        }

        return allocationList;
    }

    protected Double getAvgPrice(UserOrder userOrder) {
        return userOrder.getAvgPrice();
    }

    protected TradeAllocation buildTraderAllocation(String traderAccount, ContractInfoDTO contractInfoDTO) {
        Double userFilledQuantity = userOrderList.stream()
                .filter(item -> item.getFilledQuantity() != null)
                .map(UserOrder::getFilledQuantity)
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();
        Double restQuantity = BigDecimalUtil.subtract(aggregationOrder.getFilledQuantity(), userFilledQuantity)
                .doubleValue();
        if (BigDecimalUtil.isNotEqual(restQuantity, 0.0)) {
            return TradeAllocation.builder()
                    .type(TradeAllocationType.TRADER.name())
                    .segType(aggregationOrder.getSegType())
                    .secType(aggregationOrder.getSecType())
                    .contractId(contractInfoDTO.getContractId())
                    .symbol(aggregationOrder.getSymbol())
                    .market(aggregationOrder.getMarket())
                    .refId(aggregationOrder.getId())
                    .sourceAccount(aggregationOrder.getDealAccount())
                    .targetAccount(traderAccount)
                    .filledQuantity(restQuantity)
                    .avgPrice(aggregationOrder.getAvgPrice())
                    .currency(aggregationOrder.getCurrency())
                    .tradeTime(aggregationOrder.getCreatedAt())
                    .filledTime(filledTime)
                    .businessDate(businessDate)
                    .settleDate(settleDate)
                    .navDate(navDate)
                    .status(TradeAllocationStatus.PENDING.name())
                    .source(aggregationOrder.getSource())
                    .updatedAt(DateTime.now())
                    .createdAt(DateTime.now())
                    .build();
        }
        return null;
    }

    public TradeAllocation buildBreakAllocation(List<TradeAllocation> userTradeAllocation,
                                                TradeAllocation traderAllocation, Long glAccount) {
        double userAllocationAmount = 0;
        if (CollectionUtils.isNotEmpty(userTradeAllocation)) {
            userAllocationAmount = userTradeAllocation.stream()
                    .filter(item -> item.getFilledQuantity() != null && item.getAvgPrice() != null)
                    .map(item -> BigDecimalUtil.multiply(item.getFilledQuantity(), item.getAvgPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .doubleValue();
        }
        double totalAllocationAmount = userAllocationAmount;
        if (traderAllocation != null) {
            totalAllocationAmount = BigDecimalUtil.add(userAllocationAmount,
                        BigDecimalUtil.multiply(traderAllocation.getFilledQuantity(),
                        traderAllocation.getAvgPrice()).doubleValue()).doubleValue();
        }
        double filledAmount = aggregationOrder.getFilledAmount();
        double breakAmount = BigDecimalUtil.subtract(totalAllocationAmount, filledAmount).doubleValue();
        if (BigDecimalUtil.isMoreThan(breakAmount, 0.0)) {
            return TradeAllocation.builder()
            .type(TradeAllocationType.BREAK.name())
            .segType(aggregationOrder.getSegType())
            .secType(aggregationOrder.getSecType())
            .symbol(aggregationOrder.getSymbol())
            .market(aggregationOrder.getMarket())
            .refId(aggregationOrder.getId())
            .sourceAccount(aggregationOrder.getDealAccount())
            .targetAccount(glAccount.toString())
            .filledQuantity(1.0)
            .avgPrice(-breakAmount)
            .currency(aggregationOrder.getCurrency())
            .tradeTime(aggregationOrder.getCreatedAt())
            .filledTime(filledTime)
            .businessDate(businessDate)
            .settleDate(settleDate)
            .navDate(navDate)
            .status(TradeAllocationStatus.PENDING.name())
            .source(aggregationOrder.getSource())
            .updatedAt(DateTime.now())
            .createdAt(DateTime.now())
            .build();
        }
        return null;
    }

    protected int getPricePrecision(List<AggregationExecutionOrder> executionOrderList) {
        return executionOrderList.stream()
                .map(AggregationExecutionOrder::getAvgPrice)
                .map(BigDecimalUtil::scale)
                .max(Integer::compareTo)
                .orElse(2);
    }

    public Boolean calculateBreak() {
        return true;
    }
}
