package com.tigerbrokers.oae.allocation.aggregation.valobj;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tigerbrokers.hermes.utils.TimesUtil;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.entity.util.RefIdUtil;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@Data
@Builder
public class AllocationReportDataValObj {

    @ExcelProperty("source_account_id")
    private String sourceAccountId;

    @ExcelProperty("target_account_id")
    private String targetAccountId;

    @ExcelProperty("seg_type")
    private String segType;

    @ExcelProperty("contract_id")
    private Long contractId;

    @ExcelProperty("ref_id")
    private String refId;

    @ExcelProperty("quantity")
    private Double quantity;

    @ExcelProperty("filled_price")
    private Double filledPrice;

    @ExcelProperty("transaction_time")
    private String transactionTime;

    @ExcelProperty("business_date")
    private String businessDate;

    public static AllocationReportDataValObj create(TradeAllocation tradeAllocation, UserOrder userOrder) {
        Long dividendId = TradeAllocationType.USER.name().equals(tradeAllocation.getType()) &&
                BusinessSource.DRIP.name().equals(tradeAllocation.getSource())
                && userOrder != null ? userOrder.getDividendId() : null;
        return AllocationReportDataValObj.builder()
                .sourceAccountId(tradeAllocation.getSourceAccount())
                .targetAccountId(tradeAllocation.getTargetAccount())
                .segType(tradeAllocation.getSegType())
                .contractId(tradeAllocation.getContractId())
                .refId(RefIdUtil.getAllocationRefId(tradeAllocation.getRefId(), dividendId, tradeAllocation.getType(), tradeAllocation.getSource()))
                .quantity(tradeAllocation.getFilledQuantity())
                .filledPrice(tradeAllocation.getAvgPrice())
                .transactionTime(String.valueOf(tradeAllocation.getTradeTime().getTime()))
                .businessDate(TimesUtil.printCommonDate(tradeAllocation.getTradeTime().getTime()))
                .build();
    }
}
