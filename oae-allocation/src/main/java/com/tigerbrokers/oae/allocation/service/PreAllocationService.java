package com.tigerbrokers.oae.allocation.service;

import com.alibaba.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.alpha.ledger.thrift.entity.ClearingAccountInfo;
import com.tigerbrokers.oae.allocation.aggregation.PreAllocationAggregate;
import com.tigerbrokers.oae.allocation.aggregation.PreAllocationAggregateBuilder;
import com.tigerbrokers.oae.allocation.repository.AllocationRepository;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.facade.ContractRpcFacade;
import com.tigerbrokers.oae.facade.AccountFacade;
import com.tigerbrokers.oae.facade.context.ContractFacadeContext;
import com.tigerbrokers.oae.facade.context.QueryAccountFacadeContext;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.AccountDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Slf4j
@Service
public class PreAllocationService {

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private AllocationRepository allocationRepository;

    @Autowired
    private AccountFacade accountFacade;

    @Autowired
    private ContractRpcFacade contractRpcFacade;

    @Autowired
    private AlertService alertService;

    /**
     * 预分配，分配计算
     */
    public boolean processPreAllocation(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        List<AggregationOrder> aggregationOrderList = allocationRepository.getNewAggregationOrderList(businessSource, market, secType);
        boolean result = true;
        if (CollectionUtils.isEmpty(aggregationOrderList)) {
            return result;
        }
        for (AggregationOrder aggregationOrder : aggregationOrderList) {
            result = result && ((PreAllocationService) AopContext.currentProxy()).preAllocation(aggregationOrder);
        }
        return result;
    }

    @Transactional(value = "oaeTransactionManager", rollbackFor = Exception.class)
    public boolean preAllocation(AggregationOrder aggregationOrder) {
        List<AggregationExecutionOrder> executionOrderList = allocationRepository
                .getExecutionOrderList(aggregationOrder.getId());
        PreAllocationAggregate preAllocationAggregate = new PreAllocationAggregateBuilder(aggregationOrder,
                executionOrderList).getPreAllocationAggregate();
        if (BooleanUtils.isTrue(preAllocationAggregate.canAllocation())) {
            Pair<ContractInfoDTO, AccountDTO> contractAndAccount = getContractAndAccount(aggregationOrder, preAllocationAggregate.calculateBreak());
            if (contractAndAccount == null) {
                String errorMsg = String.format(
                        "pre allocation, no contract or trader account data: AggregationOrderId-%s",
                        aggregationOrder.getId());
                alertService.sendMsg(errorMsg);
                return false;
            }
            ContractInfoDTO contractInfoDTO = contractAndAccount.getLeft();
            AccountDTO accountDto = contractAndAccount.getRight();
            List<UserOrder> userOrderList = allocationRepository.getUserOrderList(aggregationOrder.getId());
            // 预分配处理
            preAllocationAggregate.preAllocation(userOrderList, contractInfoDTO);
            // 分配结果更新
            allocationRepository.update(preAllocationAggregate.getAggregationOrder());
            allocationRepository.update(preAllocationAggregate.getUserOrderList());
            if (BooleanUtils.isTrue(preAllocationAggregate.isFilled())) {
                // 插入Allocation数据
                List<TradeAllocation> tradeAllocationList = preAllocationAggregate.buildAllocationList(accountDto,
                        contractInfoDTO);
                allocationRepository.insert(tradeAllocationList);
            }
            // 资产解锁标记
            List<Long> unLockUserOrderIds = preAllocationAggregate.getUnLockUserOrderIds();
            if (CollectionUtils.isNotEmpty(unLockUserOrderIds)) {
                lockedAssetService.unlockPending(unLockUserOrderIds, LockStep.PLACE_ORDER);
            }
            log.info("finished pre allocation order: AggregationOrderId-{}", aggregationOrder.getId());
            return true;
        } else {
            if (BooleanUtils.isTrue(preAllocationAggregate.needAllocateSameDay())) {
                String errorMsg = String.format("pre allocation, exist unfinished orders: AggregationOrderId-%s", aggregationOrder.getId());
                log.info(errorMsg);
                alertService.sendMsg(errorMsg);
                return false;
            } else {
                return true;
            }
        }
    }

    public void patchBuildBreakAllocation(List<Long> aggregationIdList) {
        for (Long aggregationId : aggregationIdList) {
            AggregationOrder aggregationOrder = allocationRepository.getAggregationOrder(aggregationId);
            if (aggregationOrder == null) {
                log.error("calBreak error: aggregationId-%s", aggregationId);
                continue;
            }
            List<AggregationExecutionOrder> executionOrderList = allocationRepository
                    .getExecutionOrderList(aggregationOrder.getId());
            PreAllocationAggregate preAllocationAggregate = new PreAllocationAggregateBuilder(aggregationOrder,
                    executionOrderList).getPreAllocationAggregate();
            if (BooleanUtils.isTrue(preAllocationAggregate.canAllocation())) {
                List<TradeAllocation> allocationList = allocationRepository.getTradeAllocationList(aggregationId, aggregationOrder.getMarket(), aggregationOrder.getSource()); 
                if (CollectionUtils.isEmpty(allocationList)) {
                    log.info("calBreak, no allocation data: aggregationId-{}", aggregationId);
                    continue;
                }
                Pair<List<TradeAllocation>, List<TradeAllocation>> pair = splitAllocationList(allocationList);
                List<TradeAllocation> userTradeAllocationList = pair.getLeft();
                List<TradeAllocation> traderTradeAllocationList = pair.getRight();
                TradeAllocation traderAllocation = null;
                Pair<ContractInfoDTO, AccountDTO> contractAndAccount = getContractAndAccount(aggregationOrder, preAllocationAggregate.calculateBreak());
                if (contractAndAccount == null) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(traderTradeAllocationList)) {
                    traderAllocation = traderTradeAllocationList.get(0);
                }

                TradeAllocation breakTradeAllocation =
                        preAllocationAggregate.buildBreakAllocation(userTradeAllocationList, traderAllocation,
                                contractAndAccount.getRight().getGlAccountId());
                if (breakTradeAllocation == null) {
                    log.info("calBreak, no break allocation data: aggregationId-{}", aggregationId);
                    continue;
                }
                allocationRepository.insert(Lists.newArrayList(breakTradeAllocation));
                log.info("calBreak, finished: aggregationId-{}", aggregationId);
            }
        }
    }

    private Pair<List<TradeAllocation>, List<TradeAllocation>> splitAllocationList(List<TradeAllocation> allocationList) {
        List<TradeAllocation> userTradeAllocationList = new ArrayList<>();
        List<TradeAllocation> traderTradeAllocationList = new ArrayList<>();
        for (TradeAllocation allocation : allocationList) {
            if (TradeAllocationType.USER.name().equals(allocation.getType())) {
                userTradeAllocationList.add(allocation);
            } else if (TradeAllocationType.TRADER.name().equals(allocation.getType())) {
                traderTradeAllocationList.add(allocation);
            }
        }
        return Pair.of(userTradeAllocationList, traderTradeAllocationList);
    }

    private Pair<ContractInfoDTO, AccountDTO> getContractAndAccount(AggregationOrder aggregationOrder, Boolean calculateBreak) {
        ContractInfoDTO contractInfoDTO = contractRpcFacade.queryContractInfo(
                new ContractFacadeContext(aggregationOrder.getSymbol()));
        QueryAccountFacadeContext context = new QueryAccountFacadeContext(aggregationOrder.getDealAccount(), contractInfoDTO,
                aggregationOrder.getSource());
        AccountDTO accountDto = accountFacade.queryAccount(context);
        if (contractInfoDTO == null || accountDto == null) {
            log.error("pre allocation, no contract or trader account data: AggregationOrderId-%s, ContractInfoDto-%s, RspAccountDTO-%s",
                    aggregationOrder.getId(), contractInfoDTO, accountDto);
            return null;
        }
        if (calculateBreak) {
            ClearingAccountInfo clearingAccountInfo = accountFacade.queryGeneralLedgerAccount(context);
            if (Objects.isNull(clearingAccountInfo)) {
                log.error("pre allocation, no break account data: context:{}", context);
                return null;
            }
            accountDto.setGlAccountId(clearingAccountInfo.getGlAccountId());
        }
        return Pair.of(contractInfoDTO, accountDto);
    }
}
