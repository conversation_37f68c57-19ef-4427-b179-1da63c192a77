package com.tigerbrokers.oae.allocation.service;

import com.google.api.client.util.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.alpha.ledger.thrift.entity.ClearingAccountInfo;
import com.tigerbrokers.alpha.ledger.thrift.entity.CrossAccountExtraInfo;
import com.tigerbrokers.oae.allocation.aggregation.AllocationAggregate;
import com.tigerbrokers.oae.allocation.repository.AllocationRepository;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.TradeAllocationStatus;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.facade.AccountFacade;
import com.tigerbrokers.oae.facade.AllocationFacade;
import com.tigerbrokers.oae.facade.ContractRpcFacade;
import com.tigerbrokers.oae.facade.context.AllocationFacadeContext;
import com.tigerbrokers.oae.facade.context.CashTransactionFacadeContext;
import com.tigerbrokers.oae.facade.context.ContractFacadeContext;
import com.tigerbrokers.oae.facade.context.QueryAccountFacadeContext;
import com.tigerbrokers.oae.facade.dto.AllocationFacadeDTO;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import com.tigerbrokers.oae.trade.repository.LockedAssetRepository;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.oae.trade.service.LockedAssetService;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@Service
public class AllocationService {

    @Autowired
    private AllocationRepository allocationRepository;

    @Autowired
    private AllocationFacade allocationFacade;

    @Autowired
    private AlertService alertService;

    @Autowired
    private LockedAssetRepository lockedAssetRepository;

    @Autowired
    private UserOrderRepository userOrderRepository;

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private ContractRpcFacade contractRpcFacade;

    @Autowired
    private AccountFacade accountFacade;

    /**
     * 调用后台账本执行分配操作
     */
    public void processAllocation(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        List<TradeAllocation> allocationList = allocationRepository.getPendingTradeAllocationList(businessSource, market, secType);
        if (CollectionUtils.isEmpty(allocationList)) {
            log.info("no trade allocation: businessSource-{}", businessSource);
            return;
        }
        processAllocation(allocationList);
    }

    private void processAllocation(List<TradeAllocation> allocationList) {
        if (CollectionUtils.isNotEmpty(allocationList)) {
            Map<String, List<TradeAllocation>> accountAllocationMap = allocationList.stream()
                    .collect(Collectors.groupingBy(TradeAllocation::getTargetAccount));
            for (Map.Entry<String, List<TradeAllocation>> accountEntry : accountAllocationMap.entrySet()) {
                String targetAccount = accountEntry.getKey();
                List<TradeAllocation> accountAllocationList = accountEntry.getValue();
                Map<String, List<TradeAllocation>> sourceAllocationMap = accountAllocationList.stream()
                        .collect(Collectors.groupingBy(TradeAllocation::getSource));
                for (Map.Entry<String, List<TradeAllocation>> sourceEntry : sourceAllocationMap.entrySet()) {
                    try {
                        doAllocation(sourceEntry.getKey(), targetAccount, sourceEntry.getValue());
                    } catch (Exception e) {
                        List<Long> allocationIdList =  sourceEntry.getValue().stream().map(TradeAllocation::getId).collect(Collectors.toList());
                        String errorMsg = String.format("allocation to ledger fail: targetAccount-%s, businessSource-%s, allocationIdList-%s",
                                targetAccount,  sourceEntry.getKey(), allocationIdList);
                        log.error(errorMsg, e);
                        alertService.sendMsg(errorMsg);
                    }
                }
            }
        }
    }

    private void doAllocation(String businessSource, String targetAccount, List<TradeAllocation> sourceAllocationList) {
        List<Long> userOrderIds = sourceAllocationList.stream()
                .filter(item -> TradeAllocationType.USER.name().equals(item.getType()))
                .map(TradeAllocation::getRefId).collect(Collectors.toList());
        Map<Long, LockedAsset> lockedAssetMap = lockedAssetRepository.getMapByUserOrderIds(userOrderIds, LockStep.PLACE_ORDER);
        Map<Long, UserOrder> userOrderMap = userOrderRepository.getUserOrderMapByIds(userOrderIds);

        //执行订单map
        Map<Long, List<AggregationExecutionOrder>> executionOrderMap = getExecutionOrderMap(userOrderMap, sourceAllocationList);

        List<AllocationAggregate> allocationAggregates = sourceAllocationList.stream()
                .map(tradeAllocation -> new AllocationAggregate(tradeAllocation, lockedAssetMap.get(tradeAllocation.getRefId()), userOrderMap.get(tradeAllocation.getRefId())))
                .collect(Collectors.toList());
        log.info("allocationAggregates:{}", allocationAggregates);

        //DRIP FUND BUY才有settleLockAssetContexts,settleLockedAssetMap
        List<LockAssetContext> settleLockAssetContexts = allocationAggregates.stream().map(AllocationAggregate::getLockedPositionForAllocation)
                .filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, LockedAsset> settleLockedAssetMap = settleLockAssetContexts.stream()
                    .map(lockAssetContext -> lockedAssetService.newPendingLockAsset(lockAssetContext))
                    .collect(Collectors.toMap(LockedAsset::getUserOrderId, Function.identity()));

        //drip fund buy cashTransaction
        List<CashTransactionFacadeContext> dripCashTrans = allocationAggregates.stream().map(AllocationAggregate::getDripCashTransaction)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, CashTransactionFacadeContext> dripCashTransactionMap = dripCashTrans.stream()
                .map(this::constructCashTransaction)
                .collect(Collectors.toMap(CashTransactionFacadeContext::getUserOrderId, Function.identity()));

        List<AllocationFacadeContext> allocationFacadeContextList = allocationAggregates.stream()
                .map(allo -> AllocationFacadeContext.create(allo.getTradeAllocation(), allo.getLockedAsset(),
                        settleLockedAssetMap.get(allo.getUserOrderId()), userOrderMap.get(allo.getUserOrderId()), executionOrderMap.get(allo.getTradeAllocation().getRefId()),
                        dripCashTransactionMap.get(allo.getUserOrderId())))
                .collect(Collectors.toList());

        AllocationFacadeDTO allocationFacadeDTO = allocationFacade.allocation(allocationFacadeContextList, businessSource);
        if (BooleanUtils.isTrue(allocationFacadeDTO.getIsSucc())) {
            //订单结算
            allocationRepository.batchUpdateTradeAllocationStatus(sourceAllocationList, TradeAllocationStatus.SUCC);
            //下单资金&持仓 解锁
            lockedAssetRepository.batchUpdateUnlockedByUserOrderIdAndStep(userOrderIds, LockStep.PLACE_ORDER);
            //结算持仓锁定
            lockedAssetRepository.batchUpdateLocked(settleLockedAssetMap.values());
        } else {
            List<Long> allocationIdList = sourceAllocationList.stream().map(TradeAllocation::getId).collect(Collectors.toList());
            String errorMsg = String.format("allocation to ledger fail: targetAccount-%s, businessSource-%s, allocationIdList-%s",
                    targetAccount, businessSource, allocationIdList);
            log.error(errorMsg);
            alertService.sendMsg(errorMsg);
        }
    }

    private Map<Long, List<AggregationExecutionOrder>> getExecutionOrderMap(Map<Long, UserOrder> userOrderMap, List<TradeAllocation> sourceAllocationList) {
        //USER
        List<Long> aggregationIds = userOrderMap.values().stream().map(UserOrder::getAggregationId).distinct().collect(Collectors.toList());
        //Trader+break
        List<Long> traderAggregationIds = sourceAllocationList.stream()
                .filter(item -> !TradeAllocationType.USER.name().equals(item.getType()))
                .map(TradeAllocation::getRefId).collect(Collectors.toList());
        aggregationIds.addAll(traderAggregationIds);

        Map<Long, List<AggregationExecutionOrder>> map = allocationRepository.getExecutionOrderList(aggregationIds)
                .stream().filter(item -> item.getFilledQuantity() != null && item.getAvgPrice() != null)
                .collect(Collectors.groupingBy(AggregationExecutionOrder::getAggregationId));

        //大订单 key=tradeAllocation.refId
        //USER
        Map<Long, List<AggregationExecutionOrder>> executionOrderMap = userOrderMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                e -> map.getOrDefault(e.getValue().getAggregationId(), Lists.newArrayList())));
        //Trader+break
        executionOrderMap.putAll(sourceAllocationList.stream().filter( t -> !TradeAllocationType.USER.name().equals(t.getType()))
                .collect(Collectors.toMap(TradeAllocation::getRefId, t -> map.getOrDefault(t.getRefId(), Lists.newArrayList()))));

        return executionOrderMap;
    }

    private CashTransactionFacadeContext constructCashTransaction(CashTransactionFacadeContext cashContext) {
        ContractInfoDTO contractInfoDTO = contractRpcFacade.queryContractInfo(new ContractFacadeContext(cashContext.getSymbol()));
        QueryAccountFacadeContext context = new QueryAccountFacadeContext(cashContext.getAccount(), contractInfoDTO, cashContext.getBusinessSource().name());
        ClearingAccountInfo clearingAccountInfo = accountFacade.queryGeneralLedgerAccount(context);
        if (clearingAccountInfo != null) {
            CrossAccountExtraInfo extraInfo = new CrossAccountExtraInfo.Builder()
                    .clearingBroker(clearingAccountInfo.getClearingBroker())
                    .omnibus(clearingAccountInfo.getClearingAccount())
                    .build();
            cashContext.setExtraInfo(extraInfo);
        } else {
            log.error("constructCashTransaction clearingAccountInfo null, account:{}", cashContext.getAccount());
        }
        return cashContext;
    }

    public void processAllocationById(List<Long> allocationIdList) {
        List<TradeAllocation> tradeAllocationList = allocationRepository.getTradeAllocationList(allocationIdList);
        processAllocation(tradeAllocationList);
    }

}
