package com.tigerbrokers.oae.allocation.aggregation;

import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.context.CashTransactionFacadeContext;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import lombok.Data;

@Data
public class AllocationAggregate {

    private TradeAllocation tradeAllocation;

    private LockedAsset lockedAsset;

    private UserOrder userOrder;

    public AllocationAggregate(TradeAllocation tradeAllocation, LockedAsset lockedAsset, UserOrder userOrder) {
        this.tradeAllocation = tradeAllocation;
        this.lockedAsset = lockedAsset;
        this.userOrder = userOrder;
    }

    public Long getUserOrderId() {
        return tradeAllocation.getRefId();
    }

    //基金买入分配时需要锁定持仓，等待SuperOrder结算时解锁
    public boolean needLockPositionForAllocation() {
        return SecType.FUND.name().equals(tradeAllocation.getSecType())
                && TradeAllocationType.USER.name().equals(tradeAllocation.getType())
                && tradeAllocation.getFilledQuantity() > 0;
    }


    public LockAssetContext getLockedPositionForAllocation() {
        if (needLockPositionForAllocation()) {
            return LockAssetContext.builder()
                    .userOrderId(tradeAllocation.getRefId())
                    .account(tradeAllocation.getTargetAccount())
                    .portfolioId(tradeAllocation.getPortfolioId())
                    .action(ActionType.SELL.name())
                    .quantity(tradeAllocation.getFilledQuantity())
                    .currency(tradeAllocation.getCurrency())
                    .market(tradeAllocation.getMarket())
                    .contractId(tradeAllocation.getContractId())
                    .segType(tradeAllocation.getSegType())
                    .secType(tradeAllocation.getSecType())
                    .source(tradeAllocation.getSource())
                    .lockStep(LockStep.SETTLE.name())
                    .build();
        }
        return null;
    }

    //drip fund buy 0成本
    public boolean needDripCashTransaction(TradeAllocation tradeAllocation, UserOrder userOrder) {
        return TradeAllocationType.USER.name().equals(tradeAllocation.getType())
                && SecType.FUND.name().equals(tradeAllocation.getSecType())
                && BusinessSource.DRIP.name().equals(tradeAllocation.getSource())
                && userOrder != null && BigDecimalUtil.isMoreThan(userOrder.getDividendId(), 0)
                && ActionType.BUY.name().equals(userOrder.getAction());
    }

    public CashTransactionFacadeContext getDripCashTransaction() {
        if (needDripCashTransaction(tradeAllocation, userOrder)) {
            return CashTransactionFacadeContext.createDripCashContext(tradeAllocation, userOrder);
        }
        return null;
    }
}
