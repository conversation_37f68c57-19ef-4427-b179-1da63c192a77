package com.tigerbrokers.oae.allocation.aggregation;

import com.tigerbrokers.alpha.commons.data.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;

import java.util.List;

public class PreAllocationAggregateBuilder {

    private AggregationOrder aggregationOrder;

    private List<AggregationExecutionOrder> executionOrderList;

    private BusinessSource businessSource;

    public PreAllocationAggregateBuilder(AggregationOrder aggregationOrder, List<AggregationExecutionOrder> executionOrderList) {
        this.aggregationOrder = aggregationOrder;
        this.executionOrderList = executionOrderList;
        this.businessSource = BusinessSource.valueOf(aggregationOrder.getSource());
    }

    public PreAllocationAggregate getPreAllocationAggregate(){
        switch (businessSource) {
            case RSP:
                return new RSPPreAllocationAggregate(aggregationOrder, executionOrderList);
            case DRIP:
                if (aggregationOrder.getSecType().equals(SecType.FUND.name())) {
                    return new DRIPPreAllocationAggregate(aggregationOrder, executionOrderList);
                } else if (FacadeUtils.isUSDrip(aggregationOrder.getSource(), aggregationOrder.getSecType(), aggregationOrder.getMarket())) {
                    return new USDRIPPreAllocationAggregate(aggregationOrder, executionOrderList);
                }
            default:
                return null;
        }
    }

}
