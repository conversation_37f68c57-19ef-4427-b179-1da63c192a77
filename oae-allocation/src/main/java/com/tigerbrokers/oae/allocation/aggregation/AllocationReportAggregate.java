package com.tigerbrokers.oae.allocation.aggregation;

import com.alibaba.excel.EasyExcel;
import com.tigerbrokers.hermes.utils.TimesUtil;
import com.tigerbrokers.oae.allocation.aggregation.valobj.AllocationReportDataValObj;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import org.joda.time.DateTime;
import repackaged.com.arakelian.core.com.google.common.collect.Lists;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
public class AllocationReportAggregate {

    private static final String REPORT_FILE_NAME = "post-trade-allocation-%s-%s.xlsx";

    private List<TradeAllocation> tradeAllocationList;

    private Map<Long, UserOrder> userOrderMap;

    public AllocationReportAggregate(List<TradeAllocation> tradeAllocationList, Map<Long, UserOrder> userOrderMap) {
        this.tradeAllocationList = tradeAllocationList;
        this.userOrderMap = userOrderMap;
    }

    public String createReportFile(DateTime businessDate, BusinessSource source) {
        String fileName = System.getProperty("user.dir") + File.separator + "data" + File.separator
                + String.format(REPORT_FILE_NAME, source.name().toLowerCase(), TimesUtil.printCommonDate(businessDate.getMillis()));
        List<AllocationReportDataValObj> data = getReportData();
        EasyExcel.write(fileName, AllocationReportDataValObj.class).sheet("sheet1").doWrite(data);

        return fileName;
    }

    private List<AllocationReportDataValObj> getReportData() {
        List<AllocationReportDataValObj> reportDataList = Lists.newArrayList();
        for (TradeAllocation tradeAllocation : tradeAllocationList) {
            if (isAllocationInvolvingPosition(tradeAllocation.getType())) {
                AllocationReportDataValObj reportData = AllocationReportDataValObj.create(tradeAllocation, userOrderMap.get(tradeAllocation.getRefId()));
                reportDataList.add(reportData);
            }
        }

        return reportDataList;
    }

    /**
     * 判断分配是否涉及持仓
     * @param allocationType
     * @return
     */
    private boolean isAllocationInvolvingPosition(String allocationType) {
        return !TradeAllocationType.BREAK.equals(TradeAllocationType.getTradeAllocationType(allocationType));
    }
}
