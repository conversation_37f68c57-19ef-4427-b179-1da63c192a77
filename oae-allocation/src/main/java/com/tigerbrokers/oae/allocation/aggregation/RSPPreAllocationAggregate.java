package com.tigerbrokers.oae.allocation.aggregation;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class RSPPreAllocationAggregate extends PreAllocationAggregate {
    public RSPPreAllocationAggregate(AggregationOrder aggregationOrder, List<AggregationExecutionOrder> executionOrderList) {
        super(aggregationOrder, executionOrderList);
    }
}
