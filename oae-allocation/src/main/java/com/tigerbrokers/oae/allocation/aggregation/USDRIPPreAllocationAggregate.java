package com.tigerbrokers.oae.allocation.aggregation;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class USDRIPPreAllocationAggregate extends PreAllocationAggregate {
    public USDRIPPreAllocationAggregate(AggregationOrder aggregationOrder, List<AggregationExecutionOrder> executionOrderList) {
        super(aggregationOrder, executionOrderList);
    }

    @Override
    protected String buildUserErrorMsg() {
        Optional<AggregationExecutionOrder> rejectExecutionOrder = executionOrderList.stream().filter(e -> StringUtils.isNotBlank(e.getErrorMsg())).findFirst();
        return rejectExecutionOrder.isPresent() ? rejectExecutionOrder.get().getErrorMsg() : super.buildUserErrorMsg();
    }
}
