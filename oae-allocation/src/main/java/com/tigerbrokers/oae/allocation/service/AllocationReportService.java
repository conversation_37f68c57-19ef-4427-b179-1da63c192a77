package com.tigerbrokers.oae.allocation.service;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.allocation.aggregation.AllocationReportAggregate;
import com.tigerbrokers.oae.allocation.repository.AllocationRepository;
import com.tigerbrokers.oae.entity.consts.AggregationOrderStatus;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.TradeAllocationType;
import com.tigerbrokers.oae.facade.AllocationReportFtpFacade;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@Slf4j
@Service
public class AllocationReportService {

    private static final List<String> AGGREGATION_ORDER_NOT_FINISH_STATUS_LIST =
            Lists.newArrayList(AggregationOrderStatus.NEW.name(), AggregationOrderStatus.WAITING_ODD.name());

    @Autowired
    private AllocationRepository allocationRepository;

    @Autowired
    private AllocationReportFtpFacade allocationReportFtpFacade;

    @Autowired
    private UserOrderRepository userOrderRepository;

    /**
     * 后台账本分配对账文件
     */
    public void processAllocationReport(CommonConsts.StockMarket stockMarket, BusinessSource source, SecType secType, DateTime businessDate) {
        List<TradeAllocation> tradeAllocationList = allocationRepository.getTradeAllocationList(stockMarket, source, secType, businessDate);
        List<Long> userOrderIds = tradeAllocationList.stream()
                .filter(item -> TradeAllocationType.USER.name().equals(item.getType()))
                .filter(item -> BusinessSource.DRIP.name().equals(item.getSource()))
                .map(TradeAllocation::getRefId).collect(Collectors.toList());
        Map<Long, UserOrder> userOrderMap = userOrderRepository.getUserOrderMapByIds(userOrderIds);
        if (CollectionUtils.isNotEmpty(tradeAllocationList)) {
            AllocationReportAggregate aggregate = new AllocationReportAggregate(tradeAllocationList, userOrderMap);
            String filename = aggregate.createReportFile(businessDate, source);
            allocationReportFtpFacade.uploadReport(filename);
        }
    }

    public boolean canProcess(BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        List<AggregationOrder> notFinishOrder = allocationRepository.getAggregationOrderList(
                AGGREGATION_ORDER_NOT_FINISH_STATUS_LIST, source, market, secType);
        List<TradeAllocation> unusualTradeAllocationList = allocationRepository.getUnusualTradeAllocationList(market, source, secType);
        return notFinishOrder.isEmpty() && unusualTradeAllocationList.isEmpty();
    }

}
