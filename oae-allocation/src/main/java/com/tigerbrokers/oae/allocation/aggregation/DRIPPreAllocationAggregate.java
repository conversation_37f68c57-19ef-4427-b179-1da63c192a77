package com.tigerbrokers.oae.allocation.aggregation;

import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class DRIPPreAllocationAggregate extends PreAllocationAggregate {
    public DRIPPreAllocationAggregate(AggregationOrder aggregationOrder, List<AggregationExecutionOrder> executionOrderList) {
        super(aggregationOrder, executionOrderList);
    }

    @Override
    public TradeAllocation buildBreakAllocation(List<TradeAllocation> userTradeAllocation, TradeAllocation traderAllocation, Long glAccount) {
        return null;
    }

    @Override
    protected Double getAvgPrice(UserOrder userOrder) {
        //DRIP fund 买单成本记为0
        return ActionType.BUY.name().equals(userOrder.getAction()) ? 0D : userOrder.getAvgPrice();
    }

    @Override
    public Boolean calculateBreak() {
        return false;
    }

    @Override
    protected String buildUserErrorMsg() {
        Optional<AggregationExecutionOrder> rejectExecutionOrder = executionOrderList.stream().filter(e -> StringUtils.isNotBlank(e.getErrorMsg())).findFirst();
        return rejectExecutionOrder.isPresent() ? rejectExecutionOrder.get().getErrorMsg() : super.buildUserErrorMsg();
    }
}
