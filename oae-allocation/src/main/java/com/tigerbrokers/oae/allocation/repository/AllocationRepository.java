package com.tigerbrokers.oae.allocation.repository;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.hermes.utils.TimesUtil;
import com.tigerbrokers.oae.entity.consts.AggregationOrderStatus;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.TradeAllocationStatus;
import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderDao;
import com.tigerbrokers.oae.storage.jdbc.AggregationOrderDao;
import com.tigerbrokers.oae.storage.jdbc.TradeAllocationDao;
import com.tigerbrokers.oae.storage.jdbc.UserOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.*;
import com.tigerbrokers.stock.common.CommonConsts;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Service
public class AllocationRepository {

    @Autowired
    private UserOrderDao userOrderDao;

    @Autowired
    private AggregationOrderDao aggregationOrderDao;

    @Autowired
    private AggregationExecutionOrderDao executionOrderDao;

    @Autowired
    private TradeAllocationDao tradeAllocationDao;

    public List<AggregationOrder> getNewAggregationOrderList(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        return aggregationOrderDao.selectByStatus(Lists.newArrayList(AggregationOrderStatus.NEW.name()), businessSource, market, secType);
    }

    public List<AggregationOrder> getAggregationOrderList(List<String> statusList, BusinessSource businessSource,
                                                          CommonConsts.StockMarket market, SecType secType) {
        return aggregationOrderDao.selectByStatus(statusList, businessSource, market, secType);
    }

    public AggregationOrder getAggregationOrder(Long aggregationId) {
        return aggregationOrderDao.selectById(aggregationId);
    }

    public List<AggregationExecutionOrder> getExecutionOrderList(Long aggregationId) {
        return executionOrderDao.selectByAggregationId(aggregationId);
    }

    public List<AggregationExecutionOrder> getExecutionOrderList(List<Long> aggregationIds) {
        if (CollectionUtils.isEmpty(aggregationIds)) {
            return Lists.newArrayList();
        }
        return executionOrderDao.selectByAggregationIds(aggregationIds);
    }

    public List<UserOrder> getUserOrderList(Long aggregationId) {
        return userOrderDao.selectByAggregationId(aggregationId);
    }

    public void update(AggregationOrder aggregationOrder) {
        aggregationOrder.setUpdatedAt(DateTime.now().toDate());
        aggregationOrderDao.update(aggregationOrder);
    }

    public void update(List<UserOrder> userOrders) {
        userOrders.forEach(item -> item.setUpdatedAt(DateTime.now().toDate()));
        userOrderDao.batchUpdate(userOrders);
    }

    public void insert(List<TradeAllocation> tradeAllocationList) {
        tradeAllocationDao.batchInsert(tradeAllocationList);
    }

    public List<TradeAllocation> getTradeAllocationList(CommonConsts.StockMarket stockMarket, BusinessSource source, SecType secType, DateTime businessDate) {
        Long start = TimesUtil.toDateTimeAtStartOfDay(businessDate.getMillis(), stockMarket.name());
        Long end = TimesUtil.toDateTimeAtStartOfDay(businessDate.plusDays(1).getMillis(), stockMarket.name());
        return tradeAllocationDao.selectByBusinessDate(stockMarket.name(), source.name(), secType.name(), new Date(start), new Date(end));
    }

    public List<TradeAllocation> getUnusualTradeAllocationList(CommonConsts.StockMarket stockMarket, BusinessSource source, SecType secType) {
        return tradeAllocationDao.selectByBusinessDate(stockMarket.name(), source.name(), secType.name(), null, null);
    }

    public List<TradeAllocation> getPendingTradeAllocationList(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        return tradeAllocationDao.selectByStatus(TradeAllocationStatus.PENDING.name(), businessSource, market, secType);
    }

    public List<TradeAllocation> getTradeAllocationList(List<Long> allocationIdList) {
        return tradeAllocationDao.select(allocationIdList);
    }

    public void batchUpdateTradeAllocationStatus(List<TradeAllocation> tradeAllocationList, TradeAllocationStatus status) {
        List<TradeAllocation> updateTradeAllocationList = tradeAllocationList.stream()
                .map(item -> TradeAllocation.builder().id(item.getId()).status(status.name()).build())
                .collect(Collectors.toList());
        tradeAllocationDao.batchUpdate(updateTradeAllocationList);
    }

    public List<TradeAllocation> getTradeAllocationList(Long aggregationId, String market, String source) {
        return tradeAllocationDao.selectAllocationsByAggregationId(aggregationId, market, source);
    }
}
