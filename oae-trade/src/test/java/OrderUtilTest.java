import com.tigerbrokers.oae.trade.util.OrderUtil;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

public class OrderUtilTest {

    @Test
    public void testCalUSUpPrice() {
        Double refPrice = OrderUtil.calUSUpPrice(30.456, 1, 0.01);
        Assert.assertEquals(BigDecimal.valueOf(30.47), BigDecimal.valueOf(refPrice));

        Double refPrice1 = OrderUtil.calUSUpPrice(30.451, 1, 0.01);
        Assert.assertEquals(BigDecimal.valueOf(30.46), BigDecimal.valueOf(refPrice1));

        Double refPrice2 = OrderUtil.calUSUpPrice(0.456, 1, 0.0001);
        Assert.assertEquals(BigDecimal.valueOf(0.4561), BigDecimal.valueOf(refPrice2));
    }
}
