package com.tigerbrokers.oae.trade.service;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.facade.QuoteFacade;
import com.tigerbrokers.oae.facade.dto.ContractDTO;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.trade.repository.ContractInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 行情/标的
 *
 * @author: chentao
 * Created on 28/3/2023
 */
@Slf4j
@Service
public class QuoteService {

    @Autowired
    private QuoteFacade quoteFacade;

    @Autowired
    private ContractInfoRepository contractInfoRepository;

    @Autowired
    private AlertService alertService;

    public ContractDTO getContract(String symbol, String secType) {
        QuotePointDTO realTimeQuote = null;
        Double price = null;
        if (SecType.STK.name().equals(secType)) {
            // RealTimeQuote
            realTimeQuote = quoteFacade.queryRealTimeQuote(symbol);
            if (Objects.isNull(realTimeQuote)) {
                String errorMsg = String.format("symbol-%s no quote data.", symbol);
                return alertAndReturn(errorMsg);
            } else {
                price = realTimeQuote.getClose();
            }
        }
        // TickerSize, LotSize
        ContractInfoDTO contractInfo = contractInfoRepository.getContractInfo(symbol, price);;

        if (Objects.isNull(contractInfo) || Boolean.FALSE.equals(contractInfo.valid())) {
            // 缺少合约行情相关数据
            String errorMsg = String.format("symbol-%s no contract info: ContractInfo-%s", symbol, contractInfo);
            return alertAndReturn(errorMsg);
        }
        return ContractDTO.builder()
                .realTimeQuote(realTimeQuote)
                .contractInfo(contractInfo)
                .build();
    }

    private ContractDTO alertAndReturn(String errorMsg) {
        log.error(errorMsg);
        alertService.sendMsg(errorMsg);
        return null;
    }


}
