package com.tigerbrokers.oae.trade.context;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
@Builder
public class LockAssetContext {

    private Long userOrderId;

    private String action;

    private String account;

    private Long portfolioId;

    private Double amount;

    private Double quantity;

    private String currency;

    private String market;

    private String segType;

    private String secType;

    private Long contractId;

    private String source;

    private String lockStep;

    private String detail;

}
