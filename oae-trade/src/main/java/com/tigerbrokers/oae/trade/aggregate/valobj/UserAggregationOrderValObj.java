package com.tigerbrokers.oae.trade.aggregate.valobj;

import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.oae.entity.consts.AggregationOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionErrorCode;
import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.dto.ExecuteAggregationOrderDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAggregationOrderValObj {

    @Getter
    private List<UserOrder> userOrders;

    @Getter
    private AggregationOrder aggregationOrder;

    private String dealAccount;

    private String symbol;

    private String type;

    private String action;

    private String source;

    public UserAggregationOrderValObj(List<UserOrder> userOrders, AggregationKey aggregationKey) {
        this.userOrders = userOrders;
        this.dealAccount = aggregationKey.getDealAccount();
        this.symbol = aggregationKey.getSymbol();
        this.type = aggregationKey.getOrderType();
        this.action = aggregationKey.getAction();
        this.source = aggregationKey.getSource();
        buildAggregationOrder();
    }

    private void buildAggregationOrder() {
        Double tradeAmount = null;
        Double tradeQuantity = null;
        if (ActionType.BUY.name().equals(action)) {
            tradeAmount = userOrders.stream()
                    .map(UserOrder::getTradeAmount)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .doubleValue();
        } else {
            tradeQuantity = userOrders.stream()
                    .map(UserOrder::getQuantity)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .doubleValue();
        }
        UserOrder defaultItem = userOrders.get(0);
        aggregationOrder = AggregationOrder.builder()
                .tradeAmount(tradeAmount)
                .tradeQuantity(tradeQuantity)
                .symbol(symbol)
                .dealAccount(dealAccount)
                .segType(defaultItem.getSegType())
                .secType(defaultItem.getSecType())
                .market(defaultItem.getMarket())
                .currency(defaultItem.getCurrency())
                .action(action)
                .orderType(type)
                .status(AggregationOrderStatus.NEW.name())
                .source(source)
                .build();
    }

    public List<Long> getUserOrderIds() {
        if (CollectionUtils.isNotEmpty(userOrders)) {
            return userOrders.stream().map(UserOrder::getId).collect(Collectors.toList());
        }
        return null;
    }

    public void updateAfterExecutionFail(ExecuteAggregationOrderDTO executionDTO) {
        if (ExecutionErrorCode.NOT_SATISFY_MINIMIZE_QUANTITY.equals(executionDTO.getErrorCode())) {
            aggregationOrder.setStatus(AggregationOrderStatus.REJECTED.name());
            aggregationOrder.setErrorCode(executionDTO.getErrorCode().name());
            for (UserOrder userOrder : userOrders) {
                userOrder.setStatus(UserOrderStatus.REJECTED.name());
                userOrder.setErrorCode(executionDTO.getErrorCode().name());
            }
        } else if (ExecutionErrorCode.NEED_PLACE_ODD.equals(executionDTO.getErrorCode())) {
            aggregationOrder.setStatus(AggregationOrderStatus.WAITING_ODD.name());
        }
    }

    public void updateAfterOddExecutionFail(ExecuteAggregationOrderDTO executionDTO) {
        if (CollectionUtils.isNotEmpty(userOrders)) {
            aggregationOrder.setStatus(AggregationOrderStatus.REJECTED.name());
            aggregationOrder.setErrorCode(executionDTO.getErrorCode().name());
            for (UserOrder userOrder : userOrders) {
                userOrder.setStatus(UserOrderStatus.REJECTED.name());
                userOrder.setErrorCode(executionDTO.getErrorCode().name());
            }
        } else {
            aggregationOrder.setStatus(AggregationOrderStatus.NEW.name());
        }
    }

}
