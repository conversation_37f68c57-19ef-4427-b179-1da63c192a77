package com.tigerbrokers.oae.trade.kafka;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.JsonObject;
import com.tigerbrokers.brokerage.sdk.common.enums.OrderAttr;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.util.RefIdUtil;
import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.trade.context.ExecutionOrderContext;
import com.tigerbrokers.oae.trade.context.UserOrderRelateTradeContext;
import com.tigerbrokers.oae.trade.kafka.data.PrimeTradeKafka;
import com.tigerbrokers.oae.trade.service.ExecutionService;
import com.tigerbrokers.oae.trade.service.UserOrderService;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @author: chentao
 * Created on 2/2/2023
 */
@Slf4j
@Service
public class PrimeTradeKafkaConsumer {

    private static final String ORDER_SOURCE_TYPE_RSP_CODE = "rsp";

    @Autowired
    private FacadeConfig.PrimeTradeKafkaConfig primeTradeKafkaConfig;

    @Autowired
    private ExecutionService executionService;

    @Autowired
    private UserOrderService userOrderService;

    @KafkaListener(id = "primeTradeEventListener",
            topics = {"#{primeTradeKafkaConfig.tradeTopic}"},
            groupId = "#{primeTradeKafkaConfig.group}",
            containerFactory = KafkaConfiguration.PRIME_TRADE_KAFKA_LISTENER_FACTORY,
            autoStartup = "#{primeTradeKafkaConfig.autoStartup}")
    public void listenTradeMessage(String message) {
        try {
            PrimeTradeKafka orderResp = parseKafkaMsg(message);
            if (Objects.isNull(orderResp)) {
                log.error("orderResp is null, message:{}", message);
                return;
            }
            if (!needHandle(orderResp)) {
                return;
            }
            if (isPostTrade(orderResp)) {
                log.info("sync execution order info: prime OrderResp-{}", JacksonUtil.writeValueAsString(orderResp));
                executionService.syncExecutionOrderInfo(buildExecutionOrderContext(orderResp));
            } else if (isSubOrder(orderResp)) {
                if (RefIdUtil.isTraderSubOrder(orderResp.getExternalId())) {
                    // Trader回收仓位小订单不需要同步
                    return;
                }
                log.info("sync sub order info: prime OrderResp-{}", JSONObject.toJSONString(orderResp));
                userOrderService.syncTradeInfo(buildUserOrderRelateTradeContext(orderResp),
                        JacksonUtil.writeValueAsString(orderResp));
            }
        } catch (Exception e) {
            log.error("listenTradeMessage error kafka msg:{}, msg:{}", message, e.getMessage(), e);
        }
    }

    private PrimeTradeKafka parseKafkaMsg(String message) {
        JsonNode jsonNode = JacksonUtil.parseJsonNode(message);
        if (Objects.isNull(jsonNode)) {
            log.error("jsonNode is null, message:{}", message);
            return null;
        }
        String type = jsonNode.path("type").asText();
        if (StringUtils.isEmpty(type)) {
            log.error("type is null, message:{}", message);
            return null;
        }
        if (!primeTradeKafkaConfig.getTradeMsgType().equals(type)) {
            return null;
        }
        JsonNode dataNode = jsonNode.path("data");
        if (Objects.isNull(dataNode)) {
            return null;
        }
        return PrimeTradeKafka.toPrimeTradeKafka(dataNode.toString());
    }

    private boolean needHandle(PrimeTradeKafka orderResp) {
        String source = orderResp.getSource();
        return source != null && Arrays.stream(BusinessSource.values()).anyMatch(s -> s.name().equalsIgnoreCase(source));
    }

    private ExecutionOrderContext buildExecutionOrderContext(PrimeTradeKafka orderResp) {
        ExecutionOrderContext result = new ExecutionOrderContext();
        result.setId(orderResp.getId());
        result.setPrice(orderResp.getPrice());
        result.setTotalQuantity(orderResp.getTotalQuantity());
        result.setTotalQuantityScale(orderResp.getTotalQuantityScale());
        result.setFilledQuantity(orderResp.getFilledQuantity());
        result.setFilledQuantityScale(orderResp.getFilledQuantityScale());
        result.setAvgFillPrice(orderResp.getAvgFillPrice());
        result.setTotalCashAmount(orderResp.getTotalCashAmount());
        result.setFilledCashAmount(orderResp.getFilledCashAmount());
        result.setStatus(orderResp.getStatus());
        result.setStatusUpdatedAt(orderResp.getStatusUpdatedAt());
        result.setAttrList(orderResp.getAttrList());
        result.setMessage(orderResp.getMessage());
        result.setMessageCode(orderResp.getMessageCode());
        return result;
    }

    private UserOrderRelateTradeContext buildUserOrderRelateTradeContext(PrimeTradeKafka orderResp) {
        return UserOrderRelateTradeContext.builder()
                .userOrderId(Long.valueOf(RefIdUtil.removePrefix(orderResp.getExternalId())))
                .tradeId(String.valueOf(orderResp.getId()))
                .build();
    }

    /**
     * 大订单(执行订单)
     * @param orderResp
     */
    private boolean isPostTrade(PrimeTradeKafka orderResp) {
        List<String> attrs = orderResp.getAttrList();
        return CollectionUtils.isNotEmpty(attrs) && attrs.contains(OrderAttr.SUPER_ORDER.name());
    }

    /**
     * 后台账本分配订单
     * @param orderResp
     */
    private boolean isSubOrder(PrimeTradeKafka orderResp) {
        List<String> attrs = orderResp.getAttrList();
        return CollectionUtils.isNotEmpty(attrs) && attrs.contains(OrderAttr.SUB_ORDER.name());

    }
}
