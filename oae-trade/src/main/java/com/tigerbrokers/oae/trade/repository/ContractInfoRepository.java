package com.tigerbrokers.oae.trade.repository;

import com.tigerbrokers.oae.facade.ContractRpcFacade;
import com.tigerbrokers.oae.facade.context.ContractFacadeContext;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Service
public class ContractInfoRepository {

    @Autowired
    private ContractRpcFacade contractRpcFacade;

    public ContractInfoDTO getContractInfo(String symbol, Double price) {
        if (StringUtils.isEmpty(symbol)) {
            return new ContractInfoDTO();
        }
        BigDecimal decimalPrice = price == null ? null : BigDecimal.valueOf(price);
        ContractFacadeContext context = ContractFacadeContext.builder()
                .symbol(symbol)
                .price(decimalPrice)
                .build();
        ContractInfoDTO contractInfo = contractRpcFacade.queryContractInfo(context);

        return contractInfo != null ? contractInfo : new ContractInfoDTO();
    }
}
