package com.tigerbrokers.oae.trade.aggregate;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionOrderType;
import com.tigerbrokers.oae.entity.consts.ExecutionRecordOrderOperation;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.consts.ExecutionConst;
import com.tigerbrokers.oae.trade.util.OrderUtil;
import lombok.Getter;

/**
 * 人手单聚合
 * <AUTHOR>
 * @date 2023/1/10
 */
public class OddOrderAggregate {

    @Getter
    private AggregationOrder aggregationOrder;

    private ContractInfoDTO contractInfo;

    private QuotePointDTO realTimeQuote;

    private Integer priceUpSize;

    @Getter
    private ExecutionOrderValObj executionOrderValObj;

    public OddOrderAggregate(AggregationOrder aggregationOrder,
                             ContractInfoDTO contractInfo,
                             QuotePointDTO realTimeQuote,
                             Integer priceUpSize) {
        this.aggregationOrder = aggregationOrder;
        this.contractInfo = contractInfo;
        this.realTimeQuote = realTimeQuote;
        this.priceUpSize = priceUpSize;
    }

    public void buildExecutionOrder() {
        // 订单金额
        Double amount = getAmount();
        // 限价
        Double refPrice = calRefPrice();
        // 股数
        Double quantity = getQuantity();

        AggregationExecutionOrder executionOrder = AggregationExecutionOrder.builder()
                .aggregationId(aggregationOrder.getId())
                .dealAccount(aggregationOrder.getDealAccount())
                .executionMaster(aggregationOrder.getExecutionMaster())
                .symbol(aggregationOrder.getSymbol())
                .market(aggregationOrder.getMarket())
                .currency(aggregationOrder.getCurrency())
                .action(aggregationOrder.getAction())
                .totalQuantity(quantity)
                .price(refPrice)
                .orderType(aggregationOrder.getOrderType())
                .segType(aggregationOrder.getSegType())
                .secType(aggregationOrder.getSecType())
                .tradeTime(DateTime.now())
                .status(ExecutionOrderStatus.PENDING.name())
                .type(ExecutionOrderType.ODD.name())
                .source(aggregationOrder.getSource())
                .build();
        AggregationExecutionOrderRecord recordOrder = AggregationExecutionOrderRecord.builder()
                .aggregationId(aggregationOrder.getId())
                .symbol(aggregationOrder.getSymbol())
                .market(aggregationOrder.getMarket())
                .currency(aggregationOrder.getCurrency())
                .action(aggregationOrder.getAction())
                .operation(ExecutionRecordOrderOperation.PLACE.name())
                .amount(amount)
                .refPrice(refPrice)
                .quantity(quantity)
                .lotSizeUnit(getLotSize())
                .status(ExecutionOrderStatus.PENDING.name())
                .build();
        this.executionOrderValObj = ExecutionOrderValObj.builder().executionOrder(executionOrder)
                .recordOrder(recordOrder).build();
    }

    /**
     * 订单金额
     */
    public Double getAmount() {
        return BigDecimalUtil.subtract(aggregationOrder.getTradeAmount(), aggregationOrder.getFilledAmount()).doubleValue();
    }

    /**
     * 人手单数量
     */
    public Double getQuantity() {
        return Math.floor(getAmount() / calRefPrice());
    }

    /**
     * 限价
     */
    private Double calRefPrice() {
        return OrderUtil.calUpPrice(realTimeQuote.getClose(), priceUpSize, contractInfo.getTickerSize());
    }

    /**
     * 每手股数
     */
    private Integer getLotSize() {
        return contractInfo.getLotSize();
    }

}
