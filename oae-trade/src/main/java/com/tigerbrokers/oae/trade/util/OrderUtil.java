package com.tigerbrokers.oae.trade.util;

import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Slf4j
public class OrderUtil {

    public static boolean isBuyAction(String action) {
        return ActionType.BUY.name().equals(action);
    }


    /**
     * 通过金额，计算手数
     */
    public static Double calLotQty(double amount, double price, double lotSize) {
        double quantity = amount / price;
        return Math.floor(quantity / lotSize);
    }

    /**
     * 通过金额，计算订单数量
     */
    public static Double calQuantity(double amount, double price, double lotSize) {
        double lotQty = calLotQty(amount, price, lotSize);
        return BigDecimalUtil.multiply(lotQty, lotSize).doubleValue();
    }

    /**
     * 计算上浮价格
     */
    public static Double calUpPrice(double price, int upSize, double tickerSize) {
        Double upValue = BigDecimalUtil.multiply((double) upSize, tickerSize).doubleValue();
        return BigDecimalUtil.add(price, upValue).doubleValue();
    }

    /**
     * 计算美股上浮价格
     */
    public static Double calUSUpPrice(double price, int upSize, double tickerSize) {
        //行情返回的价格和tick size可能不匹配，根据tick size对价格进行四舍五入处理
        if (BigDecimalUtil.scale(price) > BigDecimalUtil.scale(tickerSize)) {
            log.info("us price {} scale more than tickerSize {} scale", price, tickerSize);
            price = BigDecimal.valueOf(price).setScale(BigDecimalUtil.scale(tickerSize), RoundingMode.HALF_UP).doubleValue();
            log.info("new us price {}", price);
        }
        return OrderUtil.calUpPrice(price, upSize, tickerSize);
    }

    /**
     * 计算美股限价份额
     * @param amount
     * @param price
     * @return
     */
    public static Double calUSOrderQuantity(double amount, double price) {
        return BigDecimalUtil.divide(amount, price, 5, RoundingMode.DOWN).doubleValue();
    }
}
