package com.tigerbrokers.oae.trade.aggregate.modify;

import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.trade.config.TradeConfig;

public class ModifyExecuteOrderAggregateBuilder {

    private Double refAmount;
    private AggregationExecutionOrder executionOrder;
    private BusinessSource businessSource;
    private TradeConfig.OAETradeConfig oaeTradeConfig;

    public ModifyExecuteOrderAggregateBuilder(AggregationExecutionOrder executionOrder, Double refAmount, TradeConfig.OAETradeConfig oaeTradeConfig) {
        this.executionOrder = executionOrder;
        this.refAmount = refAmount;
        this.businessSource = BusinessSource.valueOf(executionOrder.getSource());
        this.oaeTradeConfig = oaeTradeConfig;
    }

    public ModifyExecutionOrderAggregate getExecuteOrderAggregate(){
        switch (businessSource) {
            case RSP:
                return new RSPModifyExecutionOrderAggregate(executionOrder, refAmount, oaeTradeConfig.getPriceUpSize());
            case DRIP:
                if (FacadeUtils.isUSDrip(executionOrder.getSource(), executionOrder.getSecType(), executionOrder.getMarket())) {
                    return new USDRIPModifyExecutionOrderAggregate(executionOrder, refAmount, oaeTradeConfig.getUsPriceUpSize());
                }
            default:
                return null;
        }
    }

}
