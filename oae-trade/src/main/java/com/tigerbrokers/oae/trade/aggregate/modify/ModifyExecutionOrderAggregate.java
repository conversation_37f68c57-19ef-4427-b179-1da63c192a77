package com.tigerbrokers.oae.trade.aggregate.modify;

import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionRecordOrderOperation;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.util.OrderUtil;
import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.HkTradingCalendar;
import com.tigerbrokers.stock.util.HkTradingStatus;
import com.tigerbrokers.stock.util.TimeUtils;
import com.tigerbrokers.stock.util.TradingCalendar;
import lombok.Getter;

import java.util.Objects;

/**
 * 执行订单未成交
 * 改单处理
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public abstract class ModifyExecutionOrderAggregate {

    /**
     * 订单参考金额
     * 根据改金额计算的quantity
     */
    protected Double refAmount;

    @Getter
    protected AggregationExecutionOrder executionOrder;

    protected Integer priceUpSize;

    protected AggregationExecutionOrderRecord recordOrder;

    public ModifyExecutionOrderAggregate(AggregationExecutionOrder executionOrder, Double refAmount, Integer priceUpSize) {
        this.executionOrder = executionOrder;
        this.refAmount = refAmount;
        this.priceUpSize = priceUpSize;
    }

    private CommonConsts.StockMarket getMarket() {
        return CommonConsts.StockMarket.ofOrDefault(executionOrder.getMarket());
    }

    /**
     * 是否需要改单：状态方面 && 交易时段内
     */
    public Boolean needModify() {
        return (TradeStatus.NEW.getValue().equals(executionOrder.getTradeStatus())
                    || TradeStatus.PENDING_NEW.getValue().equals(executionOrder.getTradeStatus())) &&
                TradingCalendar.isTradingTime(getMarket());
    }

    public Boolean isMiddleClose() {
        if (CommonConsts.StockMarket.HK.equals(getMarket())) {
            return HkTradingStatus.MIDDLE_CLOSE.equals(HkTradingCalendar.getTradingStatus(TimeUtils.getHKNow()));
        }

        return false;
    }

    /**
     * 是否需要改单：金额方面
     */
    public Boolean needModify(QuotePointDTO realTimeQuote) {
        return BigDecimalUtil.isMoreThan(getRealTimeQuoteValue(realTimeQuote), executionOrder.getPrice());
    }

    public String getSymbol() {
        return executionOrder.getSymbol();
    }

    public String getSecType() {return executionOrder.getSecType();}

    public AggregationExecutionOrderRecord buildModifyOrder(ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote) {
        // 已成交金额
        Double preFilledAmount = BigDecimalUtil.multiply(getFilledQuantity(), getAvgPrice()).doubleValue();
        // 剩余金额
        Double restAmount = BigDecimalUtil.subtract(refAmount, preFilledAmount).doubleValue();
        // 限价
        Double refPrice = calRefPrice(contractInfo, realTimeQuote);
        // 数量
        Double totalQuantity = getModifyQuantity(restAmount, refPrice, contractInfo.getLotSize());

        recordOrder = AggregationExecutionOrderRecord.builder()
                .aggregationExecutionId(executionOrder.getId())
                .aggregationId(executionOrder.getAggregationId())
                .symbol(executionOrder.getSymbol())
                .market(executionOrder.getMarket())
                .currency(executionOrder.getCurrency())
                .action(executionOrder.getAction())
                .operation(ExecutionRecordOrderOperation.MODIFY.name())
                .amount(refAmount)
                .refPrice(refPrice)
                .quantity(totalQuantity)
                .lotSizeUnit(contractInfo.getLotSize())
                .preFilledQuantity(getFilledQuantity())
                .preAvgPrice(getAvgPrice())
                .status(ExecutionOrderStatus.PENDING.name())
                .build();

        return recordOrder;
    }

    protected Double calRefPrice(ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote) {
        return OrderUtil.calUpPrice(getRealTimeQuoteValue(realTimeQuote), priceUpSize, contractInfo.getTickerSize());
    }

    protected Double getFilledQuantity() {
        return executionOrder.getFilledQuantity();
    }

    protected Double getAvgPrice() {
        return executionOrder.getAvgPrice();
    }

    protected Double getRealTimeQuoteValue(QuotePointDTO realTimeQuote) {
        return realTimeQuote.getClose();
    }

    protected Double getModifyQuantity(Double restAmount, Double refPrice, double lotSize) {
        // 剩余可买数量
        Double restQuantity = Math.floor(restAmount / refPrice);
        // 总共数量
        double totalQuantity = BigDecimalUtil.add(restQuantity, getFilledQuantity()).doubleValue();
        // 总共手数
        double totalLotSize = Math.floor(totalQuantity / lotSize);
        // 整手总共数量
        return BigDecimalUtil.multiply(totalLotSize, lotSize).doubleValue();
    }

    public ExecutionOrderValObj getValObjAfterModifyOrder(OrderFacadeDTO orderFacadeDTO) {
        if (orderFacadeDTO == null || !orderFacadeDTO.isSucc()) {
            // 改单失败
            recordOrder.setStatus(ExecutionOrderStatus.REJECTED.name());
        } else {
            executionOrder.setPrice(recordOrder.getRefPrice());
            recordOrder.setStatus(ExecutionOrderStatus.SUCC.name());
        }
        return ExecutionOrderValObj.builder()
                .executionOrder(executionOrder)
                .recordOrder(recordOrder)
                .build();
    }

    public boolean needPlace() {
        Double preFilledQuantity = executionOrder.getFilledQuantity();
        preFilledQuantity = Objects.isNull(preFilledQuantity) ? 0.0 : preFilledQuantity;
        return BigDecimalUtil.isMoreThan(recordOrder.getQuantity(), preFilledQuantity);
    }
}
