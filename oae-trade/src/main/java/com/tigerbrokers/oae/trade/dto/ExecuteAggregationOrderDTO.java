package com.tigerbrokers.oae.trade.dto;

import com.tigerbrokers.oae.entity.consts.ExecutionErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteAggregationOrderDTO {

    private Boolean succ;

    private ExecutionErrorCode errorCode;

    public static ExecuteAggregationOrderDTO createSuccDTO() {
        return ExecuteAggregationOrderDTO.builder().succ(Boolean.TRUE).build();
    }

    public static ExecuteAggregationOrderDTO createFailDTO(ExecutionErrorCode errorCode) {
        return ExecuteAggregationOrderDTO.builder().succ(Boolean.FALSE).errorCode(errorCode).build();
    }
}
