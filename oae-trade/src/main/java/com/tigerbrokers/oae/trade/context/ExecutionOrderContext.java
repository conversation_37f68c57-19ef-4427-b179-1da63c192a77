package com.tigerbrokers.oae.trade.context;

import com.tigerbrokers.alpha.commons.utils.Price;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Data
public class ExecutionOrderContext {

    private long id;

    private Long userId;

    private long accountId;

    private String symbol;

    private String expiry;

    private String strike;

    private String right;

    private String market;

    private String currency;

    private String secType;

    private String segType;

    private String nameCN;

    private String action;

    private Boolean isLong;

    private Long totalQuantity;

    private Integer totalQuantityScale = 0;

    private Long filledQuantity;

    private Integer filledQuantityScale = 0;

    private Double totalCashAmount;

    private Double filledCashAmount;

    private String orderType;

    @Price
    private Double price;

    @Price
    private Double stopPrice;

    @Price
    private Double avgFillPrice;

    private Boolean onlyRth;

    private String status;

    private String statusDesc;

    private List<String> subStatusList;

    private List<String> subStatusDescList;

    @Price
    private Double realizedPnl;

    private Double commission;

    private Double commissionAndFee;

    private Double gst;

    private String message;

    private String messageCode;

    private String replaceStatus;

    private String cancelStatus;

    private List<String> attrList;

    private String attrDesc;

    private boolean liquidation;

    private String source;

    private String externalId;

    private Long statusUpdatedAt;

    private Long updatedAt;

    private Long createdAt;

    private Boolean canModify;

    private Boolean canCancel;

    private Double multiplier;

    private Long stockId;

    @Deprecated
    private Long attr;

    private String remark;

    public Double getFilledQuantityValue() {
        if (filledQuantity != null && BigDecimalUtil.isNotEqual(filledQuantity, 0.0)) {
            return BigDecimalUtil.toDouble(filledQuantity, filledQuantityScale);
        }
        return null;
    }

    public Double getTotalQuantityValue() {
        if (totalQuantity != null && BigDecimalUtil.isNotEqual(totalQuantity, 0.0)) {
            return BigDecimalUtil.toDouble(totalQuantity, totalQuantityScale);
        }
        return null;
    }

    public AggregationExecutionOrder toExecutionOrder(AggregationExecutionOrder executionOrder) {
        executionOrder.setTradeId(String.valueOf(id));
        executionOrder.setTotalQuantity(getTotalQuantityValue());
        executionOrder.setFilledQuantity(getFilledQuantityValue());
        executionOrder.setAvgPrice(avgFillPrice);
        executionOrder.setTradeStatus(status);
        executionOrder.setTradeAmount(totalCashAmount);
        executionOrder.setFilledAmount(filledCashAmount);
        executionOrder.setErrorMsg(message);
        executionOrder.setErrorCode(messageCode);
        return executionOrder;
    }

    public static ExecutionOrderContext toExecutionOrderContext(String content) {
        if (StringUtils.isNotEmpty(content)) {
            return JacksonUtil.readValue(content, ExecutionOrderContext.class);
        }

        return null;
    }
}
