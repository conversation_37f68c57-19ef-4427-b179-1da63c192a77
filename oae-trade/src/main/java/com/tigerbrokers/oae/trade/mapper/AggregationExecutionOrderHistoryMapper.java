package com.tigerbrokers.oae.trade.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface AggregationExecutionOrderHistoryMapper {

    AggregationExecutionOrderHistoryMapper INSTANCE = Mappers.getMapper(AggregationExecutionOrderHistoryMapper.class);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "aggregationExecutionId", source = "id"),
            @Mapping(target = "createdAt", ignore = true),
            @Mapping(target = "updatedAt", ignore = true)
    })
    AggregationExecutionOrderHistory from(AggregationExecutionOrder executionOrder);

}
