package com.tigerbrokers.oae.trade.service;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.LockType;
import com.tigerbrokers.oae.entity.consts.LockedAssetStatus;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.LockedAssetFacade;
import com.tigerbrokers.oae.facade.context.CalculateLockAssetContext;
import com.tigerbrokers.oae.facade.dto.CalLockInfoDTO;
import com.tigerbrokers.oae.facade.context.LockedAssetFacadeContext;
import com.tigerbrokers.oae.facade.dto.LockedAssetFacadeDTO;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.trade.aggregate.LockedAssetAggregate;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import com.tigerbrokers.oae.trade.dto.LockAssetDTO;
import com.tigerbrokers.oae.trade.repository.LockedAssetRepository;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Slf4j
@Service
public class LockedAssetService {

    @Autowired
    private LockedAssetRepository lockedAssetRepository;

    @Autowired
    private LockedAssetFacade lockedAssetFacade;

    @Autowired
    private PublishService publishService;

    /**
     * 资产锁定
     */
    public LockAssetDTO lockAsset(LockAssetContext lockAssetContext) {
        // 新建锁定记录
        LockedAssetAggregate lockedAssetAggregate = new LockedAssetAggregate();
        LockedAsset lockedAsset = lockedAssetAggregate.buildLockedAsset(lockAssetContext);
        lockedAssetRepository.insert(lockedAsset);
        // 锁定资金 & 持仓
        LockedAssetFacadeDTO lockedAssetFacadeDTO;
        if (lockedAsset.getType().equals(LockType.ASSET.name())) {
            lockedAssetFacadeDTO = lockedAssetFacade.lockAsset(
                    LockedAssetFacadeContext.createLockedContext(lockedAsset));
        } else {
            lockedAssetFacadeDTO = lockedAssetFacade.lockPosition(
                    LockedAssetFacadeContext.createLockedContext(lockedAsset));
        }
        // 更新记录
        lockedAssetRepository.updateLockedAsset(lockedAssetAggregate.updateLockedAssetAfterLock(lockedAssetFacadeDTO));

        return lockedAssetAggregate.toLockAssetDTO();
    }

    public LockedAsset newPendingLockAsset(LockAssetContext lockAssetContext) {
        // 新建锁定记录
        LockedAssetAggregate lockedAssetAggregate = new LockedAssetAggregate();
        LockedAsset toUpdate = lockedAssetAggregate.buildLockedAsset(lockAssetContext);
        LockedAsset existLocked = lockedAssetRepository.getByUserOrderId(toUpdate.getUserOrderId(),
                LockedAssetStatus.LOCKED_PENDING, LockStep.valueOf(toUpdate.getLockStep()));
        if (existLocked == null) {
            lockedAssetRepository.insert(toUpdate);
        } else {
            toUpdate.setId(existLocked.getId());
            lockedAssetRepository.updateLockedAsset(toUpdate);
        }
        return toUpdate;
    }

    /**
     * 资产预解锁标记
     */
    public void unlockPending(List<Long> userOrderIdList, LockStep lockStep) {
        lockedAssetRepository.batchUpdateUnlockedPendingStatusByUserOrderId(userOrderIdList, lockStep);
    }

    /**
     * 未成交订单，进行资产解锁
     */
    public void unlockUnfilledAsset(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        List<LockedAsset> lockedAssetList = lockedAssetRepository.getPendingLockedAsset(businessSource, market, secType);
        if (CollectionUtils.isEmpty(lockedAssetList)) {
            return;
        }
        for (LockedAsset lockedAsset : lockedAssetList) {
            // 解锁
            LockedAssetFacadeDTO lockAssetDTO;
            if (lockedAsset.getType().equals(LockType.ASSET.name())) {
                lockAssetDTO = lockedAssetFacade.unlockAsset(
                        LockedAssetFacadeContext.createUnlockedContext(lockedAsset));
            } else {
                lockAssetDTO = lockedAssetFacade.unlockPosition(
                        LockedAssetFacadeContext.createUnlockedContext(lockedAsset));
            }
            if (lockAssetDTO != null && lockAssetDTO.isSucc()) {
                lockedAsset.setStatus(LockedAssetStatus.UNLOCKED.name());
                lockedAssetRepository.updateLockedAsset(lockedAsset);
                // 发布UserOrder订单相关消息
                publishService.publishUserOrder(lockedAsset.getUserOrderId());
            }
        }
    }

    public void unlockSettleAsset(List<Long> userOrderIds) {
        List<LockedAsset> lockedAssetList = lockedAssetRepository.getByUserOrderIds(userOrderIds, LockStep.SETTLE);
        if (CollectionUtils.isEmpty(lockedAssetList)) {
            return;
        }
        for (LockedAsset lockedAsset : lockedAssetList) {
            if (lockedAsset.getType().equals(LockType.ASSET.name())) {
                return;
            }
            LockedAssetFacadeDTO  lockAssetDTO = lockedAssetFacade.unlockPosition(
                        LockedAssetFacadeContext.createUnlockedContext(lockedAsset));
            if (lockAssetDTO != null && lockAssetDTO.isSucc()) {
                lockedAsset.setStatus(LockedAssetStatus.UNLOCKED.name());
                lockedAssetRepository.updateLockedAsset(lockedAsset);
            }
        }
    }

    public CalLockInfoDTO calMarginLockInfo(CalculateLockAssetContext context) {
        return lockedAssetFacade.calMarginLockInfo(context);
    }

    public void unlockByIdList(List<Long> lockedAssetIds) {
        List<LockedAsset> lockedAssetList = lockedAssetRepository.getLockedAssetByIds(lockedAssetIds);
        if (CollectionUtils.isEmpty(lockedAssetList)) {
            log.error("unlockByIdList is empty with ids:{}", lockedAssetIds);
            return;
        }
        for (LockedAsset lockedAsset : lockedAssetList) {
            LockedAssetFacadeDTO lockAssetDTO;
            if (lockedAsset.getType().equals(LockType.ASSET.name())) {
                lockAssetDTO = lockedAssetFacade.unlockAsset(
                        LockedAssetFacadeContext.createUnlockedContext(lockedAsset));
            } else {
                lockAssetDTO = lockedAssetFacade.unlockPosition(
                        LockedAssetFacadeContext.createUnlockedContext(lockedAsset));
            }
            if (lockAssetDTO != null && lockAssetDTO.isSucc()) {
                log.info("unlockByIdList success with lockedAssetId:{}", lockedAsset.getId());
            } else {
                log.error("unlockByIdList failed with lockedAssetId:{}", lockedAsset.getId());
            }
        }
    }
}
