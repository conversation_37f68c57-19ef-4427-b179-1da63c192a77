package com.tigerbrokers.oae.trade.aggregate;

import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionRecordOrderOperation;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.util.OrderUtil;
import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.HkTradingCalendar;
import com.tigerbrokers.stock.util.HkTradingStatus;
import com.tigerbrokers.stock.util.TimeUtils;
import com.tigerbrokers.stock.util.TradingCalendar;
import lombok.Getter;

import java.util.Objects;

/**
 * 执行订单未成交
 * 撤单处理
 *
 */
public class CancelExecutionOrderAggregate {

    @Getter
    private AggregationExecutionOrder executionOrder;

    private AggregationExecutionOrderRecord recordOrder;

    public CancelExecutionOrderAggregate(AggregationExecutionOrder executionOrder) {
        this.executionOrder = executionOrder;
    }

    public AggregationExecutionOrderRecord buildCancelOrder() {
        recordOrder = AggregationExecutionOrderRecord.builder()
                .aggregationExecutionId(executionOrder.getId())
                .aggregationId(executionOrder.getAggregationId())
                .symbol(executionOrder.getSymbol())
                .market(executionOrder.getMarket())
                .currency(executionOrder.getCurrency())
                .action(executionOrder.getAction())
                .operation(ExecutionRecordOrderOperation.CANCEL.name())
                .amount(executionOrder.getTradeAmount())
                .refPrice(executionOrder.getPrice())
                .quantity(executionOrder.getFilledQuantity())
                .preFilledQuantity(getFilledQuantity())
                .preAvgPrice(getAvgPrice())
                .status(ExecutionOrderStatus.PENDING.name())
                .build();

        return recordOrder;
    }

    protected Double getFilledQuantity() {
        return executionOrder.getFilledQuantity();
    }

    protected Double getAvgPrice() {
        return executionOrder.getAvgPrice();
    }

    public ExecutionOrderValObj getValObjAfterCancelOrder(OrderFacadeDTO orderFacadeDTO) {
        if (orderFacadeDTO == null || !orderFacadeDTO.isSucc()) {
            // 取消订单失败
            recordOrder.setStatus(ExecutionOrderStatus.REJECTED.name());
        } else {
            executionOrder.setStatus(ExecutionOrderStatus.CANCELED.name());
            recordOrder.setStatus(ExecutionOrderStatus.SUCC.name());
        }
        return ExecutionOrderValObj.builder()
                .executionOrder(executionOrder)
                .recordOrder(recordOrder)
                .build();
    }
}
