package com.tigerbrokers.oae.trade.aggregate.valobj;

import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

@Data
@AllArgsConstructor
public class AggregationKey {

    private String orderType;
    private String symbol;
    private String dealAccount;
    private String action;
    private String source;

    public AggregationKey(UserOrder order) {
        this.orderType = order.getOrderType();
        this.symbol = order.getSymbol();
        this.dealAccount = order.getDealAccount();
        this.action = order.getAction();
        this.source = order.getSource();
    }

    //注意重新生成equals&hashcode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AggregationKey that = (AggregationKey) o;
        return Objects.equals(orderType, that.orderType) && Objects.equals(symbol, that.symbol) && Objects.equals(dealAccount, that.dealAccount) && Objects.equals(action, that.action) && Objects.equals(source, that.source);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderType, symbol, dealAccount, action, source);
    }
}
