package com.tigerbrokers.oae.trade.aggregate;

import cn.hutool.core.bean.BeanUtil;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.aggregate.execute.ExecuteAggregationOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.execute.ExecuteOrderAggregateBuilder;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.config.TradeConfig;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class OddFilledOrderAggregate {

    @Getter
    private AggregationOrder aggregationOrder;

    @Getter
    private boolean hasUnfinishedOrder = false;

    private ExecuteAggregationOrderAggregate executeAggregationOrderAggregate;

    public OddFilledOrderAggregate(AggregationOrder aggregationOrder,
                                   List<AggregationExecutionOrder> executionOrderList,
                                   ContractInfoDTO contractInfo,
                                   QuotePointDTO realTimeQuote,
                                   TradeConfig.OAETradeConfig oaeTradeConfig) {
        this.aggregationOrder = aggregationOrder;
        this.hasUnfinishedOrder = checkHasUnfinishedOrder(executionOrderList);
        AggregationOrder newAggregationOrder = initNewAggregationOrder(aggregationOrder, executionOrderList);
        ExecuteOrderAggregateBuilder executeOrderAggregateBuilder = new ExecuteOrderAggregateBuilder(newAggregationOrder, contractInfo, realTimeQuote, oaeTradeConfig);
        this.executeAggregationOrderAggregate = executeOrderAggregateBuilder.getExecuteOrderAggregate();
    }

    private boolean checkHasUnfinishedOrder(List<AggregationExecutionOrder> executionOrderList) {
        return executionOrderList.stream()
                .anyMatch(item -> !TradeStatus.isFinishedStatus(item.getTradeStatus()));
    }

    public void buildExecutionOrderList() {
        this.executeAggregationOrderAggregate.buildExecutionOrderList();
    }

    public List<ExecutionOrderValObj> getExecutionOrderValObjList() {
        return this.executeAggregationOrderAggregate.getExecutionOrderValObjList();
    }

    /**
     * 是否可下单
     */
    public Boolean canExecute() {
        return this.executeAggregationOrderAggregate.canExecute();
    }

    /**
     * 子订单金额
     */
    public Double getSubAmount() {
        return this.executeAggregationOrderAggregate.getSubAmount();
    }

    /**
     * 人手单数量
     */
    public Double getOddQuantity() {
        return this.executeAggregationOrderAggregate.getOddQuantity();
    }

    public Double getRestAmount() {
        return this.executeAggregationOrderAggregate.getTradeAmount();
    }

    private AggregationOrder initNewAggregationOrder(AggregationOrder aggregationOrder,
                                                     List<AggregationExecutionOrder> executionOrderList) {
        AggregationOrder result = new AggregationOrder();
        Double restAmount = 0d;
        BeanUtil.copyProperties(aggregationOrder, result, false);
        // 判断是否有没有成交的订单
        if (Boolean.FALSE.equals(hasUnfinishedOrder)) {
            Double totalFilledAmount = executionOrderList.stream()
                    .filter(item -> item.getFilledQuantity() != null && item.getAvgPrice() != null)
                    .map(item -> BigDecimalUtil.multiply(item.getFilledQuantity(), item.getAvgPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .doubleValue();
            restAmount = BigDecimalUtil.subtract(aggregationOrder.getTradeAmount(), totalFilledAmount).doubleValue();
        }
        result.setTradeAmount(restAmount);
        return result;
    }

}
