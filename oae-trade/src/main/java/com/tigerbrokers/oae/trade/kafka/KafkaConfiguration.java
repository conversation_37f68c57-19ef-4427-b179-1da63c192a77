package com.tigerbrokers.oae.trade.kafka;

import com.tigerbrokers.oae.facade.config.FacadeConfig;
import com.tigerbrokers.oae.storage.kafka.KafkaUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;

/**
 * @author: chentao
 * Created on 2/2/2023
 */
@Configuration
public class KafkaConfiguration {

    public static final String PRIME_TRADE_KAFKA_LISTENER_FACTORY = "primeTradeKafkaListenerContainerFactory";

    @Bean(name = PRIME_TRADE_KAFKA_LISTENER_FACTORY)
    public ConcurrentKafkaListenerContainerFactory<String, String> primeTradeKafkaListenerContainerFactory(
            FacadeConfig.PrimeTradeKafkaConfig primeTradeKafkaConfig) {
        return KafkaUtil.getConcurrentKafkaListenerContainerFactory(primeTradeKafkaConfig.getGroup(),
                primeTradeKafkaConfig.getBootstrapServers(),
                primeTradeKafkaConfig.getConcurrency());
    }
}
