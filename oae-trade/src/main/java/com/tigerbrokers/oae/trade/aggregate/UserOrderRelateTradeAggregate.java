package com.tigerbrokers.oae.trade.aggregate;

import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.context.UserOrderRelateTradeContext;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
public class UserOrderRelateTradeAggregate {

    @Getter
    private UserOrder userOrder;

    public UserOrderRelateTradeAggregate(UserOrder userOrder) {
        this.userOrder = userOrder;
    }

    public UserOrder updateAfterSyncTradeInfo(UserOrderRelateTradeContext context, String tradeOriMsg) {
        return UserOrder.builder().id(userOrder.getId()).tradeId(context.getTradeId())
                .status(UserOrderStatus.COMPLETED.name()).tradeMsg(tradeOriMsg).build();
    }
}
