package com.tigerbrokers.oae.trade.repository;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.storage.jdbc.UserOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Slf4j
@Service
public class UserOrderRepository {

    @Autowired
    private UserOrderDao userOrderDao;

    public void insert(UserOrder userOrder) {
        userOrderDao.insert(userOrder);
    }

    public void update(UserOrder userOrder) {
        userOrder.setUpdatedAt(DateTime.now());
        userOrderDao.update(userOrder);
    }

    public List<UserOrder> getUserOrders(UserOrderStatus status, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        return userOrderDao.selectByStatus(status.name(), source, market, secType);
    }

    public UserOrder getUserOrder(Long userOrderId) {
        return userOrderDao.select(userOrderId);
    }

    public List<UserOrder> getUserOrders(Long aggregationOrderId) {
        return userOrderDao.selectByAggregationId(aggregationOrderId);
    }

    public Map<Long, UserOrder> getUserOrderMapByIds(List<Long> userOrderIds) {
        if (CollectionUtils.isEmpty(userOrderIds)) {
            return new HashMap<>();
        }
        return userOrderDao.selectByIds(userOrderIds).stream().collect(Collectors.toMap(UserOrder::getId, Function.identity()));
    }
}
