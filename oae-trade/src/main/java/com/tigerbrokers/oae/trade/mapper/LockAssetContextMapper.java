package com.tigerbrokers.oae.trade.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface LockAssetContextMapper {

    LockAssetContextMapper INSTANCE = Mappers.getMapper(LockAssetContextMapper.class);

    @Mappings({})
    LockedAsset to(LockAssetContext context);
}
