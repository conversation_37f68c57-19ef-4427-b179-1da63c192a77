package com.tigerbrokers.oae.trade.aggregate;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.ActionType;
import com.tigerbrokers.oae.entity.consts.LockType;
import com.tigerbrokers.oae.entity.consts.LockedAssetStatus;
import com.tigerbrokers.oae.facade.dto.LockedAssetFacadeDTO;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import com.tigerbrokers.oae.trade.dto.LockAssetDTO;
import com.tigerbrokers.oae.trade.mapper.LockAssetContextMapper;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public class LockedAssetAggregate {

    private LockedAsset lockedAsset;

    public LockedAsset buildLockedAsset(LockAssetContext lockAssetContext) {
        lockedAsset = LockAssetContextMapper.INSTANCE.to(lockAssetContext);
        lockedAsset.setTradeTime(DateTime.now());
        lockedAsset.setStatus(LockedAssetStatus.LOCKED_PENDING.name());
        lockedAsset.setType(getLockedTypeByAction(lockAssetContext.getAction()).name());
        return lockedAsset;
    }

    public LockedAsset updateLockedAssetAfterLock(LockedAssetFacadeDTO lockedAssetFacadeDTO) {
        if (lockedAssetFacadeDTO != null && lockedAssetFacadeDTO.isSucc()) {
            lockedAsset.setStatus(LockedAssetStatus.LOCKED.name());
        } else {
            lockedAsset.setStatus(LockedAssetStatus.LOCKED_ERROR.name());
        }

        return lockedAsset;
    }

    public LockAssetDTO toLockAssetDTO() {
        Boolean isSucc = lockedAsset.getStatus().equals(LockedAssetStatus.LOCKED.name());

        return LockAssetDTO.builder().succ(isSucc).build();
    }

    private LockType getLockedTypeByAction(String action) {
        if (ActionType.BUY.name().equals(action)) {
            return LockType.ASSET;
        } else {
            return LockType.POSITION;
        }
    }
}
