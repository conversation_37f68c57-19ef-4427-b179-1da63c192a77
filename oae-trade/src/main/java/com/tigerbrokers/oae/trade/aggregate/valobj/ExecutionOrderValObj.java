package com.tigerbrokers.oae.trade.aggregate.valobj;

import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Data
@Builder
public class ExecutionOrderValObj {

    private AggregationExecutionOrder executionOrder;

    private AggregationExecutionOrderRecord recordOrder;

    public Long getExecutionOrderId() {
        if (executionOrder != null) {
            return executionOrder.getId();
        }
        return null;
    }

    public void updateAfterPlaceOrder(OrderFacadeDTO orderFacadeDTO) {
        if (orderFacadeDTO == null || !orderFacadeDTO.isSucc()) {
            // 下单失败
            String errorCode = orderFacadeDTO == null ? null : orderFacadeDTO.getErrorCodeStr();
            String errorMsg = orderFacadeDTO == null ? null : orderFacadeDTO.getErrorMsg();
            executionOrder.setErrorCode(errorCode);
            executionOrder.setErrorMsg(errorMsg);
            executionOrder.setStatus(ExecutionOrderStatus.REJECTED.name());
            executionOrder.setTradeStatus(TradeStatus.REJECTED.getValue());
            recordOrder.setStatus(ExecutionOrderStatus.REJECTED.name());
        } else {
            executionOrder.setTradeId(String.valueOf(orderFacadeDTO.getTradeId()));
            executionOrder.setTradeStatus(String.valueOf(orderFacadeDTO.getStatus()));
            executionOrder.setStatus(ExecutionOrderStatus.SUCC.name());
            recordOrder.setStatus(ExecutionOrderStatus.SUCC.name());
        }
    }
}
