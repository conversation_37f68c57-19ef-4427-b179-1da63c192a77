package com.tigerbrokers.oae.trade.aggregate.modify;

import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.trade.util.OrderUtil;

/**
 * 执行订单未成交
 * 改单处理
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public class USDRIPModifyExecutionOrderAggregate extends ModifyExecutionOrderAggregate {

    public USDRIPModifyExecutionOrderAggregate(AggregationExecutionOrder executionOrder, Double refAmount, Integer priceUpSize) {
        super(executionOrder, refAmount, priceUpSize);
    }

    @Override
    protected Double getModifyQuantity(Double restAmount, Double refPrice, double lotSize) {
        // 剩余可买数量
        Double restQuantity = OrderUtil.calUSOrderQuantity(restAmount, refPrice);
        // 总共数量
        return BigDecimalUtil.add(restQuantity, getFilledQuantity()).doubleValue();
    }

    @Override
    protected Double calRefPrice(ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote) {
        return OrderUtil.calUSUpPrice(getRealTimeQuoteValue(realTimeQuote), priceUpSize, contractInfo.getTickerSize());
    }
}
