package com.tigerbrokers.oae.trade.aggregate.execute;

import com.tigerbrokers.alpha.commons.data.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.config.TradeConfig;

public class ExecuteOrderAggregateBuilder {

    private AggregationOrder aggregationOrder;
    private ContractInfoDTO contractInfo;
    private QuotePointDTO realTimeQuote;
    private TradeConfig.OAETradeConfig oaeTradeConfig;
    private BusinessSource businessSource;

    public ExecuteOrderAggregateBuilder(AggregationOrder aggregationOrder, ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote,
                                        TradeConfig.OAETradeConfig oaeTradeConfig) {
        this.aggregationOrder = aggregationOrder;
        this.contractInfo = contractInfo;
        this.realTimeQuote = realTimeQuote;
        this.oaeTradeConfig = oaeTradeConfig;
        this.businessSource = BusinessSource.valueOf(aggregationOrder.getSource());
    }

    public ExecuteAggregationOrderAggregate getExecuteOrderAggregate() {
        switch (businessSource) {
            case RSP:
                return new RSPExecuteOrderAggregate(aggregationOrder, contractInfo, realTimeQuote, oaeTradeConfig.getPriceUpSize(), oaeTradeConfig.getModifyTimeout());
            case DRIP:
                if (aggregationOrder.getSecType().equals(SecType.FUND.name())) {
                    return new DRIPExecuteOrderAggregate(aggregationOrder, contractInfo, realTimeQuote, oaeTradeConfig.getPriceUpSize(), oaeTradeConfig.getModifyTimeout());
                } else if (FacadeUtils.isUSDrip(aggregationOrder.getSource(), aggregationOrder.getSecType(), aggregationOrder.getMarket())) {
                    return new USDRIPExecuteOrderAggregate(aggregationOrder, contractInfo, realTimeQuote, oaeTradeConfig.getUsPriceUpSize(), oaeTradeConfig.getUsModifyTimeout());
                }
            default:
                return null;
        }
    }

}
