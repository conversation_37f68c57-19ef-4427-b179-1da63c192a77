package com.tigerbrokers.oae.trade.kafka.data;

import com.tigerbrokers.alpha.commons.utils.Price;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @author: chentao
 * Created on 2/2/2023
 */
@Data
public class PrimeTradeKafka {

    private long id;

    private Long userId;

    private long accountId;

    private String symbol;

    private String expiry;

    private String strike;

    private String right;

    private String market;

    private String currency;

    private String secType;

    private String segType;

    private String nameCN;

    private String action;

    private Boolean isLong;

    private Long totalQuantity;

    private Integer totalQuantityScale = 0;

    private Long filledQuantity;

    private Integer filledQuantityScale = 0;

    private Double totalCashAmount;

    private Double filledCashAmount;

    private String orderType;

    @Price
    private Double price;

    @Price
    private Double stopPrice;

    @Price
    private Double avgFillPrice;

    private Boolean onlyRth;

    private String status;

    private String statusDesc;

    private List<String> subStatusList;

    private List<String> subStatusDescList;

    @Price
    private Double realizedPnl;

    private Double commission;

    private Double commissionAndFee;

    private Double gst;

    private String message;

    private String messageCode;

    private String replaceStatus;

    private String cancelStatus;

    private List<String> attrList;

    private String attrDesc;

    private boolean liquidation;

    private String source;

    private Long statusUpdatedAt;

    private Long updatedAt;

    private Long createdAt;

    private Boolean canModify;

    private Boolean canCancel;

    private Double multiplier;

    private Long stockId;

    @Deprecated
    private Long attr;

    private String remark;

    private String externalId;

    public static PrimeTradeKafka toPrimeTradeKafka(String content) {
        if (StringUtils.isNotEmpty(content)) {
            return JacksonUtil.readValue(content, PrimeTradeKafka.class);
        }

        return null;
    }
}
