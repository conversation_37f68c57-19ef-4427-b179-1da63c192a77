package com.tigerbrokers.oae.trade.context;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Data
public class ExecuteAggregationOrderContext {

    private AggregationOrder aggregationOrder;

    public ExecuteAggregationOrderContext(AggregationOrder aggregationOrder) {
        this.aggregationOrder = aggregationOrder;
    }

    public String getSymbol() {
        return aggregationOrder.getSymbol();
    }

    public String getSecType() {return aggregationOrder.getSecType();}
}
