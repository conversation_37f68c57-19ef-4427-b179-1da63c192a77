package com.tigerbrokers.oae.trade.service;

import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.OrderAttr;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.*;
import com.tigerbrokers.oae.entity.data.ExecutionOrderMsg;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.OrderApiFacade;
import com.tigerbrokers.oae.facade.context.CancelOrderFacadeContext;
import com.tigerbrokers.oae.facade.context.ModifyOrderFacadeContext;
import com.tigerbrokers.oae.facade.context.placeOrder.OrderFacadeContextBuilder;
import com.tigerbrokers.oae.facade.context.placeOrder.PlaceOrderFacadeContext;
import com.tigerbrokers.oae.facade.dto.ContractDTO;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.facade.dto.TradeDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.aggregate.CancelExecutionOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.modify.ModifyExecuteOrderAggregateBuilder;
import com.tigerbrokers.oae.trade.aggregate.modify.ModifyExecutionOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.OddFilledOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.OddOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.execute.ExecuteAggregationOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.execute.ExecuteOrderAggregateBuilder;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.config.TradeConfig;
import com.tigerbrokers.oae.trade.consts.ExecutionConst;
import com.tigerbrokers.oae.trade.context.ExecuteAggregationOrderContext;
import com.tigerbrokers.oae.trade.context.ExecutionOrderContext;
import com.tigerbrokers.oae.trade.dto.ExecuteAggregationOrderDTO;
import com.tigerbrokers.oae.trade.dto.ExecuteFilledAggregationOrderDTO;
import com.tigerbrokers.oae.trade.repository.ExecutionRepository;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 聚合订单AggregationOrder拆单
 * 执行下单
 *
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class ExecutionService {

    @Autowired
    QuoteService quoteService;
    @Autowired
    private ExecutionRepository executionRepository;
    @Autowired
    private OrderApiFacade orderApiFacade;
    @Autowired
    private AlertService alertService;
    @Autowired
    private UserOrderRepository userOrderRepository;
    @Autowired
    private LockedAssetService lockedAssetService;

    private TradeConfig.OAETradeConfig oaeTradeConfig;

    private DelayQueue<ExecutionOrderMsg> executionOrderQueue;

    private ExecutorService sendService;

    @Autowired
    public ExecutionService(TradeConfig.OAETradeConfig oaeTradeConfig) {
        this.oaeTradeConfig = oaeTradeConfig;
        this.executionOrderQueue = new DelayQueue<>();
        this.sendService = Executors.newSingleThreadExecutor();
        this.sendService.submit(this::modifyExecutionOrder);
    }

    /**
     * 向订单执行队列添加数据
     */
    private void addExecutionOrderQueue(ExecutionOrderMsg msg) {
        log.info("add ExecutionOrderQueue: {}", msg);
        executionOrderQueue.add(msg);
    }

    /**
     * 聚合订单AggregationOrder下单执行
     */
    public ExecuteAggregationOrderDTO executeAggregationOrder(ExecuteAggregationOrderContext context) {
        ContractDTO contractDTO = quoteService.getContract(context.getSymbol(), context.getSecType());
        if (Objects.isNull(contractDTO)) {
            // 缺少合约行情相关数据
            String errorMsg = String.format("execution aggregation order, symbol-%s no contract or quote data: ExecutionAggregationOrderId-%s",
                    context.getSymbol(), context.getAggregationOrder().getId());
            log.error(errorMsg);
            return ExecuteAggregationOrderDTO.createSuccDTO();
        }
        // 创建执行订单
        ExecuteAggregationOrderAggregate executeAggregate = new ExecuteOrderAggregateBuilder(context.getAggregationOrder(),
                contractDTO.getContractInfo(), contractDTO.getRealTimeQuote(), oaeTradeConfig).getExecuteOrderAggregate();
        if (Boolean.TRUE.equals(executeAggregate.canExecute())) {
            executeAggregate.buildExecutionOrderList();
            executionRepository.insert(executeAggregate.getExecutionOrderValObjList());
            // place order
            for (ExecutionOrderValObj executionOrderValObj : executeAggregate.getExecutionOrderValObjList()) {
                OrderFacadeDTO orderFacadeDTO = orderApiFacade.placeOrder(
                        OrderFacadeContextBuilder.orderContextBuilder(executionOrderValObj.getExecutionOrder().getSource(), executionOrderValObj.getExecutionOrder().getMarket(), executionOrderValObj.getExecutionOrder().getSecType())
                                .create(executionOrderValObj.getExecutionOrder()));
                executionOrderValObj.updateAfterPlaceOrder(orderFacadeDTO);
                executionRepository.update(executionOrderValObj);
                if (orderFacadeDTO != null && orderFacadeDTO.isSucc()) {
                    if (executeAggregate.needDelayHandle()) {
                        // 延时队列
                        addExecutionOrderQueue(new ExecutionOrderMsg(executionOrderValObj.getExecutionOrderId(),
                                executeAggregate.getSubAmount(), executeAggregate.getModifyTimeout(), ExecutionConst.MODIFY_TIMEUNIT));
                    }
                } else {
                    String errMsg = String.format("place execution aggregation order fail: ExecutionAggregationOrderId-%s",
                            executionOrderValObj.getExecutionOrderId());
                    log.error(errMsg);
                    alertService.sendMsg(errMsg);
                }
            }
            return ExecuteAggregationOrderDTO.createSuccDTO();
        }
        if (executeAggregate.getOddQuantity() < oaeTradeConfig.getOddMinQuantity()) {
            return ExecuteAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NOT_SATISFY_MINIMIZE_QUANTITY);
        } else {
            return ExecuteAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NEED_PLACE_ODD);
        }
    }

    /**
     * 已成交聚合订单AggregationOrder下单执行
     * @param context
     */
    public ExecuteFilledAggregationOrderDTO executeFilledAggregationOrder(ExecuteAggregationOrderContext context) {
        ContractDTO contractDTO = quoteService.getContract(context.getSymbol(), context.getSecType());
        if (Objects.isNull(contractDTO)) {
            // 缺少合约行情相关数据
            String errorMsg = String.format("execution filled aggregation order, symbol-%s no contract or quote data: ExecutionAggregationOrderId-%s",
                    context.getSymbol(), context.getAggregationOrder().getId());
            log.error(errorMsg);
            return ExecuteFilledAggregationOrderDTO.createSuccDTO();
        }
        List<AggregationExecutionOrder> executionOrderList = executionRepository.getExecutionOrderList(context.getAggregationOrder().getId());
        OddFilledOrderAggregate filledOrderAggregate = new OddFilledOrderAggregate(context.getAggregationOrder(),
                executionOrderList, contractDTO.getContractInfo(), contractDTO.getRealTimeQuote(), oaeTradeConfig);
        // 正常下单流程
        if (Boolean.TRUE.equals(filledOrderAggregate.canExecute())) {
            filledOrderAggregate.buildExecutionOrderList();
            executionRepository.insert(filledOrderAggregate.getExecutionOrderValObjList());
            // place order
            for (ExecutionOrderValObj executionOrderValObj : filledOrderAggregate.getExecutionOrderValObjList()) {
                OrderFacadeDTO orderFacadeDTO = orderApiFacade.placeOrder(
                        OrderFacadeContextBuilder.orderContextBuilder(executionOrderValObj.getExecutionOrder().getSource(), executionOrderValObj.getExecutionOrder().getMarket(), executionOrderValObj.getExecutionOrder().getSecType())
                                .create(executionOrderValObj.getExecutionOrder()));
                executionOrderValObj.updateAfterPlaceOrder(orderFacadeDTO);
                executionRepository.update(executionOrderValObj);
                if (orderFacadeDTO != null && orderFacadeDTO.isSucc()) {
                    // 延时队列
                    addExecutionOrderQueue(new ExecutionOrderMsg(executionOrderValObj.getExecutionOrderId(),
                            filledOrderAggregate.getSubAmount(), oaeTradeConfig.getModifyTimeout(), ExecutionConst.MODIFY_TIMEUNIT));
                } else {
                    String errMsg = String.format("place execution filled aggregation order fail: ExecutionAggregationOrderId-%s",
                            executionOrderValObj.getExecutionOrderId());
                    log.error(errMsg);
                    alertService.sendMsg(errMsg);
                }
            }
            return ExecuteFilledAggregationOrderDTO.createSuccDTO();
        }
        // 碎股单
        if (filledOrderAggregate.isHasUnfinishedOrder()) {
            return ExecuteFilledAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NOT_SATISFY_ODD);
        }
        if (filledOrderAggregate.getOddQuantity() < oaeTradeConfig.getOddMinQuantity()) {
            return ExecuteFilledAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NOT_SATISFY_MINIMIZE_QUANTITY);
        }
        ExecuteFilledAggregationOrderDTO executeFilledAggregationOrderDTO =
                ExecuteFilledAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NEED_PLACE_ODD);
        executeFilledAggregationOrderDTO.setFilledAmount(calculateExecutionOrderFilledAmount(executionOrderList));
        return executeFilledAggregationOrderDTO;
    }

    /**
     * 下单-碎股单
     *
     * @param context
     */
    public ExecuteAggregationOrderDTO executeOddAggregationOrder(ExecuteAggregationOrderContext context) {
        ContractDTO contractDTO = quoteService.getContract(context.getSymbol(), context.getSecType());
        if (Objects.isNull(contractDTO)) {
            // 缺少合约行情相关数据
            String errorMsg = String.format("execution odd aggregation order, symbol-%s no contract or quote data: ExecutionAggregationOrderId-%s",
                    context.getSymbol(), context.getAggregationOrder().getId());
            log.error(errorMsg);
            return ExecuteAggregationOrderDTO.createSuccDTO();
        }
        // 创建执行订单
        OddOrderAggregate executeAggregate = new OddOrderAggregate(context.getAggregationOrder(),
                contractDTO.getContractInfo(), contractDTO.getRealTimeQuote(), oaeTradeConfig.getPriceUpSize());
        if (executeAggregate.getQuantity() > oaeTradeConfig.getOddMinQuantity()) {
            executeAggregate.buildExecutionOrder();
            executionRepository.insert(Lists.newArrayList(executeAggregate.getExecutionOrderValObj()));
            // place odd order
            ExecutionOrderValObj executionOrderValObj = executeAggregate.getExecutionOrderValObj();
            PlaceOrderFacadeContext placeOrderFacadeContext =
                    OrderFacadeContextBuilder.orderContextBuilder(executionOrderValObj.getExecutionOrder().getSource(), executionOrderValObj.getExecutionOrder().getMarket(), executionOrderValObj.getExecutionOrder().getSecType())
                            .createOdd(executionOrderValObj.getExecutionOrder());
            OrderFacadeDTO orderFacadeDTO = orderApiFacade.placeOddOrder(placeOrderFacadeContext);
            executionOrderValObj.updateAfterPlaceOrder(orderFacadeDTO);
            executionRepository.update(executionOrderValObj);
            if (Objects.isNull(orderFacadeDTO) || Boolean.FALSE.equals(orderFacadeDTO.isSucc())) {
                String errMsg = String.format("place odd execution aggregation order fail: ExecutionAggregationOrderId-%s",
                        executionOrderValObj.getExecutionOrderId());
                log.error(errMsg);
                alertService.sendMsg(errMsg);
                return ExecuteAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NO_FILLED_ORDERS);
            }
            return ExecuteAggregationOrderDTO.createSuccDTO();
        } else {
            return ExecuteAggregationOrderDTO.createFailDTO(ExecutionErrorCode.NOT_SATISFY_MINIMIZE_QUANTITY);
        }
    }

    /**
     * 聚合订单是否已有碎股单
     * @param aggregationOrderId
     */
    public boolean hasOddOrder(Long aggregationOrderId) {
        List<AggregationExecutionOrder> executionOrderList = executionRepository.getExecutionOrderList(aggregationOrderId);
        return executionOrderList.stream().anyMatch(executionOrder -> Boolean.TRUE.equals(ExecutionOrderType.ODD.name().equals(executionOrder.getType())));
    }

    /**
     * 取消未成交订单
     * @param businessSource
     * @param stockMarket
     * @param secType
     */
    public void cancelOrder(BusinessSource businessSource, CommonConsts.StockMarket stockMarket, SecType secType) {
        List<AggregationExecutionOrder> executionOrderList = executionRepository.getExecutionOrderByStatus(businessSource, stockMarket, secType,
                TradeStatus.UNFINISHED_STATUS_VALUE).stream()
                .filter(item -> item.getFilledQuantity() == null || BigDecimalUtil.isEqual(item.getFilledQuantity(), 0))
                .collect(Collectors.toList());
        executionOrderList.forEach(executionOrder -> {
            CancelExecutionOrderAggregate cancelAggregate = new CancelExecutionOrderAggregate(executionOrder);
            AggregationExecutionOrderRecord recordOrder = cancelAggregate.buildCancelOrder();
            executionRepository.insert(recordOrder);
            // 撤单
            OrderFacadeDTO orderFacadeDTO = orderApiFacade.cancelOrder(
                    CancelOrderFacadeContext.create(cancelAggregate.getExecutionOrder()));
            executionRepository.update(cancelAggregate.getValObjAfterCancelOrder(orderFacadeDTO));
            if (Objects.isNull(orderFacadeDTO) || Boolean.FALSE.equals(orderFacadeDTO.isSucc())) {
                String errMsg = String.format("cancel execution order fail: ExecutionAggregationOrderId-%s, symbol-%s",
                        executionOrder.getId(), recordOrder.getSymbol());
                log.error(errMsg);
                alertService.sendMsg(errMsg);
            } else {
                log.info("success cancel execution order {}", executionOrder.getId());
            }
        });
    }

    /**
     * 若AggregationExecutionOrder未成交
     * 执行改单策略
     */
    private void modifyExecutionOrder() {
        while (true) {
            if (Boolean.TRUE.equals(oaeTradeConfig.isEnableModifyOrder())) {
                doModifyExecutionOrder();
            } else {
                try {
                    log.info("modifyExecutionOrder is disable");
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    log.error("modifyExecutionOrder sleep error", e);
                }
            }
        }
    }

    private void doModifyExecutionOrder() {
        try {
            ExecutionOrderMsg msg = executionOrderQueue.take();
            AggregationExecutionOrder executionOrder = executionRepository
                    .getExecutionOrder(msg.getExecutionOrderId());
            ModifyExecutionOrderAggregate modifyAggregate = new ModifyExecuteOrderAggregateBuilder(executionOrder,
                    msg.getRefAmount(), oaeTradeConfig).getExecuteOrderAggregate();
            if (BooleanUtils.isTrue(modifyAggregate.needModify())) {
                if (modifyAggregate.isMiddleClose()) {
                    // 午间休市，不处理改单
                    addExecutionOrderQueue(msg);
                } else {
                    // 执行改单
                    processModify(msg, modifyAggregate);
                }
            } else {
                log.info("execution order do not need modify: ExecutionOrder-{}", executionOrder);
            }
        } catch (Exception e) {
            String errMsg = String.format("modify execution order error: %s", e.getMessage());
            log.error("modifyExecutionOrder {}", errMsg, e);
            alertService.sendMsg(errMsg);
        }
    }

    private void processModify(ExecutionOrderMsg msg, ModifyExecutionOrderAggregate modifyAggregate) {
        ContractDTO contractDTO = quoteService.getContract(modifyAggregate.getSymbol(), modifyAggregate.getSecType());
        if (Objects.isNull(contractDTO)) {
            // 缺少合约行情相关数据
            String errorMsg = String.format("modify execution order, no quote data: ExecutionAggregationOrderId-%s, symbol-%s",
                    msg.getExecutionOrderId(), modifyAggregate.getSymbol());
            log.error(errorMsg);
            return;
        }
        if (BooleanUtils.isTrue(modifyAggregate.needModify(contractDTO.getRealTimeQuote()))) {
            log.info("modify execution order: {}", modifyAggregate.getExecutionOrder());
            // 创建改单记录
            AggregationExecutionOrderRecord recordOrder = modifyAggregate.buildModifyOrder(contractDTO.getContractInfo(),
                    contractDTO.getRealTimeQuote());
            if (modifyAggregate.needPlace()) {
                executionRepository.insert(recordOrder);
                // 改单
                OrderFacadeDTO orderFacadeDTO = orderApiFacade.modifyOrder(
                        ModifyOrderFacadeContext.create(modifyAggregate.getExecutionOrder(), recordOrder));
                executionRepository.update(modifyAggregate.getValObjAfterModifyOrder(orderFacadeDTO));
                if (Objects.isNull(orderFacadeDTO) || Boolean.FALSE.equals(orderFacadeDTO.isSucc())) {
                    String errMsg = String.format("modify execution order fail: ExecutionAggregationOrderId-%s, symbol-%s",
                            msg.getExecutionOrderId(), modifyAggregate.getSymbol());
                    log.error(errMsg);
                    alertService.sendMsg(errMsg);
                }
            } else {
                log.info("modify execution order quantity is less than 0 or less than filled quantity: {}", recordOrder);
            }
        } else {
            log.info("execution order price is more than real time quote: executionOrder-{}, realTimeQuote-{}",
                    modifyAggregate.getExecutionOrder(), contractDTO.getRealTimeQuote().getClose());
        }
        // 延时队列
        msg.expire(oaeTradeConfig.getModifyTimeout(), ExecutionConst.MODIFY_TIMEUNIT);
        addExecutionOrderQueue(msg);
    }

    /**
     * 交易订单重新推入延时队列
     */
    public void pushExecutionOrderQueue(Long executionOrderId) {
        Double refAmount = executionRepository.getRefAmount(executionOrderId);
        if (refAmount != null) {
            // 延时队列
            ExecutionOrderMsg msg = new ExecutionOrderMsg(executionOrderId,
                    refAmount, oaeTradeConfig.getModifyTimeout(), ExecutionConst.MODIFY_TIMEUNIT);
            addExecutionOrderQueue(msg);
        } else {
            log.error("push execution order to queue, no ref amount");
        }
    }

    /**
     * 从交易中台同步订单状态
     */
    public void syncExecutionOrderInfo(ExecutionOrderContext context) {
        AggregationExecutionOrder executionOrder = executionRepository.getExecutionOrderByTradeId(
                String.valueOf(context.getId()));
        // 数据库存储的executionOrder如果已经是完成状态，不可再修改更新
        if (executionOrder != null) {
            if (!TradeStatus.isFinishedStatus(executionOrder.getTradeStatus())) {
                AggregationExecutionOrder updateExecutionOrder = context.toExecutionOrder(executionOrder);
                if (null != executionOrder.getFilledQuantity() && executionOrder.getFilledQuantity() > 0) {
                    TradeDTO tradeDTO = orderApiFacade.getTradeByOrderId(context.getId());
                    if (tradeDTO != null) {
                        //根据orderId查询trade信息;
                        updateExecutionOrder.setFilledTime(tradeDTO.getTransactedAt());
                        updateExecutionOrder.setBusinessDate(tradeDTO.getBusinessDate());
                        updateExecutionOrder.setSettleDate(tradeDTO.getSettlementDate());
                        updateExecutionOrder.setNavDate(tradeDTO.getNavDate());
                    } else {
                        String errMsg = String.format("get trade by order id error, orderId-%s", context.getId());
                        log.error(errMsg);
                        alertService.sendMsg(errMsg);
                    }
                }
                executionRepository.updateExecutionOrderByTradeId(updateExecutionOrder);
            }
            if (TradeStatus.FILLED.getValue().equalsIgnoreCase(context.getStatus())
                && context.getAttrList().contains(OrderAttr.COMPLETE_FUND.name())) {
                List<UserOrder> userOrders = userOrderRepository.getUserOrders(executionOrder.getAggregationId());
                lockedAssetService.unlockSettleAsset(userOrders.stream().map(UserOrder::getId).collect(Collectors.toList()));
            }
        }
    }

    // 根据filled quantity 和 avg price 计算executionOrder的成交金额并返回数值
    private Double calculateExecutionOrderFilledAmount(List<AggregationExecutionOrder> executionOrder) {
        return executionOrder.stream()
                .filter(item -> item.getFilledQuantity() != null && item.getAvgPrice() != null)
                .map(item -> BigDecimalUtil.multiply(item.getFilledQuantity(), item.getAvgPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();
    }

}
