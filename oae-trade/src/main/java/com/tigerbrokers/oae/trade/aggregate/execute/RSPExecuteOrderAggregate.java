package com.tigerbrokers.oae.trade.aggregate.execute;

import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;

public class RSPExecuteOrderAggregate extends ExecuteAggregationOrderAggregate {
    public RSPExecuteOrderAggregate(AggregationOrder aggregationOrder, ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote, Integer priceUpSize,
                                    Long modifyTimeout) {
        super(aggregationOrder, contractInfo, realTimeQuote, priceUpSize, modifyTimeout);
    }

}
