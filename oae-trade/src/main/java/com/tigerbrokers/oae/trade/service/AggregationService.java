package com.tigerbrokers.oae.trade.service;

import cn.hutool.core.util.BooleanUtil;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.*;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.aggregate.AggregationAggregate;
import com.tigerbrokers.oae.trade.aggregate.valobj.UserAggregationOrderValObj;
import com.tigerbrokers.oae.trade.context.ExecuteAggregationOrderContext;
import com.tigerbrokers.oae.trade.dto.ExecuteAggregationOrderDTO;
import com.tigerbrokers.oae.trade.dto.ExecuteFilledAggregationOrderDTO;
import com.tigerbrokers.oae.trade.repository.AggregationRepository;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户订单聚合管理服务
 *
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class AggregationService {

    @Autowired
    private UserOrderRepository userOrderRepository;

    @Autowired
    private AggregationRepository aggregationRepository;

    @Autowired
    private ExecutionService executionService;

    @Autowired
    private LockedAssetService lockedAssetService;

    /**
     * 用户订单聚合
     */
    public void processAggregation(BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        // 用户订单聚合
        List<UserOrder> userOrders = userOrderRepository.getUserOrders(UserOrderStatus.NEW, source, market, secType);
        AggregationAggregate aggregationAggregate = new AggregationAggregate();
        List<UserAggregationOrderValObj> userAggregationOrders = aggregationAggregate
                .buildUserAggregationOrderList(userOrders);
        // 过滤正在处理的聚合订单
        userAggregationOrders = userAggregationOrders.stream()
                .filter(agg -> CollectionUtils.isEmpty(aggregationRepository.getAggregationOrderList(agg.getAggregationOrder(), AggregationOrderStatus.PROCESSING_STATUS)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userAggregationOrders)) {
            log.info("no user order need to be aggregated: source-{} secType-{}", source, secType);
            return;
        }
        aggregationRepository.insert(userAggregationOrders);
        // 拆单&下单
        for (UserAggregationOrderValObj userAggregationOrder : userAggregationOrders) {
            ExecuteAggregationOrderDTO executionDTO = executionService.executeAggregationOrder(
                    new ExecuteAggregationOrderContext(userAggregationOrder.getAggregationOrder()));
            if (BooleanUtil.isFalse(executionDTO.getSucc())) {
                log.info("execution aggregation order fail: UserAggregationOrderId-{}, ErrorCode-{}",
                        userAggregationOrder.getAggregationOrder().getId(), executionDTO.getErrorCode());
                // 执行下单失败数据更新
                userAggregationOrder.updateAfterExecutionFail(executionDTO);
                aggregationRepository.update(userAggregationOrder);
                if (AggregationOrderStatus.REJECTED.name().equals(userAggregationOrder.getAggregationOrder().getStatus())) {
                    // 资产解锁标记
                    lockedAssetService.unlockPending(userAggregationOrder.getUserOrderIds(), LockStep.PLACE_ORDER);
                }
            }
        }
    }

    /**
     * 碎股单
     * 用户碎股单聚合
     * @param businessSource
     */
    public void processODDAggregation(BusinessSource businessSource, CommonConsts.StockMarket market) {
        // 处理已终态聚合订单
        processFilledAggregation(businessSource, market);
        // 获取需要下碎股单的聚合订单
        List<AggregationOrder> aggregationOrderList =
                aggregationRepository.getAggregationOrderList(AggregationOrderStatus.WAITING_ODD, businessSource, market, SecType.STK);
        if (CollectionUtils.isEmpty(aggregationOrderList)) {
            log.info("no aggregation order need to be place odd order: businessSource-{}", businessSource);
            return;
        }
        // 下单
        for (AggregationOrder aggregationOrder : aggregationOrderList) {
            // 判断是否已有碎股单
            if (executionService.hasOddOrder(aggregationOrder.getId())) {
                log.info("aggregation order has odd order: UserAggregationOrderId-{}", aggregationOrder.getId());
                continue;
            }
            ExecuteAggregationOrderDTO executionDTO = executionService.executeOddAggregationOrder(
                    new ExecuteAggregationOrderContext(aggregationOrder));
            if (BooleanUtil.isFalse(executionDTO.getSucc())) {
                log.info("execution add aggregation order fail: UserAggregationOrderId-{}, ErrorCode-{}",
                        aggregationOrder.getId(), executionDTO.getErrorCode());
                // 执行下单失败数据更新
                UserAggregationOrderValObj userAggregationOrderValObj = buildOddOrderValueObj(aggregationOrder);
                userAggregationOrderValObj.updateAfterOddExecutionFail(executionDTO);
                aggregationRepository.update(userAggregationOrderValObj);
                if (AggregationOrderStatus.REJECTED.name().equals(userAggregationOrderValObj.getAggregationOrder().getStatus())
                        && CollectionUtils.isNotEmpty(userAggregationOrderValObj.getUserOrderIds())) {
                    // 资产解锁标记
                    lockedAssetService.unlockPending(userAggregationOrderValObj.getUserOrderIds(), LockStep.PLACE_ORDER);
                }
            } else {
                // 下单成功，更新聚合订单状态
                aggregationOrder.setStatus(AggregationOrderStatus.NEW.name());
                aggregationRepository.update(aggregationOrder);
            }
        }
    }

    /**
     * 碎股单
     * 处理已终态聚合订单
     * @param businessSource
     */
    private void processFilledAggregation(BusinessSource businessSource, CommonConsts.StockMarket market) {
        List<AggregationOrder> aggregationOrderList = aggregationRepository.getAggregationOrderList(
                AggregationOrderStatus.NEW, businessSource, market, SecType.STK);
        if (CollectionUtils.isEmpty(aggregationOrderList)) {
            return;
        }
        for (AggregationOrder aggregationOrder : aggregationOrderList) {
            if (executionService.hasOddOrder(aggregationOrder.getId())) {
                log.info("aggregation order has odd order: UserAggregationOrderId-{}", aggregationOrder.getId());
                continue;
            }
            ExecuteFilledAggregationOrderDTO executionDTO = executionService.executeFilledAggregationOrder(
                    new ExecuteAggregationOrderContext(aggregationOrder));
            if (BooleanUtil.isFalse(executionDTO.getSucc())) {
                log.info("execution filled aggregation order fail: UserAggregationOrderId-{}, ErrorCode-{}",
                        aggregationOrder.getId(), executionDTO.getErrorCode());
                // 执行下单失败数据更新
                AggregationOrder updateAggregationOrder = buildFilledOrderExecutionFail(aggregationOrder, executionDTO);
                if (Objects.nonNull(updateAggregationOrder)) {
                    aggregationRepository.update(updateAggregationOrder);
                }
            }
        }
    }

    private AggregationOrder buildFilledOrderExecutionFail(AggregationOrder aggregationOrder,
                                                           ExecuteFilledAggregationOrderDTO executionDTO) {
        AggregationOrder result = null;
        if (ExecutionErrorCode.NEED_PLACE_ODD.equals(executionDTO.getErrorCode())) {
            result = AggregationOrder.builder()
                    .id(aggregationOrder.getId())
                    .filledAmount(executionDTO.getFilledAmount())
                    .status(AggregationOrderStatus.WAITING_ODD.name())
                    .build();
        }
        return result;
    }

    private UserAggregationOrderValObj buildOddOrderValueObj(AggregationOrder aggregationOrder) {
        List<UserOrder> userOrders = null;
        if (Objects.isNull(aggregationOrder.getFilledAmount()) || 0 >= aggregationOrder.getFilledAmount()) {
            userOrders = userOrderRepository.getUserOrders(aggregationOrder.getId());
        }
        return UserAggregationOrderValObj.builder()
                .aggregationOrder(aggregationOrder)
                .userOrders(userOrders)
                .build();
    }
}
