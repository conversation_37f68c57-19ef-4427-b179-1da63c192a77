package com.tigerbrokers.oae.trade.repository;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.LockedAssetStatus;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.LockedAssetDao;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.stock.common.CommonConsts;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.*;
import java.util.function.Function;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Service
public class LockedAssetRepository {

    @Autowired
    private LockedAssetDao lockedAssetDao;

    public void insert(LockedAsset lockedAsset) {
        lockedAssetDao.insert(lockedAsset);
    }

    public void insertOrUpdate(LockedAsset lockedAsset) {
        lockedAssetDao.insertOrUpdate(lockedAsset);
    }

    public void updateLockedAsset(LockedAsset lockedAsset) {
        lockedAsset.setUpdatedAt(DateTime.now());
        lockedAssetDao.update(lockedAsset);
    }

    public void batchUpdateLocked(Collection<LockedAsset> lockedAssets) {
        batchUpdate(lockedAssets, LockedAssetStatus.LOCKED);

    }

    public void batchUpdateLockedError(Collection<LockedAsset> lockedAssets) {
        batchUpdate(lockedAssets, LockedAssetStatus.LOCKED_ERROR);
    }

    public void batchUpdate(Collection<LockedAsset> lockedAssets, LockedAssetStatus status) {
        if (CollectionUtils.isEmpty(lockedAssets)) {
            return;
        }
        lockedAssets.forEach(lockedAsset -> {
            lockedAsset.setStatus(status.name());
            lockedAsset.setUpdatedAt(DateTime.now());
        });
        lockedAssetDao.batchUpdate(new ArrayList<>(lockedAssets));
    }

    public void batchUpdateUnlockedPendingStatusByUserOrderId(List<Long> userOrderIds, LockStep lockStep) {
        batchUpdateByUserOrderIds(userOrderIds, lockStep, LockedAssetStatus.UNLOCKED_PENDING);
    }


    public void batchUpdateUnlockedByUserOrderIdAndStep(List<Long> userOrderIds, LockStep lockStep) {
        batchUpdateByUserOrderIds(userOrderIds, lockStep, LockedAssetStatus.UNLOCKED);
    }

    public void batchUpdateUnlockedErrorByUserOrderIdAndStep(List<Long> userOrderIds, LockStep lockStep) {
        batchUpdateByUserOrderIds(userOrderIds, lockStep, LockedAssetStatus.UNLOCKED_ERROR);
    }

    public void batchUpdateByUserOrderIds(List<Long> userOrderIds, LockStep lockStep, LockedAssetStatus status) {
        if (CollectionUtils.isEmpty(userOrderIds)) {
            return;
        }
        List<LockedAsset> lockedAssetList = lockedAssetDao.selectByUserOrderIdsAndStep(userOrderIds, lockStep);
        if (CollectionUtils.isEmpty(lockedAssetList)) {
            return;
        }
        List<LockedAsset> updateLockedAssetList = lockedAssetList.stream()
                .map(item -> LockedAsset.builder().id(item.getId()).status(status.name()).build())
                .collect(Collectors.toList());
        lockedAssetDao.batchUpdate(updateLockedAssetList);
    }

    public List<LockedAsset> getPendingLockedAsset(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType) {
        return lockedAssetDao.selectByStatus(LockedAssetStatus.UNLOCKED_PENDING.name(), businessSource, market, secType);
    }

    public Map<Long, LockedAsset> getMapByUserOrderIds(List<Long> userOrderIds, LockStep lockStep) {
        List<LockedAsset> lockedAssetList = getByUserOrderIds(userOrderIds, lockStep);
        if (CollectionUtils.isNotEmpty(lockedAssetList)) {
            return lockedAssetList.stream().collect(Collectors.toMap(LockedAsset::getUserOrderId, Function.identity()));
        }
        return new HashMap<>();
    }

    public List<LockedAsset> getByUserOrderIds(List<Long> userOrderIds, LockStep lockStep) {
        if (CollectionUtils.isEmpty(userOrderIds)) {
            return new ArrayList<>();
        }
        return lockedAssetDao.selectByUserOrderIdsAndStep(userOrderIds, lockStep);
    }

    public LockedAsset getByUserOrderId(Long userOrderId, LockedAssetStatus status, LockStep lockStep) {
        return lockedAssetDao.getByUserOrderId(userOrderId, status, lockStep);
    }

    public Map<Long, LockedAsset> getByUserOrderIds(List<Long> userOrderIds) {
        if (CollectionUtils.isEmpty(userOrderIds)) {
            return new HashMap<>();
        }
        List<LockedAsset> lockedAssetList = lockedAssetDao.selectByUserOrderIds(userOrderIds);
        if (CollectionUtils.isNotEmpty(lockedAssetList)) {
            return lockedAssetList.stream().collect(Collectors.toMap(LockedAsset::getUserOrderId, Function.identity()));
        }
        return new HashMap<>();
    }

    public List<LockedAsset> getLockedAssetByIds(List<Long> ids) {
        return lockedAssetDao.selectByPrimaryIds(ids);
    }
}
