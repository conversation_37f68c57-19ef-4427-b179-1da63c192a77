package com.tigerbrokers.oae.trade.mapper;

import com.tigerbrokers.oae.api.PlaceOrderInfo;
import com.tigerbrokers.oae.api.PlaceOrderReply;
import com.tigerbrokers.oae.api.error.ErrorCode;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public class PlaceOrderReplyMapper {

    public static PlaceOrderReply createErrorReply(ErrorCode errorCode, Object... params) {
        return PlaceOrderReply.newBuilder().setIsSucc(Boolean.FALSE).setErrorCode(errorCode.name())
                .setErrorMsg(errorCode.getMsg(params)).build();
    }

    public static PlaceOrderReply createReply(UserOrder userOrder, ErrorCode errorCode) {
        if (errorCode == null) {
            PlaceOrderInfo info = PlaceOrderInfo.newBuilder().setId(userOrder.getId())
                    .setExternalId(userOrder.getExternalId()).build();
            return PlaceOrderReply.newBuilder().setIsSucc(Boolean.TRUE).setPlaceOrderInfo(info).build();
        } else {
            return createErrorReply(errorCode);
        }
    }
}
