package com.tigerbrokers.oae.trade.service;

import com.tigerbrokers.alpha.commons.utils.TextUtils;
import com.tigerbrokers.oae.api.PlaceOrderReply;
import com.tigerbrokers.oae.api.PlaceOrderRequest;
import com.tigerbrokers.oae.api.error.ErrorCode;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.facade.ContractRpcFacade;
import com.tigerbrokers.oae.facade.AccountFacade;
import com.tigerbrokers.oae.facade.OrderApiFacade;
import com.tigerbrokers.oae.facade.context.ContractFacadeContext;
import com.tigerbrokers.oae.facade.context.QueryAccountFacadeContext;
import com.tigerbrokers.oae.facade.context.placeOrder.OrderFacadeContextBuilder;
import com.tigerbrokers.oae.facade.dto.*;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.aggregate.userOrder.AbstractUserOrderAggregate;
import com.tigerbrokers.oae.trade.aggregate.UserOrderRelateTradeAggregate;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import com.tigerbrokers.oae.trade.context.UserOrderRelateTradeContext;
import com.tigerbrokers.oae.trade.dto.LockAssetDTO;
import com.tigerbrokers.oae.trade.mapper.PlaceOrderReplyMapper;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 用户订单订单管理服务
 *
 * <AUTHOR>
 * @date 2023/1/4
 */
@Slf4j
@Service
public class UserOrderService {

    @Autowired
    private LockedAssetService lockedAssetService;

    @Autowired
    private PublishService publishService;

    @Autowired
    private UserOrderRepository userOrderRepository;

    @Autowired
    private AccountFacade accountFacade;

    @Autowired
    private OrderApiFacade orderApiFacade;

    @Autowired
    private ContractRpcFacade contractRpcFacade;

    @Autowired
    private AlertService alertService;

    /**
     * 接收用户下单
     */
    public PlaceOrderReply placeOrder(PlaceOrderRequest request) {
        ContractInfoDTO contractInfoDTO = contractRpcFacade.queryContractInfo(
                new ContractFacadeContext(request.getSymbol()));
        if (ObjectUtils.anyNull(contractInfoDTO, contractInfoDTO.getContractId())) {
            log.error("query contract info failed, symbol ={}, response ={}", request.getSymbol(), contractInfoDTO);
            return PlaceOrderReplyMapper.createErrorReply(ErrorCode.QUERY_CONTRACT_ERROR);
        }
        // 判断执行上手
        if (BusinessSource.RSP.name().equalsIgnoreCase(request.getSource())) {
            ExecutingBrokerDTO executingBrokerDTO =
                    accountFacade.queryExecutingBroker(Long.parseLong(request.getAccount()),
                            contractInfoDTO.getContractId());
            if (executingBrokerDTO == null || executingBrokerDTO.invalid()) {
                String errMsg = String.format("query executing broker failed, account: %s, contract: %s, broker: %s",
                        request.getAccount(), JacksonUtil.writeValueAsString(contractInfoDTO),
                        JacksonUtil.writeValueAsString(executingBrokerDTO));
                log.error(errMsg);
                alertService.sendMsg(errMsg);
                return PlaceOrderReplyMapper.createErrorReply(ErrorCode.UNSUPPORTED_EXECUTING_BROKER);
            }
        }
        // 上手和交易员账号
        AccountDTO accountDTO = accountFacade.queryAccount(
                new QueryAccountFacadeContext(request.getAccount(), contractInfoDTO, request.getSource()));
        if (accountDTO == null || accountDTO.daAccountInvalid()) {
            String errMsg = String.format("query executing broker failed, account: %s, contract: %s, broker: %s",
                    request.getAccount(), JacksonUtil.writeValueAsString(contractInfoDTO),
                    JacksonUtil.writeValueAsString(accountDTO));
            alertService.sendMsg(errMsg);
            log.error(errMsg);
            return PlaceOrderReplyMapper.createErrorReply(ErrorCode.QUERY_EXECUTION_MASTER_AND_DEAL_ACCOUNT_ERROR);
        }
        // 创建用户订单
        AbstractUserOrderAggregate userOrderAggregate = AbstractUserOrderAggregate.getUserOrderAggregate(request.getSource(), contractInfoDTO.getSecType(), contractInfoDTO.getMarket());
        UserOrder userOrder = userOrderAggregate.buildUserOrder(request, accountDTO);
        userOrderRepository.insert(userOrder);

        // pre trade
        PreTradeFacadeDTO preTradeFacadeDTO = orderApiFacade.preTrade(
                OrderFacadeContextBuilder.orderContextBuilder(userOrder.getSource(), userOrder.getMarket(), userOrder.getSecType()).createPreTradeFacadeContext(userOrder));
        if (preTradeFacadeDTO == null || BooleanUtils.isNotTrue(preTradeFacadeDTO.getIsPass())) {
            userOrderRepository.update(userOrderAggregate.updateAfterPreTrade(preTradeFacadeDTO));
            return PlaceOrderReplyMapper.createErrorReply(ErrorCode.PRE_TRADE_NOT_PASS, preTradeFacadeDTO == null ? "" : preTradeFacadeDTO.getMessage());
        }
        // 锁定资产
        LockAssetContext lockAssetContext = userOrderAggregate.toLockAssetContext(contractInfoDTO, request);
        if (request.getApplyMargin()) {
            CalLockInfoDTO calLockInfoDTO = lockedAssetService.calMarginLockInfo(userOrderAggregate.toCalculateLockAssetContext(request));
            if (calLockInfoDTO.getIsSucc()) {
                lockAssetContext.setDetail(JacksonUtil.writeValueAsString(calLockInfoDTO.getLockInfoList()));
            } else {
                return PlaceOrderReplyMapper.createErrorReply(ErrorCode.CALCULATE_MARGIN_ERROR, calLockInfoDTO.getErrMsg());
            }
        }
        LockAssetDTO lockAssetDTO = lockedAssetService.lockAsset(lockAssetContext);
        // update UserOrder
        userOrderRepository.update(userOrderAggregate.updateAfterLockAsset(lockAssetDTO));

        return PlaceOrderReplyMapper.createReply(userOrderAggregate.getUserOrder(), userOrderAggregate.getErrorCode());
    }

    /**
     * 用户订单同步关联交易信息
     */
    public void syncTradeInfo(UserOrderRelateTradeContext context, String tradeOriMsg) {
        UserOrder userOrder = userOrderRepository.getUserOrder(context.getUserOrderId());
        if (userOrder != null && !UserOrderStatus.isFinishedStatus(userOrder.getStatus())) {
            // 更新交易信息TradeId
            UserOrderRelateTradeAggregate aggregate = new UserOrderRelateTradeAggregate(userOrder);
            UserOrder updateUserOrder = aggregate.updateAfterSyncTradeInfo(context, tradeOriMsg);
            userOrderRepository.update(updateUserOrder);
            // 发布UserOrder订单相关消息
            publishService.publishUserOrder(userOrder.getId());
        }
    }

}
