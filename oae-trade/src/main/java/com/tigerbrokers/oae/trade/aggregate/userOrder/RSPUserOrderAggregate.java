package com.tigerbrokers.oae.trade.aggregate.userOrder;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.api.PlaceOrderRequest;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.facade.consts.OrderConst;
import com.tigerbrokers.oae.facade.context.CalculateLockAssetContext;

public class RSPUserOrderAggregate extends AbstractUserOrderAggregate {

    @Override
    protected Double calTradeAmount(Double amount) {
        if (amount == null) {
            return null;
        }

        return Math.floor((amount - 50) / 1.0016885);
    }

    @Override
    public SecType secType() {
        return SecType.STK;
    }

    @Override
    public CalculateLockAssetContext toCalculateLockAssetContext(PlaceOrderRequest placeOrderRequest) {
        return CalculateLockAssetContext.builder()
                .account(placeOrderRequest.getAccount())
                .action(placeOrderRequest.getAction())
                .orderType(OrderConst.ORDER_TYPE_MKT)
                .secType(placeOrderRequest.getSecType())
                .symbol(placeOrderRequest.getSymbol())
                .externalId(placeOrderRequest.getExternalId())
                .source(BusinessSource.RSP.name())
                .appId(BusinessSource.RSP.name())
                .timeInForce(OrderConst.TIME_IN_FORCE_DAY)
                .quantity(0)
                .amount(String.valueOf(placeOrderRequest.getAmount()))
                .build();
    }
}
