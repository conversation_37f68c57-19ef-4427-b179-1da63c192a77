package com.tigerbrokers.oae.trade.aggregate.execute;

import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.util.OrderUtil;

import java.math.RoundingMode;

public class USDRIPExecuteOrderAggregate extends ExecuteAggregationOrderAggregate {
    public USDRIPExecuteOrderAggregate(AggregationOrder aggregationOrder, ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote, Integer priceUpSize,
                                       Long modifyTimeout) {
        super(aggregationOrder, contractInfo, realTimeQuote, priceUpSize, modifyTimeout);
    }

    @Override
    public Boolean canExecute() {
        return true;
    }

    @Override
    protected Double calOrderQty() {
        Double refPrice = calRefPrice();
        double totalQuantity = aggregationOrder.getTradeAmount() / refPrice;
        //每单不超过30w股
        return Math.ceil(totalQuantity / 300000d);
    }

    @Override
    protected Double calSubAmount(Double orderQty) {
        if (OrderUtil.isBuyAction(aggregationOrder.getAction())) {
            return BigDecimalUtil.divide(aggregationOrder.getTradeAmount(), orderQty,2, RoundingMode.DOWN).doubleValue();
        }
        return null;
    }


    @Override
    protected Double calSubQuantity(Double orderQty, Double amount, Double price) {
        return OrderUtil.calUSOrderQuantity(amount, price);
    }

    @Override
    protected Double calRefPrice() {
        return OrderUtil.calUSUpPrice(getRealTimeQuoteValue(), priceUpSize, contractInfo.getTickerSize());
    }
}
