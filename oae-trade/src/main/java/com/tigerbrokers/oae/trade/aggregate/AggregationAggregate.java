package com.tigerbrokers.oae.trade.aggregate;

import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.aggregate.valobj.AggregationKey;
import com.tigerbrokers.oae.trade.aggregate.valobj.UserAggregationOrderValObj;
import org.apache.commons.collections.CollectionUtils;
import repackaged.com.arakelian.core.com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public class AggregationAggregate {

    private List<UserAggregationOrderValObj> addUserAggregateOrderList;

    public AggregationAggregate() {
        this.addUserAggregateOrderList = Lists.newArrayList();
    }

    private void addUserAggregateOrderList(UserAggregationOrderValObj userAggregationOrder) {
        addUserAggregateOrderList.add(userAggregationOrder);
    }

    public List<UserAggregationOrderValObj> buildUserAggregationOrderList(List<UserOrder> userOrderList) {
        // 尚未聚合的用户订单
        userOrderList = userOrderList.stream().filter(item -> item.getAggregationId() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userOrderList)) {
            return addUserAggregateOrderList;
        }
        Map<AggregationKey, List<UserOrder>> userOrderMap = userOrderList.stream()
                .collect(Collectors.groupingBy(AggregationKey::new));

        userOrderMap.forEach((key, userOrders) -> {
            addUserAggregateOrderList(new UserAggregationOrderValObj(userOrders, key));
        });
        return addUserAggregateOrderList;
    }



}
