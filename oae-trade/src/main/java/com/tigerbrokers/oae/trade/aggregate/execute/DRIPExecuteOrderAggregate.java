package com.tigerbrokers.oae.trade.aggregate.execute;

import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.util.OrderUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DRIPExecuteOrderAggregate extends ExecuteAggregationOrderAggregate {
    public DRIPExecuteOrderAggregate(AggregationOrder aggregationOrder, ContractInfoDTO contractInfo, QuotePointDTO realTimeQuote, Integer priceUpSize,
                                     Long modifyTimeout) {
        super(aggregationOrder, contractInfo, realTimeQuote, priceUpSize, modifyTimeout);
    }

    @Override
    public Boolean needDelayHandle() {
        return false;
    }

    @Override
    public Boolean canExecute() {
        return true;
    }

    //基金分红子订单数量=1
    @Override
    protected Double calOrderQty() {
        return 1d;
    }

    @Override
    protected Double calRefPrice() {
        return null;
    }

    @Override
    protected Double calSubAmount(Double orderQty) {
        if (OrderUtil.isBuyAction(aggregationOrder.getAction())) {
            return BigDecimal.valueOf(aggregationOrder.getTradeAmount() / orderQty).setScale(2, RoundingMode.DOWN).doubleValue();
        }
        return null;
    }

    @Override
    protected Double calSubQuantity(Double orderQty, Double amount, Double price) {
        if (OrderUtil.isBuyAction(aggregationOrder.getAction())) {
            return null;
        }
        int scale = contractInfo.getShareScale();
        return BigDecimal.valueOf(aggregationOrder.getTradeQuantity() / orderQty).setScale(scale, RoundingMode.DOWN).doubleValue();
    }
}
