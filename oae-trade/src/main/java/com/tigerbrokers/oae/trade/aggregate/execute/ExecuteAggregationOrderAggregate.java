package com.tigerbrokers.oae.trade.aggregate.execute;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionRecordOrderOperation;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.util.OrderUtil;
import lombok.Getter;
import repackaged.com.arakelian.core.com.google.common.collect.Lists;

import java.util.List;

/**
 * 聚合订单执行聚合根
 * 聚合订单拆分
 *
 * <AUTHOR>
 * @date 2023/1/6
 */
public abstract class ExecuteAggregationOrderAggregate {

    protected AggregationOrder aggregationOrder;

    @Getter
    protected List<ExecutionOrderValObj> executionOrderValObjList;

    protected ContractInfoDTO contractInfo;

    protected QuotePointDTO realTimeQuote;

    protected Integer priceUpSize;

    @Getter
    private Long modifyTimeout;

    @Getter
    protected Double subAmount;

    public ExecuteAggregationOrderAggregate(AggregationOrder aggregationOrder, ContractInfoDTO contractInfo,
                                            QuotePointDTO realTimeQuote, Integer priceUpSize, Long modifyTimeout) {
        this.aggregationOrder = aggregationOrder;
        this.contractInfo = contractInfo;
        this.realTimeQuote = realTimeQuote;
        this.priceUpSize = priceUpSize;
        this.modifyTimeout = modifyTimeout;

        this.executionOrderValObjList = Lists.newArrayList();
    }

    public Boolean needDelayHandle() {
        return true;
    }

    protected Double getRealTimeQuoteValue() {
        return realTimeQuote.getClose();
    }

    protected Integer getLotSize() {
        return contractInfo.getLotSize();
    }

    private void addExecutionOrderValObjList(ExecutionOrderValObj executionOrderValObj) {
        executionOrderValObjList.add(executionOrderValObj);
    }

    /**
     * 聚合订单是否能执行判断
     * 不足一手情况不执行
     */
    public Boolean canExecute() {
        Double lotQty = calLotQty();

        return BigDecimalUtil.isMoreThan(lotQty, 0.0);
    }

    /**
     * 不足一手时quantity
     */
    public Double getOddQuantity() {
        return Math.floor(aggregationOrder.getTradeAmount() / calRefPrice());
    }

    public void buildExecutionOrderList() {
        // 拆分订单数量
        Double orderQty = calOrderQty();
        // 子订单金额
        subAmount = calSubAmount(orderQty);
        //限价
        Double refPrice = calRefPrice();
        // 子订单股数
        Double subQuantity = calSubQuantity(orderQty, subAmount, refPrice);

        for (int i = 0; i < orderQty; i++) {
            AggregationExecutionOrder executionOrder = AggregationExecutionOrder.builder()
                    .aggregationId(aggregationOrder.getId())
                    .dealAccount(aggregationOrder.getDealAccount())
                    .executionMaster(aggregationOrder.getExecutionMaster())
                    .symbol(aggregationOrder.getSymbol())
                    .market(aggregationOrder.getMarket())
                    .currency(aggregationOrder.getCurrency())
                    .action(aggregationOrder.getAction())
                    .totalQuantity(subQuantity)
                    .tradeAmount(subAmount)
                    .price(refPrice)
                    .orderType(aggregationOrder.getOrderType())
                    .segType(aggregationOrder.getSegType())
                    .secType(aggregationOrder.getSecType())
                    .tradeTime(DateTime.now())
                    .status(ExecutionOrderStatus.PENDING.name())
                    .source(aggregationOrder.getSource())
                    .build();
            AggregationExecutionOrderRecord recordOrder = AggregationExecutionOrderRecord.builder()
                    .aggregationId(aggregationOrder.getId())
                    .symbol(aggregationOrder.getSymbol())
                    .market(aggregationOrder.getMarket())
                    .currency(aggregationOrder.getCurrency())
                    .action(aggregationOrder.getAction())
                    .operation(ExecutionRecordOrderOperation.PLACE.name())
                    .amount(subAmount)
                    .refPrice(refPrice)
                    .quantity(subQuantity)
                    .lotSizeUnit(getLotSize())
                    .status(ExecutionOrderStatus.PENDING.name())
                    .build();
            ExecutionOrderValObj executionOrderValObj = ExecutionOrderValObj.builder().executionOrder(executionOrder)
                    .recordOrder(recordOrder).build();
            addExecutionOrderValObjList(executionOrderValObj);
        }
    }

    public Double getTradeAmount() {
        return aggregationOrder.getTradeAmount();
    }

    // 子订单金额
    protected Double calSubAmount(Double orderQty) {
        return aggregationOrder.getTradeAmount() / orderQty;
    }

    protected Double calSubQuantity(Double orderQty, Double amount, Double price) {
        return OrderUtil.calQuantity(amount, price, getLotSize());
    }

    protected Double calOrderQty() {
        Double lotQty = calLotQty();
        return Math.ceil(lotQty / 100);
    }

    /**
     * 计算总共手数
     */
    private Double calLotQty() {
        return OrderUtil.calLotQty(aggregationOrder.getTradeAmount(), calRefPrice(), getLotSize());
    }

    protected Double calRefPrice() {
        return OrderUtil.calUpPrice(getRealTimeQuoteValue(), priceUpSize, contractInfo.getTickerSize());
    }
}
