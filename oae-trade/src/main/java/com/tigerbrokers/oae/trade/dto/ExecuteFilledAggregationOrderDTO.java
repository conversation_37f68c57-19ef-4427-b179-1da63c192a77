package com.tigerbrokers.oae.trade.dto;

import com.tigerbrokers.oae.entity.consts.ExecutionErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteFilledAggregationOrderDTO {

    private Boolean succ;

    private ExecutionErrorCode errorCode;

    private Double filledAmount;

    public static ExecuteFilledAggregationOrderDTO createSuccDTO() {
        return ExecuteFilledAggregationOrderDTO.builder().succ(Boolean.TRUE).build();
    }

    public static ExecuteFilledAggregationOrderDTO createFailDTO(ExecutionErrorCode errorCode) {
        return ExecuteFilledAggregationOrderDTO.builder().succ(Boolean.FALSE).errorCode(errorCode).build();
    }
}
