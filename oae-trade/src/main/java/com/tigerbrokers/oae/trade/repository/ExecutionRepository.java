package com.tigerbrokers.oae.trade.repository;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderDao;
import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderHistoryDao;
import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderRecordDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderHistory;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.mapper.AggregationExecutionOrderHistoryMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class ExecutionRepository {

    @Autowired
    private AggregationExecutionOrderDao executionOrderDao;

    @Autowired
    private AggregationExecutionOrderRecordDao recordDao;

    @Autowired
    private AggregationExecutionOrderHistoryDao historyDao;

    @Transactional(value = "oaeTransactionManager", rollbackFor = Exception.class)
    public void insert(List<ExecutionOrderValObj> executionOrderValObjList) {
        for (ExecutionOrderValObj item : executionOrderValObjList) {
            Long executionOrderId = insertExecutionOrder(item.getExecutionOrder());
            AggregationExecutionOrderRecord recordOrder = item.getRecordOrder();
            recordOrder.setAggregationExecutionId(executionOrderId);
            recordDao.insert(recordOrder);
        }
    }

    public void update(ExecutionOrderValObj executionOrderValObj) {
        updateExecutionOrder(executionOrderValObj.getExecutionOrder());
        update(executionOrderValObj.getRecordOrder());
    }

    private void updateExecutionOrder(AggregationExecutionOrder executionOrder) {
        executionOrder.setUpdatedAt(DateTime.now());
        executionOrderDao.update(executionOrder);
        insertExecutionOrderHistory(executionOrder);
    }

    public void updateExecutionOrderByTradeId(AggregationExecutionOrder executionOrder) {
        executionOrder.setUpdatedAt(DateTime.now());
        executionOrderDao.updateByIdAndTradeId(executionOrder);
        insertExecutionOrderHistory(executionOrder);
    }

    private void insertExecutionOrderHistory(AggregationExecutionOrder executionOrder) {
        AggregationExecutionOrderHistory historyOrder = AggregationExecutionOrderHistoryMapper
                .INSTANCE.from(executionOrder);
        historyDao.insert(historyOrder);
    }

    public void update(AggregationExecutionOrderRecord recordOrder) {
        recordOrder.setUpdatedAt(DateTime.now());
        recordDao.update(recordOrder);
    }

    private Long insertExecutionOrder(AggregationExecutionOrder executionOrder) {
        Long executionOrderId = executionOrderDao.insert(executionOrder);
        AggregationExecutionOrderHistory historyOrder = AggregationExecutionOrderHistoryMapper
                .INSTANCE.from(executionOrder);
        historyDao.insert(historyOrder);

        return executionOrderId;
    }

    public void insert(AggregationExecutionOrderRecord recordOrder) {
        recordDao.insert(recordOrder);
    }

    public AggregationExecutionOrder getExecutionOrder(Long executionOrderId) {
        return executionOrderDao.select(executionOrderId);
    }

    public AggregationExecutionOrder getExecutionOrderByTradeId(String tradeId) {
        return executionOrderDao.selectByTradeId(tradeId);
    }

    public List<AggregationExecutionOrder> getExecutionOrderList(Long aggregationId) {
        return executionOrderDao.selectByAggregationId(aggregationId);
    }

    /**
     * 执行订单的参考金额
     */
    public Double getRefAmount(Long executionOrderId) {
        List<AggregationExecutionOrderRecord> recordList = recordDao.selectByExecutionId(executionOrderId);
        if (CollectionUtils.isNotEmpty(recordList)) {
            return recordList.get(0).getAmount();
        }

        return null;
    }

    public List<AggregationExecutionOrder> getExecutionOrderByStatus(BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType, List<String> status) {
        return executionOrderDao.selectByStatus(businessSource, market, secType, status);
    }
}
