package com.tigerbrokers.oae.trade.service;

import com.tigerbrokers.oae.entity.service.AlertService;
import com.tigerbrokers.oae.storage.DBConfig;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.storage.kafka.KafkaUtil;
import com.tigerbrokers.oae.storage.kafka.data.PublishEvent;
import com.tigerbrokers.oae.storage.kafka.data.UserOrderEvent;
import com.tigerbrokers.oae.storage.kafka.mapper.UserOrderEventMapper;
import com.tigerbrokers.oae.trade.repository.UserOrderRepository;
import com.tigerbrokers.tools.extra.parse.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@Service
public class PublishService {

    @Autowired
    private UserOrderRepository userOrderRepository;

    @Autowired
    private AlertService alertService;

    private DBConfig.OaeKafkaConfig oaeKafkaConfig;

    private KafkaProducer<String, String> kafkaProducer;

    @Autowired
    public PublishService(DBConfig.OaeKafkaConfig oaeKafkaConfig) {
        this.oaeKafkaConfig = oaeKafkaConfig;
        this.kafkaProducer = new KafkaProducer<>(KafkaUtil.getOrderPublishProperties(oaeKafkaConfig));
    }

    /**
     * 用户订单结果发布
     */
    public void publishUserOrder(Long userOrderId) {
        UserOrder userOrder = userOrderRepository.getUserOrder(userOrderId);
        if (userOrder != null) {
            UserOrderEvent userOrderEvent = UserOrderEventMapper.INSTANCE.from(userOrder);
            PublishEvent publishEvent = PublishEvent.createUserOrderPublishEvent(userOrderEvent);
            publishUserOrder(publishEvent);
        }
    }

    public void publishUserOrder(List<Long> userOrderIdList) {
        for (Long userOrderId : userOrderIdList) {
            publishUserOrder(userOrderId);
        }
    }

    private boolean publishUserOrder(PublishEvent event) {
        try {
            String eventInfo = JacksonUtil.writeValueAsString(event);
            log.info("publish {}", eventInfo);
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(oaeKafkaConfig.getTopic(), eventInfo);
            kafkaProducer.send(producerRecord);
            return true;
        } catch (Exception e) {
            log.error("publishUserOrder {}", e.getMessage(), e);
            alertService.sendMsg(String.format("Failed publish data: %s", event));
        }

        return false;
    }
}
