package com.tigerbrokers.oae.trade.repository;

import cn.hutool.core.date.DateTime;
import com.google.common.collect.Lists;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.AggregationOrderStatus;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.AggregationOrderDao;
import com.tigerbrokers.oae.storage.jdbc.UserOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.aggregate.valobj.UserAggregationOrderValObj;
import com.tigerbrokers.stock.common.CommonConsts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Slf4j
@Service
public class AggregationRepository {

    @Autowired
    private UserOrderDao userOrderDao;

    @Autowired
    private AggregationOrderDao aggregationOrderDao;

    public void insert(List<UserAggregationOrderValObj> userAggregationOrderList) {
        for (UserAggregationOrderValObj userAggregationOrder : userAggregationOrderList) {
            // 插入AggregationOrder记录
            Long aggregationId = aggregationOrderDao.insert(userAggregationOrder.getAggregationOrder());
            // 更新UserOrders.aggregation_id
            List<UserOrder> userOrders = userAggregationOrder.getUserOrders();
            List<UserOrder> updateUserOrders = userOrders.stream()
                    .map(item -> UserOrder.builder().id(item.getId()).aggregationId(aggregationId).build())
                    .collect(Collectors.toList());
            userOrderDao.batchUpdate(updateUserOrders);
        }
    }

    public void update(UserAggregationOrderValObj userAggregationOrder) {
        AggregationOrder aggregationOrder = userAggregationOrder.getAggregationOrder();
        aggregationOrder.setUpdatedAt(DateTime.now());
        aggregationOrderDao.update(aggregationOrder);

        List<UserOrder> userOrders = userAggregationOrder.getUserOrders();
        if (CollectionUtils.isNotEmpty(userOrders)) {
            userOrders.forEach(item -> item.setUpdatedAt(DateTime.now()));
            userOrderDao.batchUpdate(userOrders);
        }
    }

    public void update(AggregationOrder aggregationOrder) {
        aggregationOrder.setUpdatedAt(DateTime.now());
        aggregationOrderDao.update(aggregationOrder);
    }

    public List<AggregationOrder> getAggregationOrderList(AggregationOrderStatus status, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        return aggregationOrderDao.selectByStatus(Lists.newArrayList(status.name()), source, market, secType);
    }

    public List<AggregationOrder> getAggregationOrderList(AggregationOrder aggregationOrder, List<String> statusList) {
        return aggregationOrderDao.selectByOrder(aggregationOrder, statusList);
    }
}
