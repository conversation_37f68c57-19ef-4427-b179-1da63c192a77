package com.tigerbrokers.oae.trade.aggregate.modify;

import com.tigerbrokers.oae.entity.consts.ExecutionOrderStatus;
import com.tigerbrokers.oae.entity.consts.ExecutionRecordOrderOperation;
import com.tigerbrokers.oae.entity.consts.TradeStatus;
import com.tigerbrokers.oae.entity.util.BigDecimalUtil;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.OrderFacadeDTO;
import com.tigerbrokers.oae.facade.dto.QuotePointDTO;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.trade.aggregate.valobj.ExecutionOrderValObj;
import com.tigerbrokers.oae.trade.util.OrderUtil;
import com.tigerbrokers.stock.common.CommonConsts;
import com.tigerbrokers.stock.util.HkTradingCalendar;
import com.tigerbrokers.stock.util.HkTradingStatus;
import com.tigerbrokers.stock.util.TimeUtils;
import com.tigerbrokers.stock.util.TradingCalendar;
import lombok.Getter;

/**
 * 执行订单未成交
 * 改单处理
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public class RSPModifyExecutionOrderAggregate extends ModifyExecutionOrderAggregate {

    public RSPModifyExecutionOrderAggregate(AggregationExecutionOrder executionOrder, Double refAmount, Integer priceUpSize) {
        super(executionOrder, refAmount, priceUpSize);
    }
}
