package com.tigerbrokers.oae.trade.config;

import com.ctrip.framework.apollo.spring.annotation.TigerConfigBean;
import lombok.Data;

/**
 * @author: chentao
 * Created on 22/3/2023
 */
public class TradeConfig {

    private static final String TRADE_CONFIG_NAMESPACE = "TradeConfig";

    @TigerConfigBean(namespace = TRADE_CONFIG_NAMESPACE, prefix = "trade")
    @Data
    public static class OAETradeConfig {

        /**
         * 碎股最小股数
         */
        private Long oddMinQuantity;

        /**
         * 改单开关
         */
        private boolean enableModifyOrder;

        /**
         * 价格上浮档位
         */
        private Integer priceUpSize;

        /**
         * 未成交提交改单时间
         */
        private Long modifyTimeout;

        private Long usModifyTimeout;

        private Integer usPriceUpSize;

    }

}
