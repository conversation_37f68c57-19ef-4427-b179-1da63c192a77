package com.tigerbrokers.oae.trade.aggregate.userOrder;

import cn.hutool.core.date.DateTime;
import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.api.PlaceOrderRequest;
import com.tigerbrokers.oae.api.error.ErrorCode;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.UserOrderStatus;
import com.tigerbrokers.oae.entity.util.ServerUtil;
import com.tigerbrokers.oae.facade.context.CalculateLockAssetContext;
import com.tigerbrokers.oae.facade.dto.ContractInfoDTO;
import com.tigerbrokers.oae.facade.dto.AccountDTO;
import com.tigerbrokers.oae.facade.dto.PreTradeFacadeDTO;
import com.tigerbrokers.oae.facade.utils.FacadeUtils;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.trade.context.LockAssetContext;
import com.tigerbrokers.oae.trade.dto.LockAssetDTO;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public abstract class AbstractUserOrderAggregate {

    @Getter
    private UserOrder userOrder;

    @Getter
    private ErrorCode errorCode;

    public static AbstractUserOrderAggregate getUserOrderAggregate(String source, String secType, String market) {
        BusinessSource businessSource = BusinessSource.valueOf(source);
        switch (businessSource) {
            case RSP:
                return new RSPUserOrderAggregate();
            case DRIP:
                if (SecType.FUND.name().equals(secType)) {
                    return new DRIPUserOrderAggregate();
                } else if (FacadeUtils.isUSDrip(source, secType, market)) {
                    return new USDRIPUserOrderAggregate();
                }
            default:
                return null;
        }
    }

    public UserOrder buildUserOrder(PlaceOrderRequest placeOrderRequest, AccountDTO executionMasterDTO) {
        this.userOrder = UserOrder.builder()
                .externalId(placeOrderRequest.getExternalId())
                .account(placeOrderRequest.getAccount())
                .portfolioId(placeOrderRequest.getPortfolioId())
                .dividendId(placeOrderRequest.getDividendId())
                .symbol(placeOrderRequest.getSymbol())
                .segType(placeOrderRequest.getSegType())
                .secType(placeOrderRequest.getSecType())
                .market(placeOrderRequest.getMarket())
                .currency(placeOrderRequest.getCurrency())
                .action(placeOrderRequest.getAction())
                .amount(placeOrderRequest.getAmount())
                .quantity(placeOrderRequest.getQuantity())
                .tradeTime(DateTime.now())
                .status(UserOrderStatus.PENDING.name())
                .orderType(placeOrderRequest.getOrderType())
                .tradeAmount(calTradeAmount(placeOrderRequest.getAmount()))
                .executionMaster(executionMasterDTO.getExecutionMaster())
                .dealAccount(executionMasterDTO.getDealAccountId())
                .client(ServerUtil.getClient())
                .source(placeOrderRequest.getSource())
                .applyMargin(placeOrderRequest.getApplyMargin())
                .build();

        return userOrder;
    }

    protected abstract Double calTradeAmount(Double amount);

    public abstract SecType secType();

    public UserOrder updateAfterPreTrade(PreTradeFacadeDTO preTradeFacadeDTO) {
        if (preTradeFacadeDTO == null || BooleanUtils.isNotTrue(preTradeFacadeDTO.getIsPass())) {
            errorCode = ErrorCode.PRE_TRADE_NOT_PASS;
            String errorMsg = preTradeFacadeDTO != null ? preTradeFacadeDTO.getErrorMsg() : null;
            return UserOrder.builder()
                    .id(userOrder.getId())
                    .status(UserOrderStatus.REJECTED.name())
                    .errorCode(errorCode.name())
                    .errorMsg(errorMsg)
                    .build();
        }

        return userOrder;
    }

    public UserOrder updateAfterLockAsset(LockAssetDTO lockAssetDTO) {
        if (lockAssetDTO.getSucc()) {
            return UserOrder.builder().id(userOrder.getId()).status(UserOrderStatus.NEW.name()).build();
        } else {
            errorCode = ErrorCode.LOCK_ASSET_FAIL;
            return UserOrder.builder().id(userOrder.getId()).status(UserOrderStatus.REJECTED.name())
                            .errorMsg(errorCode.name()).errorCode(errorCode.getMsg()).build();
        }
    }

    public LockAssetContext toLockAssetContext(ContractInfoDTO contractInfoDTO, PlaceOrderRequest placeOrderRequest) {
        return LockAssetContext.builder().userOrderId(userOrder.getId())
                .account(userOrder.getAccount()).portfolioId(placeOrderRequest.getPortfolioId())
                .amount(userOrder.getAmount()).action(userOrder.getAction()).quantity(userOrder.getQuantity())
                .currency(userOrder.getCurrency()).market(userOrder.getMarket()).contractId(contractInfoDTO.getContractId())
                .segType(placeOrderRequest.getSegType())
                .secType(placeOrderRequest.getSecType())
                .source(placeOrderRequest.getSource())
                .lockStep(LockStep.PLACE_ORDER.name())
                .build();
    }

    public CalculateLockAssetContext toCalculateLockAssetContext(PlaceOrderRequest placeOrderRequest) {
        throw new UnsupportedOperationException();
    }

}
