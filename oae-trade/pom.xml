<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oae-platform</artifactId>
        <groupId>com.tigerbrokers.oae.platform</groupId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oae-trade</artifactId>

    <dependencies>
        <!--project-->
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.alpha</groupId>
            <artifactId>core-data</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven.deploy.plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>