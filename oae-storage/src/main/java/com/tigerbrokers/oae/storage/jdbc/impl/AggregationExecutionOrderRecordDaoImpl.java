package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderRecordDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.AggregationExecutionOrderRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Repository
public class AggregationExecutionOrderRecordDaoImpl implements AggregationExecutionOrderRecordDao {

    @Autowired
    private AggregationExecutionOrderRecordMapper aggregationExecutionOrderRecordMapper;

    @Override
    public void insert(AggregationExecutionOrderRecord aggregationExecutionOrderRecord) {
        aggregationExecutionOrderRecordMapper.insertSelective(aggregationExecutionOrderRecord);
    }

    @Override
    public void update(AggregationExecutionOrderRecord aggregationExecutionOrderRecord) {
        aggregationExecutionOrderRecordMapper.updateByPrimaryKeySelective(aggregationExecutionOrderRecord);
    }

    @Override
    public List<AggregationExecutionOrderRecord> selectByExecutionId(Long executionId) {
        AggregationExecutionOrderRecordExample example = new AggregationExecutionOrderRecordExample();
        example.createCriteria().andAggregationExecutionIdEqualTo(executionId);

        return aggregationExecutionOrderRecordMapper.selectByExample(example);
    }
}
