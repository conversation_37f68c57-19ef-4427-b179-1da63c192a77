package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LockedAsset {
    private Long id;

    private Long userOrderId;

    private String account;

    private Long portfolioId;

    private String market;

    private String currency;

    private Double amount;

    private Double quantity;

    private Date tradeTime;

    private String segType;

    private String secType;

    private String type;

    private String status;

    private String lockStep;

    private Long contractId;

    private String source;

    private Date createdAt;

    private Date updatedAt;

    private String detail;
}