package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.AggregationExecutionOrderMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Repository
public class AggregationExecutionOrderDaoImpl implements AggregationExecutionOrderDao {

    @Autowired
    private AggregationExecutionOrderMapper aggregationExecutionOrderMapper;

    @Override
    public Long insert(AggregationExecutionOrder aggregationExecutionOrder) {
        aggregationExecutionOrderMapper.insertSelective(aggregationExecutionOrder);
        return aggregationExecutionOrder.getId();
    }

    @Override
    public void update(AggregationExecutionOrder aggregationExecutionOrder) {
        aggregationExecutionOrderMapper.updateByPrimaryKeySelective(aggregationExecutionOrder);
    }

    @Override
    public void updateByIdAndTradeId(AggregationExecutionOrder aggregationExecutionOrder) {
        AggregationExecutionOrderExample example = new AggregationExecutionOrderExample();
        example.createCriteria().andIdEqualTo(aggregationExecutionOrder.getId())
                .andTradeIdEqualTo(aggregationExecutionOrder.getTradeId());
        aggregationExecutionOrderMapper.updateByExampleSelective(aggregationExecutionOrder, example);
    }

    @Override
    public AggregationExecutionOrder select(Long id) {
        return aggregationExecutionOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public AggregationExecutionOrder selectByTradeId(String tradeId) {
        AggregationExecutionOrderExample example = new AggregationExecutionOrderExample();
        example.createCriteria().andTradeIdEqualTo(String.valueOf(tradeId));
        List<AggregationExecutionOrder> list = aggregationExecutionOrderMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }

        return null;
    }

    @Override
    public List<AggregationExecutionOrder> selectByAggregationId(Long aggregationId) {
        AggregationExecutionOrderExample example = new AggregationExecutionOrderExample();
        example.createCriteria().andAggregationIdEqualTo(aggregationId);
        return aggregationExecutionOrderMapper.selectByExample(example);
    }

    @Override
    public List<AggregationExecutionOrder> selectByAggregationIds(List<Long> aggregationIds) {
        AggregationExecutionOrderExample example = new AggregationExecutionOrderExample();
        example.createCriteria().andAggregationIdIn(aggregationIds);
        return aggregationExecutionOrderMapper.selectByExample(example);
    }

    @Override
    public List<AggregationExecutionOrder> selectByStatus(BusinessSource source, CommonConsts.StockMarket market, SecType secType, List<String> status) {
        AggregationExecutionOrderExample example = new AggregationExecutionOrderExample();
        AggregationExecutionOrderExample.Criteria criteria = example.createCriteria();
        if (source != null) {
            criteria.andSourceEqualTo(source.name());
        }
        if (market != null) {
            criteria.andMarketEqualTo(market.name());
        }
        if (secType != null) {
            criteria.andSecTypeEqualTo(secType.name());
        }
        if (CollectionUtils.isNotEmpty(status)) {
            criteria.andTradeStatusIn(status);
        }
        return aggregationExecutionOrderMapper.selectByExample(example);
    }
}
