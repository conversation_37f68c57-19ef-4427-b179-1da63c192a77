package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface UserOrderMapper {
    long countByExample(UserOrderExample example);

    int deleteByExample(UserOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(UserOrder record);

    int insertSelective(UserOrder record);

    List<UserOrder> selectByExampleWithBLOBsWithRowbounds(UserOrderExample example, RowBounds rowBounds);

    List<UserOrder> selectByExampleWithBLOBs(UserOrderExample example);

    List<UserOrder> selectByExampleWithRowbounds(UserOrderExample example, RowBounds rowBounds);

    List<UserOrder> selectByExample(UserOrderExample example);

    UserOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") UserOrder record, @Param("example") UserOrderExample example);

    int updateByExampleWithBLOBs(@Param("record") UserOrder record, @Param("example") UserOrderExample example);

    int updateByExample(@Param("record") UserOrder record, @Param("example") UserOrderExample example);

    int updateByPrimaryKeySelective(UserOrder record);

    int updateByPrimaryKeyWithBLOBs(UserOrder record);

    int updateByPrimaryKey(UserOrder record);

    int insertOrUpdate(UserOrder record);

    int insertOrUpdateSelective(UserOrder record);

    int batchUpdate(List<UserOrder> list);

    int batchInsert(List<UserOrder> list);
}