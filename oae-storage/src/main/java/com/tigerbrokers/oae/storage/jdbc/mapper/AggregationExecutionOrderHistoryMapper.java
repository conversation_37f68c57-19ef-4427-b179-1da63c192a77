package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderHistory;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AggregationExecutionOrderHistoryMapper {
    long countByExample(AggregationExecutionOrderHistoryExample example);

    int deleteByExample(AggregationExecutionOrderHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AggregationExecutionOrderHistory record);

    int insertSelective(AggregationExecutionOrderHistory record);

    List<AggregationExecutionOrderHistory> selectByExampleWithRowbounds(AggregationExecutionOrderHistoryExample example, RowBounds rowBounds);

    List<AggregationExecutionOrderHistory> selectByExample(AggregationExecutionOrderHistoryExample example);

    AggregationExecutionOrderHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AggregationExecutionOrderHistory record, @Param("example") AggregationExecutionOrderHistoryExample example);

    int updateByExample(@Param("record") AggregationExecutionOrderHistory record, @Param("example") AggregationExecutionOrderHistoryExample example);

    int updateByPrimaryKeySelective(AggregationExecutionOrderHistory record);

    int updateByPrimaryKey(AggregationExecutionOrderHistory record);

    int insertOrUpdate(AggregationExecutionOrderHistory record);

    int insertOrUpdateSelective(AggregationExecutionOrderHistory record);

    int batchUpdate(List<AggregationExecutionOrderHistory> list);

    int batchInsert(List<AggregationExecutionOrderHistory> list);
}