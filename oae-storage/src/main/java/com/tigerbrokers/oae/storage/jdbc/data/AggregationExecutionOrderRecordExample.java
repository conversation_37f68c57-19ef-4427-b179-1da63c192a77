package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AggregationExecutionOrderRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Boolean forUpdate = false;

    public AggregationExecutionOrderRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setForUpdate(Boolean forUpdate) {
        this.forUpdate = forUpdate;
    }

    public Boolean isForUpdate() {
        return forUpdate;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdIsNull() {
            addCriterion("aggregation_execution_id is null");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdIsNotNull() {
            addCriterion("aggregation_execution_id is not null");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdEqualTo(Long value) {
            addCriterion("aggregation_execution_id =", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdNotEqualTo(Long value) {
            addCriterion("aggregation_execution_id <>", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdGreaterThan(Long value) {
            addCriterion("aggregation_execution_id >", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("aggregation_execution_id >=", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdLessThan(Long value) {
            addCriterion("aggregation_execution_id <", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdLessThanOrEqualTo(Long value) {
            addCriterion("aggregation_execution_id <=", value, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdIn(List<Long> values) {
            addCriterion("aggregation_execution_id in", values, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdNotIn(List<Long> values) {
            addCriterion("aggregation_execution_id not in", values, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdBetween(Long value1, Long value2) {
            addCriterion("aggregation_execution_id between", value1, value2, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationExecutionIdNotBetween(Long value1, Long value2) {
            addCriterion("aggregation_execution_id not between", value1, value2, "aggregationExecutionId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdIsNull() {
            addCriterion("aggregation_id is null");
            return (Criteria) this;
        }

        public Criteria andAggregationIdIsNotNull() {
            addCriterion("aggregation_id is not null");
            return (Criteria) this;
        }

        public Criteria andAggregationIdEqualTo(Long value) {
            addCriterion("aggregation_id =", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdNotEqualTo(Long value) {
            addCriterion("aggregation_id <>", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdGreaterThan(Long value) {
            addCriterion("aggregation_id >", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdGreaterThanOrEqualTo(Long value) {
            addCriterion("aggregation_id >=", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdLessThan(Long value) {
            addCriterion("aggregation_id <", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdLessThanOrEqualTo(Long value) {
            addCriterion("aggregation_id <=", value, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdIn(List<Long> values) {
            addCriterion("aggregation_id in", values, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdNotIn(List<Long> values) {
            addCriterion("aggregation_id not in", values, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdBetween(Long value1, Long value2) {
            addCriterion("aggregation_id between", value1, value2, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andAggregationIdNotBetween(Long value1, Long value2) {
            addCriterion("aggregation_id not between", value1, value2, "aggregationId");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNull() {
            addCriterion("symbol is null");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNotNull() {
            addCriterion("symbol is not null");
            return (Criteria) this;
        }

        public Criteria andSymbolEqualTo(String value) {
            addCriterion("symbol =", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotEqualTo(String value) {
            addCriterion("symbol <>", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThan(String value) {
            addCriterion("symbol >", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThanOrEqualTo(String value) {
            addCriterion("symbol >=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThan(String value) {
            addCriterion("symbol <", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThanOrEqualTo(String value) {
            addCriterion("symbol <=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLike(String value) {
            addCriterion("symbol like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotLike(String value) {
            addCriterion("symbol not like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolIn(List<String> values) {
            addCriterion("symbol in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotIn(List<String> values) {
            addCriterion("symbol not in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolBetween(String value1, String value2) {
            addCriterion("symbol between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotBetween(String value1, String value2) {
            addCriterion("symbol not between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andMarketIsNull() {
            addCriterion("market is null");
            return (Criteria) this;
        }

        public Criteria andMarketIsNotNull() {
            addCriterion("market is not null");
            return (Criteria) this;
        }

        public Criteria andMarketEqualTo(String value) {
            addCriterion("market =", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotEqualTo(String value) {
            addCriterion("market <>", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThan(String value) {
            addCriterion("market >", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThanOrEqualTo(String value) {
            addCriterion("market >=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThan(String value) {
            addCriterion("market <", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThanOrEqualTo(String value) {
            addCriterion("market <=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLike(String value) {
            addCriterion("market like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotLike(String value) {
            addCriterion("market not like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketIn(List<String> values) {
            addCriterion("market in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotIn(List<String> values) {
            addCriterion("market not in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketBetween(String value1, String value2) {
            addCriterion("market between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotBetween(String value1, String value2) {
            addCriterion("market not between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andActionIsNull() {
            addCriterion("`action` is null");
            return (Criteria) this;
        }

        public Criteria andActionIsNotNull() {
            addCriterion("`action` is not null");
            return (Criteria) this;
        }

        public Criteria andActionEqualTo(String value) {
            addCriterion("`action` =", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotEqualTo(String value) {
            addCriterion("`action` <>", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThan(String value) {
            addCriterion("`action` >", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThanOrEqualTo(String value) {
            addCriterion("`action` >=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThan(String value) {
            addCriterion("`action` <", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThanOrEqualTo(String value) {
            addCriterion("`action` <=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLike(String value) {
            addCriterion("`action` like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotLike(String value) {
            addCriterion("`action` not like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionIn(List<String> values) {
            addCriterion("`action` in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotIn(List<String> values) {
            addCriterion("`action` not in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionBetween(String value1, String value2) {
            addCriterion("`action` between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotBetween(String value1, String value2) {
            addCriterion("`action` not between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andOperationIsNull() {
            addCriterion("`operation` is null");
            return (Criteria) this;
        }

        public Criteria andOperationIsNotNull() {
            addCriterion("`operation` is not null");
            return (Criteria) this;
        }

        public Criteria andOperationEqualTo(String value) {
            addCriterion("`operation` =", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationNotEqualTo(String value) {
            addCriterion("`operation` <>", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationGreaterThan(String value) {
            addCriterion("`operation` >", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationGreaterThanOrEqualTo(String value) {
            addCriterion("`operation` >=", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationLessThan(String value) {
            addCriterion("`operation` <", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationLessThanOrEqualTo(String value) {
            addCriterion("`operation` <=", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationLike(String value) {
            addCriterion("`operation` like", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationNotLike(String value) {
            addCriterion("`operation` not like", value, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationIn(List<String> values) {
            addCriterion("`operation` in", values, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationNotIn(List<String> values) {
            addCriterion("`operation` not in", values, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationBetween(String value1, String value2) {
            addCriterion("`operation` between", value1, value2, "operation");
            return (Criteria) this;
        }

        public Criteria andOperationNotBetween(String value1, String value2) {
            addCriterion("`operation` not between", value1, value2, "operation");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(Double value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(Double value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(Double value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(Double value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(Double value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<Double> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<Double> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(Double value1, Double value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(Double value1, Double value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andRefPriceIsNull() {
            addCriterion("ref_price is null");
            return (Criteria) this;
        }

        public Criteria andRefPriceIsNotNull() {
            addCriterion("ref_price is not null");
            return (Criteria) this;
        }

        public Criteria andRefPriceEqualTo(Double value) {
            addCriterion("ref_price =", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceNotEqualTo(Double value) {
            addCriterion("ref_price <>", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceGreaterThan(Double value) {
            addCriterion("ref_price >", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("ref_price >=", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceLessThan(Double value) {
            addCriterion("ref_price <", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceLessThanOrEqualTo(Double value) {
            addCriterion("ref_price <=", value, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceIn(List<Double> values) {
            addCriterion("ref_price in", values, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceNotIn(List<Double> values) {
            addCriterion("ref_price not in", values, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceBetween(Double value1, Double value2) {
            addCriterion("ref_price between", value1, value2, "refPrice");
            return (Criteria) this;
        }

        public Criteria andRefPriceNotBetween(Double value1, Double value2) {
            addCriterion("ref_price not between", value1, value2, "refPrice");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Double value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Double value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Double value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Double value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Double value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Double> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Double> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Double value1, Double value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Double value1, Double value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitIsNull() {
            addCriterion("lot_size_unit is null");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitIsNotNull() {
            addCriterion("lot_size_unit is not null");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitEqualTo(Integer value) {
            addCriterion("lot_size_unit =", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitNotEqualTo(Integer value) {
            addCriterion("lot_size_unit <>", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitGreaterThan(Integer value) {
            addCriterion("lot_size_unit >", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("lot_size_unit >=", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitLessThan(Integer value) {
            addCriterion("lot_size_unit <", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitLessThanOrEqualTo(Integer value) {
            addCriterion("lot_size_unit <=", value, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitIn(List<Integer> values) {
            addCriterion("lot_size_unit in", values, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitNotIn(List<Integer> values) {
            addCriterion("lot_size_unit not in", values, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitBetween(Integer value1, Integer value2) {
            addCriterion("lot_size_unit between", value1, value2, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andLotSizeUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("lot_size_unit not between", value1, value2, "lotSizeUnit");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityIsNull() {
            addCriterion("pre_filled_quantity is null");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityIsNotNull() {
            addCriterion("pre_filled_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityEqualTo(Double value) {
            addCriterion("pre_filled_quantity =", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityNotEqualTo(Double value) {
            addCriterion("pre_filled_quantity <>", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityGreaterThan(Double value) {
            addCriterion("pre_filled_quantity >", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("pre_filled_quantity >=", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityLessThan(Double value) {
            addCriterion("pre_filled_quantity <", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityLessThanOrEqualTo(Double value) {
            addCriterion("pre_filled_quantity <=", value, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityIn(List<Double> values) {
            addCriterion("pre_filled_quantity in", values, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityNotIn(List<Double> values) {
            addCriterion("pre_filled_quantity not in", values, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityBetween(Double value1, Double value2) {
            addCriterion("pre_filled_quantity between", value1, value2, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreFilledQuantityNotBetween(Double value1, Double value2) {
            addCriterion("pre_filled_quantity not between", value1, value2, "preFilledQuantity");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceIsNull() {
            addCriterion("pre_avg_price is null");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceIsNotNull() {
            addCriterion("pre_avg_price is not null");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceEqualTo(Double value) {
            addCriterion("pre_avg_price =", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceNotEqualTo(Double value) {
            addCriterion("pre_avg_price <>", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceGreaterThan(Double value) {
            addCriterion("pre_avg_price >", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("pre_avg_price >=", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceLessThan(Double value) {
            addCriterion("pre_avg_price <", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceLessThanOrEqualTo(Double value) {
            addCriterion("pre_avg_price <=", value, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceIn(List<Double> values) {
            addCriterion("pre_avg_price in", values, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceNotIn(List<Double> values) {
            addCriterion("pre_avg_price not in", values, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceBetween(Double value1, Double value2) {
            addCriterion("pre_avg_price between", value1, value2, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andPreAvgPriceNotBetween(Double value1, Double value2) {
            addCriterion("pre_avg_price not between", value1, value2, "preAvgPrice");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}