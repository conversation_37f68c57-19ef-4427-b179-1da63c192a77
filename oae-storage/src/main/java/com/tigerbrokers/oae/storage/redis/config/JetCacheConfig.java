package com.tigerbrokers.oae.storage.redis.config;

import com.alicp.jetcache.anno.CacheConsts;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.alicp.jetcache.anno.support.SpringConfigProvider;
import com.alicp.jetcache.embedded.EmbeddedCacheBuilder;
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder;
import com.alicp.jetcache.redis.RedisCacheBuilder;
import com.alicp.jetcache.support.FastjsonKeyConvertor;
import com.alicp.jetcache.support.JavaValueDecoder;
import com.alicp.jetcache.support.JavaValueEncoder;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.tigerbrokers.oae.storage.DBConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Protocol;
import redis.clients.jedis.util.Pool;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/3
 */
@Configuration
@EnableMethodCache(basePackages = "com.tigerbrokers.oae")
public class JetCacheConfig {

    private DBConfig.RedisConfig redisConfig;

    @Autowired
    public JetCacheConfig(@Qualifier("redisConfig") DBConfig.RedisConfig redisConfig) {
        this.redisConfig = redisConfig;
    }

    @Bean
    public Pool<Jedis> pool() {
        GenericObjectPoolConfig pc = new GenericObjectPoolConfig();
        pc.setMinIdle(2);
        pc.setMaxIdle(10);
        pc.setMaxTotal(10);
        if (redisConfig.getSentinelEnable()) {
            Set<String> sentinels = Sets.newHashSet(StringUtils.split(redisConfig.getSentinels(), ";"));
            return new JedisSentinelPool(redisConfig.getMasterName(), sentinels, pc,
                    Protocol.DEFAULT_TIMEOUT, redisConfig.getPassword(), redisConfig.getDbIndex());
        } else {
            return new JedisPool(pc, redisConfig.getHost(), redisConfig.getPort(),
                    Protocol.DEFAULT_TIMEOUT, redisConfig.getPassword(), redisConfig.getDbIndex());
        }
    }

    @Bean
    public GlobalCacheConfig config(Pool<Jedis> pool) {
        Map localBuilders = Maps.newHashMap();
        EmbeddedCacheBuilder localBuilder = LinkedHashMapCacheBuilder
                .createLinkedHashMapCacheBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE);
        localBuilders.put(CacheConsts.DEFAULT_AREA, localBuilder);

        Map remoteBuilders = Maps.newHashMap();
        RedisCacheBuilder remoteCacheBuilder = RedisCacheBuilder.createRedisCacheBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE)
                .valueEncoder(JavaValueEncoder.INSTANCE)
                .valueDecoder(JavaValueDecoder.INSTANCE)
                .jedisPool(pool);
        remoteBuilders.put(CacheConsts.DEFAULT_AREA, remoteCacheBuilder);

        GlobalCacheConfig globalCacheConfig = new GlobalCacheConfig();
        globalCacheConfig.setLocalCacheBuilders(localBuilders);
        globalCacheConfig.setRemoteCacheBuilders(remoteBuilders);
        globalCacheConfig.setStatIntervalMinutes(15);
        globalCacheConfig.setAreaInCacheName(false);

        return globalCacheConfig;
    }
}
