package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.stock.common.CommonConsts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public interface UserOrderDao {

    void insert(UserOrder userOrder);

    void update(UserOrder userOrder);

    void batchUpdate(List<UserOrder> userOrders);

    UserOrder select(Long id);

    List<UserOrder> selectByStatus(String status, BusinessSource source, CommonConsts.StockMarket market, SecType secType);

    List<UserOrder> selectByAggregationId(Long aggregationId);

    List<UserOrder> selectByIds(List<Long> userOrderIds);
}
