package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.LockedAssetStatus;
import com.tigerbrokers.oae.storage.jdbc.LockedAssetDao;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.LockedAssetMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Repository
public class LockedAssetDaoImpl implements LockedAssetDao {

    @Autowired
    private LockedAssetMapper lockedAssetMapper;

    @Override
    public void insert(LockedAsset lockedAsset) {
        lockedAssetMapper.insertSelective(lockedAsset);
    }

    @Override
    public void insertOrUpdate(LockedAsset lockedAsset) {
        lockedAssetMapper.insertOrUpdateSelective(lockedAsset);
    }

    @Override
    public void update(LockedAsset lockedAsset) {
        lockedAssetMapper.updateByPrimaryKeySelective(lockedAsset);
    }

    @Override
    public void batchUpdate(List<LockedAsset> lockedAssetList) {
        lockedAssetMapper.batchUpdate(lockedAssetList);
    }

    @Override
    public List<LockedAsset> selectByPrimaryIds(List<Long> ids) {
        LockedAssetExample example = new LockedAssetExample();
        example.createCriteria().andIdIn(ids);
        return lockedAssetMapper.selectByExample(example);
    }

    @Override
    public List<LockedAsset> selectByUserOrderIds(List<Long> userOrderIds) {
        LockedAssetExample example = new LockedAssetExample();
        example.createCriteria().andUserOrderIdIn(userOrderIds);
        return lockedAssetMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<LockedAsset> selectByUserOrderIdsAndStep(List<Long> userOrderIds, LockStep step) {
        LockedAssetExample example = new LockedAssetExample();
        example.createCriteria().andUserOrderIdIn(userOrderIds).andLockStepEqualTo(step.name());
        return lockedAssetMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public List<LockedAsset> selectByStatus(String status, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        LockedAssetExample example = new LockedAssetExample();
        LockedAssetExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(status);
        if (source != null) {
            criteria.andSourceEqualTo(source.name());
        }
        if (market != null) {
            criteria.andMarketEqualTo(market.name());
        }
        if (secType != null) {
            criteria.andSecTypeEqualTo(secType.name());
        }
        return lockedAssetMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public LockedAsset getByUserOrderId(Long userOrderId, LockedAssetStatus status, LockStep lockStep) {
        LockedAssetExample example = new LockedAssetExample();
        LockedAssetExample.Criteria criteria = example.createCriteria();
        criteria.andUserOrderIdEqualTo(userOrderId).andStatusEqualTo(status.name()).andLockStepEqualTo(lockStep.name());
        return lockedAssetMapper.selectByExampleWithBLOBs(example).stream().findAny().orElse(null);
    }
}
