package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AggregationExecutionOrderRecordMapper {
    long countByExample(AggregationExecutionOrderRecordExample example);

    int deleteByExample(AggregationExecutionOrderRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AggregationExecutionOrderRecord record);

    int insertSelective(AggregationExecutionOrderRecord record);

    List<AggregationExecutionOrderRecord> selectByExampleWithRowbounds(AggregationExecutionOrderRecordExample example, RowBounds rowBounds);

    List<AggregationExecutionOrderRecord> selectByExample(AggregationExecutionOrderRecordExample example);

    AggregationExecutionOrderRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AggregationExecutionOrderRecord record, @Param("example") AggregationExecutionOrderRecordExample example);

    int updateByExample(@Param("record") AggregationExecutionOrderRecord record, @Param("example") AggregationExecutionOrderRecordExample example);

    int updateByPrimaryKeySelective(AggregationExecutionOrderRecord record);

    int updateByPrimaryKey(AggregationExecutionOrderRecord record);

    int insertOrUpdate(AggregationExecutionOrderRecord record);

    int insertOrUpdateSelective(AggregationExecutionOrderRecord record);

    int batchUpdate(List<AggregationExecutionOrderRecord> list);

    int batchInsert(List<AggregationExecutionOrderRecord> list);
}