package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.stock.common.CommonConsts;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public interface TradeAllocationDao {

    void batchInsert(List<TradeAllocation> tradeAllocations);

    void update(TradeAllocation tradeAllocation);

    void batchUpdate(List<TradeAllocation> tradeAllocations);

    List<TradeAllocation> select(List<Long> ids);

    List<TradeAllocation> selectByStatus(String status, BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType);

    List<TradeAllocation> selectByBusinessDate(String market, String source, String secType, Date start, Date end);

    List<TradeAllocation> selectAllocationsByAggregationId(Long aggregationId, String market, String source);
}
