package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AggregationExecutionOrderMapper {
    long countByExample(AggregationExecutionOrderExample example);

    int deleteByExample(AggregationExecutionOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AggregationExecutionOrder record);

    int insertSelective(AggregationExecutionOrder record);

    List<AggregationExecutionOrder> selectByExampleWithRowbounds(AggregationExecutionOrderExample example, RowBounds rowBounds);

    List<AggregationExecutionOrder> selectByExample(AggregationExecutionOrderExample example);

    AggregationExecutionOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AggregationExecutionOrder record, @Param("example") AggregationExecutionOrderExample example);

    int updateByExample(@Param("record") AggregationExecutionOrder record, @Param("example") AggregationExecutionOrderExample example);

    int updateByPrimaryKeySelective(AggregationExecutionOrder record);

    int updateByPrimaryKey(AggregationExecutionOrder record);

    int insertOrUpdate(AggregationExecutionOrder record);

    int insertOrUpdateSelective(AggregationExecutionOrder record);

    int batchUpdate(List<AggregationExecutionOrder> list);

    int batchInsert(List<AggregationExecutionOrder> list);
}