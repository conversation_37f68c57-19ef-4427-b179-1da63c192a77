package com.tigerbrokers.oae.storage.kafka.data;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Data
@Builder
public class UserOrderEvent {

    private Long id;

    private String externalId;

    private String tradeId;

    private String account;

    private Long portfolioId;

    private String symbol;

    private String segType;

    private String secType;

    private String market;

    private String currency;

    private String action;

    private Double amount;

    private Double quantity;

    private Double filledQuantity;

    private Double filledAmount;

    private Double avgPrice;

    private Date tradeTime;

    private String type;

    private String status;

    private String source;

    private String errorCode;

    private String errorMsg;

    /**
     * Prime原始kafka消息
     */
    private String tradeOriMsg;
}
