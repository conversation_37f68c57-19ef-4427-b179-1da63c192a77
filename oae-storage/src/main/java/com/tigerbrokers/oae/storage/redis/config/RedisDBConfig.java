package com.tigerbrokers.oae.storage.redis.config;

import com.tigerbrokers.oae.storage.DBConfig;
import com.tigerbrokers.tools.storage.redis.RedisConfig;
import com.tigerbrokers.tools.storage.redis.RedisUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Configuration
public class RedisDBConfig {

    @Primary
    @Bean(name = "jedisConnectionFactory")
    @Qualifier(value = "jedisConnectionFactory")
    public JedisConnectionFactory jedisConnectionFactory(@Qualifier("redisConfig") DBConfig.RedisConfig redisConfig) {
        RedisConfig config = RedisConfig.builder()
                .sentinelEnable(redisConfig.getSentinelEnable())
                .sentinels(redisConfig.getSentinels())
                .masterName(redisConfig.getMasterName())
                .password(redisConfig.getPassword())
                .dbIndex(redisConfig.getDbIndex())
                .build();
        return RedisUtils.getConnectionFactory(config);
    }

    @Primary
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(@Qualifier("jedisConnectionFactory") JedisConnectionFactory jedisConnectionFactory) {
        return RedisUtils.getRedisTemplate(jedisConnectionFactory);
    }
}
