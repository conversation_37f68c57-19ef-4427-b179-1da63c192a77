package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.UserOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.UserOrderMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Repository
public class UserOrderImpl implements UserOrderDao {

    @Autowired
    private UserOrderMapper userOrderMapper;

    @Override
    public void insert(UserOrder userOrder) {
        userOrderMapper.insertSelective(userOrder);
    }

    @Override
    public void update(UserOrder userOrder) {
        userOrderMapper.updateByPrimaryKeySelective(userOrder);
    }

    @Override
    public void batchUpdate(List<UserOrder> userOrders) {
        userOrderMapper.batchUpdate(userOrders);
    }

    @Override
    public UserOrder select(Long id) {
        return userOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<UserOrder> selectByStatus(String status, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        UserOrderExample example = new UserOrderExample();
        UserOrderExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(status);
        if (source != null) {
            criteria.andSourceEqualTo(source.name());
        }
        if (market != null) {
            criteria.andMarketEqualTo(market.name());
        }
        if (secType != null) {
            criteria.andSecTypeEqualTo(secType.name());
        }
        return userOrderMapper.selectByExample(example);
    }

    @Override
    public List<UserOrder> selectByAggregationId(Long aggregationId) {
        UserOrderExample example = new UserOrderExample();
        example.createCriteria().andAggregationIdEqualTo(aggregationId);
        return userOrderMapper.selectByExample(example);
    }

    @Override
    public List<UserOrder> selectByIds(List<Long> userOrderIds) {
        UserOrderExample example = new UserOrderExample();
        example.createCriteria().andIdIn(userOrderIds);
        return userOrderMapper.selectByExample(example);
    }
}
