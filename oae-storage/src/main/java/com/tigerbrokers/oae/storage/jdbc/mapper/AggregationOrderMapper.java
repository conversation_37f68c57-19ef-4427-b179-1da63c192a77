package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AggregationOrderMapper {
    long countByExample(AggregationOrderExample example);

    int deleteByExample(AggregationOrderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AggregationOrder record);

    int insertSelective(AggregationOrder record);

    List<AggregationOrder> selectByExampleWithRowbounds(AggregationOrderExample example, RowBounds rowBounds);

    List<AggregationOrder> selectByExample(AggregationOrderExample example);

    AggregationOrder selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AggregationOrder record, @Param("example") AggregationOrderExample example);

    int updateByExample(@Param("record") AggregationOrder record, @Param("example") AggregationOrderExample example);

    int updateByPrimaryKeySelective(AggregationOrder record);

    int updateByPrimaryKey(AggregationOrder record);

    int insertOrUpdate(AggregationOrder record);

    int insertOrUpdateSelective(AggregationOrder record);

    int batchUpdate(List<AggregationOrder> list);

    int batchInsert(List<AggregationOrder> list);
}