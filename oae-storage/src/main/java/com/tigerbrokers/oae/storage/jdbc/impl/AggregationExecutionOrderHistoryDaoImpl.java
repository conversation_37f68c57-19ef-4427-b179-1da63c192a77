package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.oae.storage.jdbc.AggregationExecutionOrderHistoryDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderHistory;
import com.tigerbrokers.oae.storage.jdbc.mapper.AggregationExecutionOrderHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Repository
public class AggregationExecutionOrderHistoryDaoImpl implements AggregationExecutionOrderHistoryDao {

    @Autowired
    private AggregationExecutionOrderHistoryMapper aggregationExecutionOrderHistoryMapper;

    @Override
    public void insert(AggregationExecutionOrderHistory aggregationExecutionOrderHistory) {
        aggregationExecutionOrderHistoryMapper.insertSelective(aggregationExecutionOrderHistory);
    }
}
