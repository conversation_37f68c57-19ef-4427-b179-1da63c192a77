package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.AggregationOrderDao;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.AggregationOrderMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
@Repository
public class AggregationOrderDaoImpl implements AggregationOrderDao {

    @Autowired
    private AggregationOrderMapper aggregationOrderMapper;

    @Override
    public Long insert(AggregationOrder aggregationOrder) {
         aggregationOrderMapper.insertSelective(aggregationOrder);

         return aggregationOrder.getId();
    }

    @Override
    public void update(AggregationOrder aggregationOrder) {
        aggregationOrderMapper.updateByPrimaryKeySelective(aggregationOrder);
    }

    @Override
    public List<AggregationOrder> selectByStatus(List<String> statusList, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        AggregationOrderExample example = new AggregationOrderExample();
        AggregationOrderExample.Criteria criteria = example.createCriteria();
        criteria.andStatusIn(statusList);
        if (source != null) {
            criteria.andSourceEqualTo(source.name());
        }
        if (market != null) {
            criteria.andMarketEqualTo(market.name());
        }
        if (secType != null) {
            criteria.andSecTypeEqualTo(secType.name());
        }
        return aggregationOrderMapper.selectByExample(example);
    }

    @Override
    public List<AggregationOrder> selectByOrder(AggregationOrder aggOrder, List<String> statusList) {
        AggregationOrderExample example = new AggregationOrderExample();
        example.createCriteria().andSymbolEqualTo(aggOrder.getSymbol())
                .andOrderTypeEqualTo(aggOrder.getOrderType())
                .andDealAccountEqualTo(aggOrder.getDealAccount())
                .andMarketEqualTo(aggOrder.getMarket())
                .andStatusIn(statusList);

        return aggregationOrderMapper.selectByExample(example);
    }

    @Override
    public AggregationOrder selectById(Long id) {
        return aggregationOrderMapper.selectByPrimaryKey(id);
    }
}
