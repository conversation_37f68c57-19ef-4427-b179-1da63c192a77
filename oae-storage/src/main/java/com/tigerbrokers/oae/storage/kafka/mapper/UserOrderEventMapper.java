package com.tigerbrokers.oae.storage.kafka.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.UserOrder;
import com.tigerbrokers.oae.storage.kafka.data.UserOrderEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface UserOrderEventMapper {

    UserOrderEventMapper INSTANCE = Mappers.getMapper(UserOrderEventMapper.class);

    @Mappings({
            @Mapping(source = "tradeMsg", target = "tradeOriMsg")
    })
    UserOrderEvent from(UserOrder userOrder);
}
