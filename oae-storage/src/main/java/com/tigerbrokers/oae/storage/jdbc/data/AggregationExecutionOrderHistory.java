package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregationExecutionOrderHistory {
    private Long id;

    private Long aggregationExecutionId;

    private String tradeId;

    private Long aggregationId;

    private String dealAccount;

    private String executionMaster;

    private String symbol;

    private String market;

    private String currency;

    private String action;

    private Double totalQuantity;

    private Double filledQuantity;

    private Double price;

    private Double avgPrice;

    private String orderType;

    private String segType;

    private String secType;

    private Date tradeTime;

    private String status;

    private String tradeStatus;

    private String errorCode;

    private String errorMsg;

    private Date createdAt;

    private Date updatedAt;
}