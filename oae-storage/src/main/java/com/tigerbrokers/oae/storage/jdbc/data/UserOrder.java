package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOrder {
    private Long id;

    private String externalId;

    private String tradeId;

    private Long aggregationId;

    private String account;

    private Long portfolioId;

    private Long dividendId;

    private String dealAccount;

    private String executionMaster;

    private String symbol;

    private String segType;

    private String secType;

    private String market;

    private String currency;

    private String action;

    private Double amount;

    private Double quantity;

    private Double tradeAmount;

    private Double filledQuantity;

    private Double filledAmount;

    private Double avgPrice;

    private Date tradeTime;

    private Date filledTime;

    private String orderType;

    private String client;

    private String status;

    private String source;

    private Boolean applyMargin;

    private String errorCode;

    private String errorMsg;

    private Date createdAt;

    private Date updatedAt;

    private String tradeMsg;
}