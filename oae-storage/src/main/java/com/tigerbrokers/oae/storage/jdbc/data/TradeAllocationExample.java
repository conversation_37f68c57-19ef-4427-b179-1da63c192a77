package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TradeAllocationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Boolean forUpdate = false;

    public TradeAllocationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setForUpdate(Boolean forUpdate) {
        this.forUpdate = forUpdate;
    }

    public Boolean isForUpdate() {
        return forUpdate;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRefIdIsNull() {
            addCriterion("ref_id is null");
            return (Criteria) this;
        }

        public Criteria andRefIdIsNotNull() {
            addCriterion("ref_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefIdEqualTo(Long value) {
            addCriterion("ref_id =", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdNotEqualTo(Long value) {
            addCriterion("ref_id <>", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdGreaterThan(Long value) {
            addCriterion("ref_id >", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ref_id >=", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdLessThan(Long value) {
            addCriterion("ref_id <", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdLessThanOrEqualTo(Long value) {
            addCriterion("ref_id <=", value, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdIn(List<Long> values) {
            addCriterion("ref_id in", values, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdNotIn(List<Long> values) {
            addCriterion("ref_id not in", values, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdBetween(Long value1, Long value2) {
            addCriterion("ref_id between", value1, value2, "refId");
            return (Criteria) this;
        }

        public Criteria andRefIdNotBetween(Long value1, Long value2) {
            addCriterion("ref_id not between", value1, value2, "refId");
            return (Criteria) this;
        }

        public Criteria andSegTypeIsNull() {
            addCriterion("seg_type is null");
            return (Criteria) this;
        }

        public Criteria andSegTypeIsNotNull() {
            addCriterion("seg_type is not null");
            return (Criteria) this;
        }

        public Criteria andSegTypeEqualTo(String value) {
            addCriterion("seg_type =", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotEqualTo(String value) {
            addCriterion("seg_type <>", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeGreaterThan(String value) {
            addCriterion("seg_type >", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeGreaterThanOrEqualTo(String value) {
            addCriterion("seg_type >=", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLessThan(String value) {
            addCriterion("seg_type <", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLessThanOrEqualTo(String value) {
            addCriterion("seg_type <=", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLike(String value) {
            addCriterion("seg_type like", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotLike(String value) {
            addCriterion("seg_type not like", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeIn(List<String> values) {
            addCriterion("seg_type in", values, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotIn(List<String> values) {
            addCriterion("seg_type not in", values, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeBetween(String value1, String value2) {
            addCriterion("seg_type between", value1, value2, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotBetween(String value1, String value2) {
            addCriterion("seg_type not between", value1, value2, "segType");
            return (Criteria) this;
        }

        public Criteria andSecTypeIsNull() {
            addCriterion("sec_type is null");
            return (Criteria) this;
        }

        public Criteria andSecTypeIsNotNull() {
            addCriterion("sec_type is not null");
            return (Criteria) this;
        }

        public Criteria andSecTypeEqualTo(String value) {
            addCriterion("sec_type =", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotEqualTo(String value) {
            addCriterion("sec_type <>", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeGreaterThan(String value) {
            addCriterion("sec_type >", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sec_type >=", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLessThan(String value) {
            addCriterion("sec_type <", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLessThanOrEqualTo(String value) {
            addCriterion("sec_type <=", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLike(String value) {
            addCriterion("sec_type like", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotLike(String value) {
            addCriterion("sec_type not like", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeIn(List<String> values) {
            addCriterion("sec_type in", values, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotIn(List<String> values) {
            addCriterion("sec_type not in", values, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeBetween(String value1, String value2) {
            addCriterion("sec_type between", value1, value2, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotBetween(String value1, String value2) {
            addCriterion("sec_type not between", value1, value2, "secType");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNull() {
            addCriterion("contract_id is null");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNotNull() {
            addCriterion("contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdEqualTo(Long value) {
            addCriterion("contract_id =", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotEqualTo(Long value) {
            addCriterion("contract_id <>", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThan(Long value) {
            addCriterion("contract_id >", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThanOrEqualTo(Long value) {
            addCriterion("contract_id >=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThan(Long value) {
            addCriterion("contract_id <", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThanOrEqualTo(Long value) {
            addCriterion("contract_id <=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdIn(List<Long> values) {
            addCriterion("contract_id in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotIn(List<Long> values) {
            addCriterion("contract_id not in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdBetween(Long value1, Long value2) {
            addCriterion("contract_id between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotBetween(Long value1, Long value2) {
            addCriterion("contract_id not between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNull() {
            addCriterion("symbol is null");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNotNull() {
            addCriterion("symbol is not null");
            return (Criteria) this;
        }

        public Criteria andSymbolEqualTo(String value) {
            addCriterion("symbol =", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotEqualTo(String value) {
            addCriterion("symbol <>", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThan(String value) {
            addCriterion("symbol >", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThanOrEqualTo(String value) {
            addCriterion("symbol >=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThan(String value) {
            addCriterion("symbol <", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThanOrEqualTo(String value) {
            addCriterion("symbol <=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLike(String value) {
            addCriterion("symbol like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotLike(String value) {
            addCriterion("symbol not like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolIn(List<String> values) {
            addCriterion("symbol in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotIn(List<String> values) {
            addCriterion("symbol not in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolBetween(String value1, String value2) {
            addCriterion("symbol between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotBetween(String value1, String value2) {
            addCriterion("symbol not between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andMarketIsNull() {
            addCriterion("market is null");
            return (Criteria) this;
        }

        public Criteria andMarketIsNotNull() {
            addCriterion("market is not null");
            return (Criteria) this;
        }

        public Criteria andMarketEqualTo(String value) {
            addCriterion("market =", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotEqualTo(String value) {
            addCriterion("market <>", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThan(String value) {
            addCriterion("market >", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThanOrEqualTo(String value) {
            addCriterion("market >=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThan(String value) {
            addCriterion("market <", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThanOrEqualTo(String value) {
            addCriterion("market <=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLike(String value) {
            addCriterion("market like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotLike(String value) {
            addCriterion("market not like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketIn(List<String> values) {
            addCriterion("market in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotIn(List<String> values) {
            addCriterion("market not in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketBetween(String value1, String value2) {
            addCriterion("market between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotBetween(String value1, String value2) {
            addCriterion("market not between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("`type` like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("`type` not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andSourceAccountIsNull() {
            addCriterion("source_account is null");
            return (Criteria) this;
        }

        public Criteria andSourceAccountIsNotNull() {
            addCriterion("source_account is not null");
            return (Criteria) this;
        }

        public Criteria andSourceAccountEqualTo(String value) {
            addCriterion("source_account =", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountNotEqualTo(String value) {
            addCriterion("source_account <>", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountGreaterThan(String value) {
            addCriterion("source_account >", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountGreaterThanOrEqualTo(String value) {
            addCriterion("source_account >=", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountLessThan(String value) {
            addCriterion("source_account <", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountLessThanOrEqualTo(String value) {
            addCriterion("source_account <=", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountLike(String value) {
            addCriterion("source_account like", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountNotLike(String value) {
            addCriterion("source_account not like", value, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountIn(List<String> values) {
            addCriterion("source_account in", values, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountNotIn(List<String> values) {
            addCriterion("source_account not in", values, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountBetween(String value1, String value2) {
            addCriterion("source_account between", value1, value2, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andSourceAccountNotBetween(String value1, String value2) {
            addCriterion("source_account not between", value1, value2, "sourceAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountIsNull() {
            addCriterion("target_account is null");
            return (Criteria) this;
        }

        public Criteria andTargetAccountIsNotNull() {
            addCriterion("target_account is not null");
            return (Criteria) this;
        }

        public Criteria andTargetAccountEqualTo(String value) {
            addCriterion("target_account =", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountNotEqualTo(String value) {
            addCriterion("target_account <>", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountGreaterThan(String value) {
            addCriterion("target_account >", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountGreaterThanOrEqualTo(String value) {
            addCriterion("target_account >=", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountLessThan(String value) {
            addCriterion("target_account <", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountLessThanOrEqualTo(String value) {
            addCriterion("target_account <=", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountLike(String value) {
            addCriterion("target_account like", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountNotLike(String value) {
            addCriterion("target_account not like", value, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountIn(List<String> values) {
            addCriterion("target_account in", values, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountNotIn(List<String> values) {
            addCriterion("target_account not in", values, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountBetween(String value1, String value2) {
            addCriterion("target_account between", value1, value2, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andTargetAccountNotBetween(String value1, String value2) {
            addCriterion("target_account not between", value1, value2, "targetAccount");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdIsNull() {
            addCriterion("portfolio_id is null");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdIsNotNull() {
            addCriterion("portfolio_id is not null");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdEqualTo(Long value) {
            addCriterion("portfolio_id =", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdNotEqualTo(Long value) {
            addCriterion("portfolio_id <>", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdGreaterThan(Long value) {
            addCriterion("portfolio_id >", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdGreaterThanOrEqualTo(Long value) {
            addCriterion("portfolio_id >=", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdLessThan(Long value) {
            addCriterion("portfolio_id <", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdLessThanOrEqualTo(Long value) {
            addCriterion("portfolio_id <=", value, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdIn(List<Long> values) {
            addCriterion("portfolio_id in", values, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdNotIn(List<Long> values) {
            addCriterion("portfolio_id not in", values, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdBetween(Long value1, Long value2) {
            addCriterion("portfolio_id between", value1, value2, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andPortfolioIdNotBetween(Long value1, Long value2) {
            addCriterion("portfolio_id not between", value1, value2, "portfolioId");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIsNull() {
            addCriterion("filled_quantity is null");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIsNotNull() {
            addCriterion("filled_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityEqualTo(Double value) {
            addCriterion("filled_quantity =", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotEqualTo(Double value) {
            addCriterion("filled_quantity <>", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityGreaterThan(Double value) {
            addCriterion("filled_quantity >", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("filled_quantity >=", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityLessThan(Double value) {
            addCriterion("filled_quantity <", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityLessThanOrEqualTo(Double value) {
            addCriterion("filled_quantity <=", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIn(List<Double> values) {
            addCriterion("filled_quantity in", values, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotIn(List<Double> values) {
            addCriterion("filled_quantity not in", values, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityBetween(Double value1, Double value2) {
            addCriterion("filled_quantity between", value1, value2, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotBetween(Double value1, Double value2) {
            addCriterion("filled_quantity not between", value1, value2, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIsNull() {
            addCriterion("avg_price is null");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIsNotNull() {
            addCriterion("avg_price is not null");
            return (Criteria) this;
        }

        public Criteria andAvgPriceEqualTo(Double value) {
            addCriterion("avg_price =", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotEqualTo(Double value) {
            addCriterion("avg_price <>", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceGreaterThan(Double value) {
            addCriterion("avg_price >", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_price >=", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceLessThan(Double value) {
            addCriterion("avg_price <", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceLessThanOrEqualTo(Double value) {
            addCriterion("avg_price <=", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIn(List<Double> values) {
            addCriterion("avg_price in", values, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotIn(List<Double> values) {
            addCriterion("avg_price not in", values, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceBetween(Double value1, Double value2) {
            addCriterion("avg_price between", value1, value2, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotBetween(Double value1, Double value2) {
            addCriterion("avg_price not between", value1, value2, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNull() {
            addCriterion("trade_time is null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNotNull() {
            addCriterion("trade_time is not null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeEqualTo(Date value) {
            addCriterion("trade_time =", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotEqualTo(Date value) {
            addCriterion("trade_time <>", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThan(Date value) {
            addCriterion("trade_time >", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("trade_time >=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThan(Date value) {
            addCriterion("trade_time <", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThanOrEqualTo(Date value) {
            addCriterion("trade_time <=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIn(List<Date> values) {
            addCriterion("trade_time in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotIn(List<Date> values) {
            addCriterion("trade_time not in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeBetween(Date value1, Date value2) {
            addCriterion("trade_time between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotBetween(Date value1, Date value2) {
            addCriterion("trade_time not between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeIsNull() {
            addCriterion("filled_time is null");
            return (Criteria) this;
        }

        public Criteria andFilledTimeIsNotNull() {
            addCriterion("filled_time is not null");
            return (Criteria) this;
        }

        public Criteria andFilledTimeEqualTo(Date value) {
            addCriterion("filled_time =", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeNotEqualTo(Date value) {
            addCriterion("filled_time <>", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeGreaterThan(Date value) {
            addCriterion("filled_time >", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("filled_time >=", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeLessThan(Date value) {
            addCriterion("filled_time <", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeLessThanOrEqualTo(Date value) {
            addCriterion("filled_time <=", value, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeIn(List<Date> values) {
            addCriterion("filled_time in", values, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeNotIn(List<Date> values) {
            addCriterion("filled_time not in", values, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeBetween(Date value1, Date value2) {
            addCriterion("filled_time between", value1, value2, "filledTime");
            return (Criteria) this;
        }

        public Criteria andFilledTimeNotBetween(Date value1, Date value2) {
            addCriterion("filled_time not between", value1, value2, "filledTime");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNull() {
            addCriterion("business_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIsNotNull() {
            addCriterion("business_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDateEqualTo(Date value) {
            addCriterionForJDBCDate("business_date =", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("business_date <>", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThan(Date value) {
            addCriterionForJDBCDate("business_date >", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("business_date >=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThan(Date value) {
            addCriterionForJDBCDate("business_date <", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("business_date <=", value, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateIn(List<Date> values) {
            addCriterionForJDBCDate("business_date in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("business_date not in", values, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("business_date between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andBusinessDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("business_date not between", value1, value2, "businessDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateIsNull() {
            addCriterion("settle_date is null");
            return (Criteria) this;
        }

        public Criteria andSettleDateIsNotNull() {
            addCriterion("settle_date is not null");
            return (Criteria) this;
        }

        public Criteria andSettleDateEqualTo(Date value) {
            addCriterionForJDBCDate("settle_date =", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("settle_date <>", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateGreaterThan(Date value) {
            addCriterionForJDBCDate("settle_date >", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("settle_date >=", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateLessThan(Date value) {
            addCriterionForJDBCDate("settle_date <", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("settle_date <=", value, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateIn(List<Date> values) {
            addCriterionForJDBCDate("settle_date in", values, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("settle_date not in", values, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("settle_date between", value1, value2, "settleDate");
            return (Criteria) this;
        }

        public Criteria andSettleDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("settle_date not between", value1, value2, "settleDate");
            return (Criteria) this;
        }

        public Criteria andNavDateIsNull() {
            addCriterion("nav_date is null");
            return (Criteria) this;
        }

        public Criteria andNavDateIsNotNull() {
            addCriterion("nav_date is not null");
            return (Criteria) this;
        }

        public Criteria andNavDateEqualTo(Date value) {
            addCriterionForJDBCDate("nav_date =", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("nav_date <>", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateGreaterThan(Date value) {
            addCriterionForJDBCDate("nav_date >", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("nav_date >=", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateLessThan(Date value) {
            addCriterionForJDBCDate("nav_date <", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("nav_date <=", value, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateIn(List<Date> values) {
            addCriterionForJDBCDate("nav_date in", values, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("nav_date not in", values, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("nav_date between", value1, value2, "navDate");
            return (Criteria) this;
        }

        public Criteria andNavDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("nav_date not between", value1, value2, "navDate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`source` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`source` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("`source` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("`source` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("`source` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("`source` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("`source` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("`source` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("`source` like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("`source` not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("`source` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("`source` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("`source` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("`source` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}