package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface LockedAssetMapper {
    long countByExample(LockedAssetExample example);

    int deleteByExample(LockedAssetExample example);

    int deleteByPrimaryKey(Long id);

    int insert(LockedAsset record);

    int insertSelective(LockedAsset record);

    List<LockedAsset> selectByExampleWithBLOBsWithRowbounds(LockedAssetExample example, RowBounds rowBounds);

    List<LockedAsset> selectByExampleWithBLOBs(LockedAssetExample example);

    List<LockedAsset> selectByExampleWithRowbounds(LockedAssetExample example, RowBounds rowBounds);

    List<LockedAsset> selectByExample(LockedAssetExample example);

    LockedAsset selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") LockedAsset record, @Param("example") LockedAssetExample example);

    int updateByExampleWithBLOBs(@Param("record") LockedAsset record, @Param("example") LockedAssetExample example);

    int updateByExample(@Param("record") LockedAsset record, @Param("example") LockedAssetExample example);

    int updateByPrimaryKeySelective(LockedAsset record);

    int updateByPrimaryKeyWithBLOBs(LockedAsset record);

    int updateByPrimaryKey(LockedAsset record);

    int insertOrUpdate(LockedAsset record);

    int insertOrUpdateSelective(LockedAsset record);

    int batchUpdate(List<LockedAsset> list);

    int batchInsert(List<LockedAsset> list);
}