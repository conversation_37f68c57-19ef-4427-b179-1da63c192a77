package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder;
import com.tigerbrokers.stock.common.CommonConsts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public interface AggregationOrderDao {

    Long insert(AggregationOrder aggregationOrder);

    void update(AggregationOrder aggregationOrder);

    List<AggregationOrder> selectByStatus(List<String> statusList, BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType);

    List<AggregationOrder> selectByOrder(AggregationOrder aggregationOrder, List<String> statusList);

    AggregationOrder selectById(Long id);
}
