package com.tigerbrokers.oae.storage.jdbc.mapper;

import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface TradeAllocationMapper {
    long countByExample(TradeAllocationExample example);

    int deleteByExample(TradeAllocationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TradeAllocation record);

    int insertSelective(TradeAllocation record);

    List<TradeAllocation> selectByExampleWithRowbounds(TradeAllocationExample example, RowBounds rowBounds);

    List<TradeAllocation> selectByExample(TradeAllocationExample example);

    TradeAllocation selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TradeAllocation record, @Param("example") TradeAllocationExample example);

    int updateByExample(@Param("record") TradeAllocation record, @Param("example") TradeAllocationExample example);

    int updateByPrimaryKeySelective(TradeAllocation record);

    int updateByPrimaryKey(TradeAllocation record);

    int insertOrUpdate(TradeAllocation record);

    int insertOrUpdateSelective(TradeAllocation record);

    int batchUpdate(List<TradeAllocation> list);

    int batchInsert(List<TradeAllocation> list);
}