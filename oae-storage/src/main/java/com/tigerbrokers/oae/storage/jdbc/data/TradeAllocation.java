package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeAllocation {
    private Long id;

    private Long refId;

    private String segType;

    private String secType;

    private Long contractId;

    private String symbol;

    private String market;

    private String type;

    private String sourceAccount;

    private String targetAccount;

    private Long portfolioId;

    private Double filledQuantity;

    private Double avgPrice;

    private String currency;

    private Date tradeTime;

    private Date filledTime;

    private Date businessDate;

    private Date settleDate;

    private Date navDate;

    private String status;

    private String source;

    private Date createdAt;

    private Date updatedAt;
}