package com.tigerbrokers.oae.storage.jdbc.mapper.custom;

import java.util.List;

import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;

import org.apache.ibatis.annotations.Param;

public interface CustomTradeAllocationMapper {
    
    List<TradeAllocation> selectAllocationByAggregationId(@Param("aggregationId") Long aggregationId,
            @Param("market") String market, @Param("source") String source);

}
