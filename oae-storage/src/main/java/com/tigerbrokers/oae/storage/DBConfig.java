package com.tigerbrokers.oae.storage;

import com.ctrip.framework.apollo.spring.annotation.TigerConfigBean;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
public class DBConfig {

    private static final String OAE_DB_CONFIG_NAMESPACE = "OaeDBConfig";

    @Component(value = "oaeDBConfig")
    @TigerConfigBean(namespace = OAE_DB_CONFIG_NAMESPACE, prefix = "oae.storage.db")
    @Data
    public static class OaeDBConfig {
        private String url;
        private String username;
        private String password;
    }

    @Component(value = "redisConfig")
    @TigerConfigBean(namespace = OAE_DB_CONFIG_NAMESPACE, prefix = "oae.storage.redis")
    @Data
    public static class RedisConfig {

        private Boolean sentinelEnable;
        private String sentinels;
        private String masterName;
        private String password;
        private Integer dbIndex;

        private String host;
        private Integer port;
    }

    @Component(value = "oaeKafkaConfig")
    @TigerConfigBean(namespace = OAE_DB_CONFIG_NAMESPACE, prefix = "oae.kafka")
    @Data
    public static class OaeKafkaConfig {
        private String brokers;
        private String sendBuffer;
        private String retries;
        private String topic;
    }
}
