package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AggregationOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Boolean forUpdate = false;

    public AggregationOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setForUpdate(Boolean forUpdate) {
        this.forUpdate = forUpdate;
    }

    public Boolean isForUpdate() {
        return forUpdate;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDealAccountIsNull() {
            addCriterion("deal_account is null");
            return (Criteria) this;
        }

        public Criteria andDealAccountIsNotNull() {
            addCriterion("deal_account is not null");
            return (Criteria) this;
        }

        public Criteria andDealAccountEqualTo(String value) {
            addCriterion("deal_account =", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountNotEqualTo(String value) {
            addCriterion("deal_account <>", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountGreaterThan(String value) {
            addCriterion("deal_account >", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountGreaterThanOrEqualTo(String value) {
            addCriterion("deal_account >=", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountLessThan(String value) {
            addCriterion("deal_account <", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountLessThanOrEqualTo(String value) {
            addCriterion("deal_account <=", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountLike(String value) {
            addCriterion("deal_account like", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountNotLike(String value) {
            addCriterion("deal_account not like", value, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountIn(List<String> values) {
            addCriterion("deal_account in", values, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountNotIn(List<String> values) {
            addCriterion("deal_account not in", values, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountBetween(String value1, String value2) {
            addCriterion("deal_account between", value1, value2, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andDealAccountNotBetween(String value1, String value2) {
            addCriterion("deal_account not between", value1, value2, "dealAccount");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterIsNull() {
            addCriterion("execution_master is null");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterIsNotNull() {
            addCriterion("execution_master is not null");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterEqualTo(String value) {
            addCriterion("execution_master =", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterNotEqualTo(String value) {
            addCriterion("execution_master <>", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterGreaterThan(String value) {
            addCriterion("execution_master >", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterGreaterThanOrEqualTo(String value) {
            addCriterion("execution_master >=", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterLessThan(String value) {
            addCriterion("execution_master <", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterLessThanOrEqualTo(String value) {
            addCriterion("execution_master <=", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterLike(String value) {
            addCriterion("execution_master like", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterNotLike(String value) {
            addCriterion("execution_master not like", value, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterIn(List<String> values) {
            addCriterion("execution_master in", values, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterNotIn(List<String> values) {
            addCriterion("execution_master not in", values, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterBetween(String value1, String value2) {
            addCriterion("execution_master between", value1, value2, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andExecutionMasterNotBetween(String value1, String value2) {
            addCriterion("execution_master not between", value1, value2, "executionMaster");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNull() {
            addCriterion("symbol is null");
            return (Criteria) this;
        }

        public Criteria andSymbolIsNotNull() {
            addCriterion("symbol is not null");
            return (Criteria) this;
        }

        public Criteria andSymbolEqualTo(String value) {
            addCriterion("symbol =", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotEqualTo(String value) {
            addCriterion("symbol <>", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThan(String value) {
            addCriterion("symbol >", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolGreaterThanOrEqualTo(String value) {
            addCriterion("symbol >=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThan(String value) {
            addCriterion("symbol <", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLessThanOrEqualTo(String value) {
            addCriterion("symbol <=", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolLike(String value) {
            addCriterion("symbol like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotLike(String value) {
            addCriterion("symbol not like", value, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolIn(List<String> values) {
            addCriterion("symbol in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotIn(List<String> values) {
            addCriterion("symbol not in", values, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolBetween(String value1, String value2) {
            addCriterion("symbol between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andSymbolNotBetween(String value1, String value2) {
            addCriterion("symbol not between", value1, value2, "symbol");
            return (Criteria) this;
        }

        public Criteria andSegTypeIsNull() {
            addCriterion("seg_type is null");
            return (Criteria) this;
        }

        public Criteria andSegTypeIsNotNull() {
            addCriterion("seg_type is not null");
            return (Criteria) this;
        }

        public Criteria andSegTypeEqualTo(String value) {
            addCriterion("seg_type =", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotEqualTo(String value) {
            addCriterion("seg_type <>", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeGreaterThan(String value) {
            addCriterion("seg_type >", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeGreaterThanOrEqualTo(String value) {
            addCriterion("seg_type >=", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLessThan(String value) {
            addCriterion("seg_type <", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLessThanOrEqualTo(String value) {
            addCriterion("seg_type <=", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeLike(String value) {
            addCriterion("seg_type like", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotLike(String value) {
            addCriterion("seg_type not like", value, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeIn(List<String> values) {
            addCriterion("seg_type in", values, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotIn(List<String> values) {
            addCriterion("seg_type not in", values, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeBetween(String value1, String value2) {
            addCriterion("seg_type between", value1, value2, "segType");
            return (Criteria) this;
        }

        public Criteria andSegTypeNotBetween(String value1, String value2) {
            addCriterion("seg_type not between", value1, value2, "segType");
            return (Criteria) this;
        }

        public Criteria andSecTypeIsNull() {
            addCriterion("sec_type is null");
            return (Criteria) this;
        }

        public Criteria andSecTypeIsNotNull() {
            addCriterion("sec_type is not null");
            return (Criteria) this;
        }

        public Criteria andSecTypeEqualTo(String value) {
            addCriterion("sec_type =", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotEqualTo(String value) {
            addCriterion("sec_type <>", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeGreaterThan(String value) {
            addCriterion("sec_type >", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sec_type >=", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLessThan(String value) {
            addCriterion("sec_type <", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLessThanOrEqualTo(String value) {
            addCriterion("sec_type <=", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeLike(String value) {
            addCriterion("sec_type like", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotLike(String value) {
            addCriterion("sec_type not like", value, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeIn(List<String> values) {
            addCriterion("sec_type in", values, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotIn(List<String> values) {
            addCriterion("sec_type not in", values, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeBetween(String value1, String value2) {
            addCriterion("sec_type between", value1, value2, "secType");
            return (Criteria) this;
        }

        public Criteria andSecTypeNotBetween(String value1, String value2) {
            addCriterion("sec_type not between", value1, value2, "secType");
            return (Criteria) this;
        }

        public Criteria andMarketIsNull() {
            addCriterion("market is null");
            return (Criteria) this;
        }

        public Criteria andMarketIsNotNull() {
            addCriterion("market is not null");
            return (Criteria) this;
        }

        public Criteria andMarketEqualTo(String value) {
            addCriterion("market =", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotEqualTo(String value) {
            addCriterion("market <>", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThan(String value) {
            addCriterion("market >", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketGreaterThanOrEqualTo(String value) {
            addCriterion("market >=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThan(String value) {
            addCriterion("market <", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLessThanOrEqualTo(String value) {
            addCriterion("market <=", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketLike(String value) {
            addCriterion("market like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotLike(String value) {
            addCriterion("market not like", value, "market");
            return (Criteria) this;
        }

        public Criteria andMarketIn(List<String> values) {
            addCriterion("market in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotIn(List<String> values) {
            addCriterion("market not in", values, "market");
            return (Criteria) this;
        }

        public Criteria andMarketBetween(String value1, String value2) {
            addCriterion("market between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andMarketNotBetween(String value1, String value2) {
            addCriterion("market not between", value1, value2, "market");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andActionIsNull() {
            addCriterion("`action` is null");
            return (Criteria) this;
        }

        public Criteria andActionIsNotNull() {
            addCriterion("`action` is not null");
            return (Criteria) this;
        }

        public Criteria andActionEqualTo(String value) {
            addCriterion("`action` =", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotEqualTo(String value) {
            addCriterion("`action` <>", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThan(String value) {
            addCriterion("`action` >", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThanOrEqualTo(String value) {
            addCriterion("`action` >=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThan(String value) {
            addCriterion("`action` <", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThanOrEqualTo(String value) {
            addCriterion("`action` <=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLike(String value) {
            addCriterion("`action` like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotLike(String value) {
            addCriterion("`action` not like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionIn(List<String> values) {
            addCriterion("`action` in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotIn(List<String> values) {
            addCriterion("`action` not in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionBetween(String value1, String value2) {
            addCriterion("`action` between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotBetween(String value1, String value2) {
            addCriterion("`action` not between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIsNull() {
            addCriterion("trade_amount is null");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIsNotNull() {
            addCriterion("trade_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTradeAmountEqualTo(Double value) {
            addCriterion("trade_amount =", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotEqualTo(Double value) {
            addCriterion("trade_amount <>", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThan(Double value) {
            addCriterion("trade_amount >", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("trade_amount >=", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThan(Double value) {
            addCriterion("trade_amount <", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThanOrEqualTo(Double value) {
            addCriterion("trade_amount <=", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIn(List<Double> values) {
            addCriterion("trade_amount in", values, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotIn(List<Double> values) {
            addCriterion("trade_amount not in", values, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountBetween(Double value1, Double value2) {
            addCriterion("trade_amount between", value1, value2, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotBetween(Double value1, Double value2) {
            addCriterion("trade_amount not between", value1, value2, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityIsNull() {
            addCriterion("trade_quantity is null");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityIsNotNull() {
            addCriterion("trade_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityEqualTo(Double value) {
            addCriterion("trade_quantity =", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityNotEqualTo(Double value) {
            addCriterion("trade_quantity <>", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityGreaterThan(Double value) {
            addCriterion("trade_quantity >", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("trade_quantity >=", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityLessThan(Double value) {
            addCriterion("trade_quantity <", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityLessThanOrEqualTo(Double value) {
            addCriterion("trade_quantity <=", value, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityIn(List<Double> values) {
            addCriterion("trade_quantity in", values, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityNotIn(List<Double> values) {
            addCriterion("trade_quantity not in", values, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityBetween(Double value1, Double value2) {
            addCriterion("trade_quantity between", value1, value2, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andTradeQuantityNotBetween(Double value1, Double value2) {
            addCriterion("trade_quantity not between", value1, value2, "tradeQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledAmountIsNull() {
            addCriterion("filled_amount is null");
            return (Criteria) this;
        }

        public Criteria andFilledAmountIsNotNull() {
            addCriterion("filled_amount is not null");
            return (Criteria) this;
        }

        public Criteria andFilledAmountEqualTo(Double value) {
            addCriterion("filled_amount =", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountNotEqualTo(Double value) {
            addCriterion("filled_amount <>", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountGreaterThan(Double value) {
            addCriterion("filled_amount >", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("filled_amount >=", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountLessThan(Double value) {
            addCriterion("filled_amount <", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountLessThanOrEqualTo(Double value) {
            addCriterion("filled_amount <=", value, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountIn(List<Double> values) {
            addCriterion("filled_amount in", values, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountNotIn(List<Double> values) {
            addCriterion("filled_amount not in", values, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountBetween(Double value1, Double value2) {
            addCriterion("filled_amount between", value1, value2, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledAmountNotBetween(Double value1, Double value2) {
            addCriterion("filled_amount not between", value1, value2, "filledAmount");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIsNull() {
            addCriterion("filled_quantity is null");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIsNotNull() {
            addCriterion("filled_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityEqualTo(Double value) {
            addCriterion("filled_quantity =", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotEqualTo(Double value) {
            addCriterion("filled_quantity <>", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityGreaterThan(Double value) {
            addCriterion("filled_quantity >", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("filled_quantity >=", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityLessThan(Double value) {
            addCriterion("filled_quantity <", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityLessThanOrEqualTo(Double value) {
            addCriterion("filled_quantity <=", value, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityIn(List<Double> values) {
            addCriterion("filled_quantity in", values, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotIn(List<Double> values) {
            addCriterion("filled_quantity not in", values, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityBetween(Double value1, Double value2) {
            addCriterion("filled_quantity between", value1, value2, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andFilledQuantityNotBetween(Double value1, Double value2) {
            addCriterion("filled_quantity not between", value1, value2, "filledQuantity");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIsNull() {
            addCriterion("avg_price is null");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIsNotNull() {
            addCriterion("avg_price is not null");
            return (Criteria) this;
        }

        public Criteria andAvgPriceEqualTo(Double value) {
            addCriterion("avg_price =", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotEqualTo(Double value) {
            addCriterion("avg_price <>", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceGreaterThan(Double value) {
            addCriterion("avg_price >", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("avg_price >=", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceLessThan(Double value) {
            addCriterion("avg_price <", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceLessThanOrEqualTo(Double value) {
            addCriterion("avg_price <=", value, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceIn(List<Double> values) {
            addCriterion("avg_price in", values, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotIn(List<Double> values) {
            addCriterion("avg_price not in", values, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceBetween(Double value1, Double value2) {
            addCriterion("avg_price between", value1, value2, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andAvgPriceNotBetween(Double value1, Double value2) {
            addCriterion("avg_price not between", value1, value2, "avgPrice");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIsNull() {
            addCriterion("error_code is null");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIsNotNull() {
            addCriterion("error_code is not null");
            return (Criteria) this;
        }

        public Criteria andErrorCodeEqualTo(String value) {
            addCriterion("error_code =", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotEqualTo(String value) {
            addCriterion("error_code <>", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeGreaterThan(String value) {
            addCriterion("error_code >", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("error_code >=", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeLessThan(String value) {
            addCriterion("error_code <", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeLessThanOrEqualTo(String value) {
            addCriterion("error_code <=", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeLike(String value) {
            addCriterion("error_code like", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotLike(String value) {
            addCriterion("error_code not like", value, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeIn(List<String> values) {
            addCriterion("error_code in", values, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotIn(List<String> values) {
            addCriterion("error_code not in", values, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeBetween(String value1, String value2) {
            addCriterion("error_code between", value1, value2, "errorCode");
            return (Criteria) this;
        }

        public Criteria andErrorCodeNotBetween(String value1, String value2) {
            addCriterion("error_code not between", value1, value2, "errorCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`source` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`source` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("`source` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("`source` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("`source` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("`source` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("`source` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("`source` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("`source` like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("`source` not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("`source` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("`source` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("`source` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("`source` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}