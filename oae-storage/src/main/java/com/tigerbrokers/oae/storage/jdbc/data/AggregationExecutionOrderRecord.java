package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregationExecutionOrderRecord {
    private Long id;

    private Long aggregationExecutionId;

    private Long aggregationId;

    private String symbol;

    private String market;

    private String currency;

    private String action;

    private String operation;

    private Double amount;

    private Double refPrice;

    private Double quantity;

    private Integer lotSizeUnit;

    private Double preFilledQuantity;

    private Double preAvgPrice;

    private String status;

    private Date createdAt;

    private Date updatedAt;
}