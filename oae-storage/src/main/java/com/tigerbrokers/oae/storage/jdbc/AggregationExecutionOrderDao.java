package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder;
import com.tigerbrokers.stock.common.CommonConsts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public interface AggregationExecutionOrderDao {

    Long insert(AggregationExecutionOrder aggregationExecutionOrder);

    void update(AggregationExecutionOrder aggregationExecutionOrder);

    void updateByIdAndTradeId(AggregationExecutionOrder aggregationExecutionOrder);

    AggregationExecutionOrder select(Long id);

    AggregationExecutionOrder selectByTradeId(String tradeId);

    List<AggregationExecutionOrder> selectByAggregationId(Long aggregationId);

    List<AggregationExecutionOrder> selectByStatus(BusinessSource businessSource, CommonConsts.StockMarket stockMarket, SecType secType, List<String> status);

    List<AggregationExecutionOrder> selectByAggregationIds(List<Long> aggregationIds);
}
