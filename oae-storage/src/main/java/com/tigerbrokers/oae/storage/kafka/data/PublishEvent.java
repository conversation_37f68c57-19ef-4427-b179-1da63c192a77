package com.tigerbrokers.oae.storage.kafka.data;

import com.tigerbrokers.oae.entity.consts.PublishEventType;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Data
@Builder
public class PublishEvent<T> {

    private String type;

    private String source;

    private Long timestamp;

    private T data;

    public static PublishEvent createUserOrderPublishEvent(UserOrderEvent event) {
        return PublishEvent.builder()
                .type(PublishEventType.USER_ORDER.name())
                .source(event.getSource())
                .timestamp(System.currentTimeMillis())
                .data(event)
                .build();
    }
}
