package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregationExecutionOrder {
    private Long id;

    private String tradeId;

    private Long aggregationId;

    private String dealAccount;

    private String executionMaster;

    private String symbol;

    private String market;

    private String currency;

    private String action;

    private Double totalQuantity;

    private Double tradeAmount;

    private Double filledQuantity;

    private Double filledAmount;

    private Double price;

    private Double avgPrice;

    private String orderType;

    private String segType;

    private String secType;

    private Date tradeTime;

    private Date filledTime;

    private Date businessDate;

    private Date settleDate;

    private Date navDate;

    private String status;

    private String source;

    private String tradeStatus;

    private String errorCode;

    private String errorMsg;

    private String type;

    private Date createdAt;

    private Date updatedAt;
}