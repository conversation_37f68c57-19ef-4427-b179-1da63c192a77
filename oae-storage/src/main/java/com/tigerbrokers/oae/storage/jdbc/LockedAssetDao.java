package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.entity.consts.LockStep;
import com.tigerbrokers.oae.entity.consts.LockedAssetStatus;
import com.tigerbrokers.oae.storage.jdbc.data.LockedAsset;
import com.tigerbrokers.stock.common.CommonConsts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
public interface LockedAssetDao {

    void insert(LockedAsset lockedAsset);

    void insertOrUpdate(LockedAsset lockedAsset);


    void update(LockedAsset lockedAsset);

    void batchUpdate(List<LockedAsset> lockedAssetList);

    List<LockedAsset> selectByPrimaryIds(List<Long> ids);

    List<LockedAsset> selectByUserOrderIds(List<Long> userOrderIds);

    List<LockedAsset> selectByUserOrderIdsAndStep(List<Long> userOrderIds, LockStep step);


    List<LockedAsset> selectByStatus(String status, BusinessSource businessSource, CommonConsts.StockMarket market, SecType secType);

    LockedAsset getByUserOrderId(Long userOrderId, LockedAssetStatus status, LockStep lockStep);
}
