package com.tigerbrokers.oae.storage.jdbc.impl;

import com.tigerbrokers.brokerage.sdk.common.enums.SecType;
import com.tigerbrokers.oae.entity.consts.BusinessSource;
import com.tigerbrokers.oae.storage.jdbc.TradeAllocationDao;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation;
import com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample;
import com.tigerbrokers.oae.storage.jdbc.mapper.TradeAllocationMapper;
import com.tigerbrokers.oae.storage.jdbc.mapper.custom.CustomTradeAllocationMapper;
import com.tigerbrokers.stock.common.CommonConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Repository
public class TradeAllocationDaoImpl implements TradeAllocationDao {

    @Autowired
    private TradeAllocationMapper tradeAllocationMapper;

    @Autowired
    private CustomTradeAllocationMapper customTradeAllocationMapper;

    @Override
    public void batchInsert(List<TradeAllocation> tradeAllocations) {
        tradeAllocationMapper.batchInsert(tradeAllocations);
    }

    @Override
    public void update(TradeAllocation tradeAllocation) {
        tradeAllocationMapper.updateByPrimaryKeySelective(tradeAllocation);
    }

    @Override
    public List<TradeAllocation> select(List<Long> ids) {
        TradeAllocationExample example = new TradeAllocationExample();
        example.createCriteria().andIdIn(ids);
        return tradeAllocationMapper.selectByExample(example);
    }

    @Override
    public void batchUpdate(List<TradeAllocation> tradeAllocations) {
        tradeAllocationMapper.batchUpdate(tradeAllocations);
    }

    @Override
    public List<TradeAllocation> selectByStatus(String status, BusinessSource source, CommonConsts.StockMarket market, SecType secType) {
        TradeAllocationExample example = new TradeAllocationExample();
        TradeAllocationExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(status);
        if (source != null) {
            criteria.andSourceEqualTo(source.name());
        }
        if (market != null) {
            criteria.andMarketEqualTo(market.name());
        }
        if (secType != null) {
            criteria.andSecTypeEqualTo(secType.name());
        }
        return tradeAllocationMapper.selectByExample(example);
    }

    @Override
    public List<TradeAllocation> selectByBusinessDate(String market, String source, String secType, Date start, Date end) {
        TradeAllocationExample example = new TradeAllocationExample();
        if (Objects.isNull(start) && Objects.isNull(end)) {
            example.createCriteria().andMarketEqualTo(market).andSourceEqualTo(source).andSecTypeEqualTo(secType).andBusinessDateIsNull();
        } else {
            example.createCriteria().andMarketEqualTo(market).andSourceEqualTo(source).andSecTypeEqualTo(secType).andBusinessDateBetween(start, end);
        }
        return tradeAllocationMapper.selectByExample(example);
    }

    @Override
    public List<TradeAllocation> selectAllocationsByAggregationId(Long aggregationId, String market, String source) {
        return customTradeAllocationMapper.selectAllocationByAggregationId(aggregationId, market, source);
    }
}
