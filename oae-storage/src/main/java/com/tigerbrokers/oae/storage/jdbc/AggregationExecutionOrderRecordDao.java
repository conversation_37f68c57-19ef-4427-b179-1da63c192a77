package com.tigerbrokers.oae.storage.jdbc;

import com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/6
 */
public interface AggregationExecutionOrderRecordDao {

    void insert(AggregationExecutionOrderRecord aggregationExecutionOrderRecord);

    void update(AggregationExecutionOrderRecord aggregationExecutionOrderRecord);

    List<AggregationExecutionOrderRecord> selectByExecutionId(Long executionId);
}
