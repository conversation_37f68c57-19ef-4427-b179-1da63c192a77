package com.tigerbrokers.oae.storage.jdbc.config;

import com.tigerbrokers.oae.storage.DBConfig;
import com.tigerbrokers.tools.storage.mysql.DBUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Configuration
@EnableTransactionManagement
public class DBConfiguration {

    @Bean(name = "oaeDataSource")
    @Qualifier(value = "oaeDataSource")
    public DataSource oaeDataSource(@Qualifier("oaeDBConfig") DBConfig.OaeDBConfig oaeDBConfig) {
        return DBUtils.getDefaultDataSource(oaeDBConfig.getUrl(), oaeDBConfig.getUsername(), oaeDBConfig.getPassword());
    }

    @Bean(name = "oaeSqlSessionFactory")
    @Qualifier(value = "oaeSqlSessionFactory")
    @Primary
    public SqlSessionFactory oaeSqlSessionFactory(@Qualifier("oaeDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean sqlSessionFactory = new SqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setConfigLocation(new ClassPathResource("mybatis-spring-config.xml"));
        sqlSessionFactory.setFailFast(true);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:/mapper/*.xml");
        Resource[] customResources = resolver.getResources("classpath:/mapper/custom/*.xml");
        Resource[] allResources = new Resource[resources.length + customResources.length];
        System.arraycopy(resources, 0, allResources, 0, resources.length);
        System.arraycopy(customResources, 0, allResources, resources.length, customResources.length);
        sqlSessionFactory.setMapperLocations(allResources);
        return sqlSessionFactory.getObject();
    }

    @Bean(name = "oaeSqlSessionTemplate")
    @Qualifier(value = "oaeSqlSessionTemplate")
    public SqlSessionTemplate oaeSqlSessionTemplate(@Qualifier("oaeDataSource") DataSource dataSource) throws Exception {
        return new SqlSessionTemplate(oaeSqlSessionFactory(dataSource));
    }

    @Bean("oaeTransactionManager")
    @Qualifier(value = "oaeTransactionManager")
    public PlatformTransactionManager oaeTransactionManager(@Qualifier("oaeDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public MapperScannerConfigurer oaeScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setSqlSessionTemplateBeanName("oaeSqlSessionTemplate");
        mapperScannerConfigurer.setBasePackage("com.tigerbrokers.oae.storage.jdbc.mapper");
        return mapperScannerConfigurer;
    }
}
