package com.tigerbrokers.oae.storage.jdbc.data;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregationOrder {
    private Long id;

    private String dealAccount;

    private String executionMaster;

    private String symbol;

    private String segType;

    private String secType;

    private String market;

    private String currency;

    private String action;

    private Double tradeAmount;

    private Double tradeQuantity;

    private Double filledAmount;

    private Double filledQuantity;

    private Double avgPrice;

    private String orderType;

    private String errorCode;

    private String status;

    private String source;

    private Date createdAt;

    private Date updatedAt;
}