<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.AggregationOrderMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deal_account" jdbcType="VARCHAR" property="dealAccount" />
    <result column="execution_master" jdbcType="VARCHAR" property="executionMaster" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="seg_type" jdbcType="VARCHAR" property="segType" />
    <result column="sec_type" jdbcType="VARCHAR" property="secType" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="trade_amount" jdbcType="DOUBLE" property="tradeAmount" />
    <result column="trade_quantity" jdbcType="DOUBLE" property="tradeQuantity" />
    <result column="filled_amount" jdbcType="DOUBLE" property="filledAmount" />
    <result column="filled_quantity" jdbcType="DOUBLE" property="filledQuantity" />
    <result column="avg_price" jdbcType="DOUBLE" property="avgPrice" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, deal_account, execution_master, symbol, seg_type, sec_type, market, currency, 
    `action`, trade_amount, trade_quantity, filled_amount, filled_quantity, avg_price, 
    order_type, error_code, `status`, `source`, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aggregation_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aggregation_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample">
    delete from aggregation_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_order (deal_account, execution_master, symbol, 
      seg_type, sec_type, market, 
      currency, `action`, trade_amount, 
      trade_quantity, filled_amount, filled_quantity, 
      avg_price, order_type, error_code, 
      `status`, `source`, created_at, 
      updated_at)
    values (#{dealAccount,jdbcType=VARCHAR}, #{executionMaster,jdbcType=VARCHAR}, #{symbol,jdbcType=VARCHAR}, 
      #{segType,jdbcType=VARCHAR}, #{secType,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR}, 
      #{currency,jdbcType=VARCHAR}, #{action,jdbcType=VARCHAR}, #{tradeAmount,jdbcType=DOUBLE}, 
      #{tradeQuantity,jdbcType=DOUBLE}, #{filledAmount,jdbcType=DOUBLE}, #{filledQuantity,jdbcType=DOUBLE}, 
      #{avgPrice,jdbcType=DOUBLE}, #{orderType,jdbcType=VARCHAR}, #{errorCode,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeQuantity != null">
        trade_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="tradeQuantity != null">
        #{tradeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample" resultType="java.lang.Long">
    select count(*) from aggregation_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aggregation_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dealAccount != null">
        deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.executionMaster != null">
        execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="record.symbol != null">
        symbol = #{record.symbol,jdbcType=VARCHAR},
      </if>
      <if test="record.segType != null">
        seg_type = #{record.segType,jdbcType=VARCHAR},
      </if>
      <if test="record.secType != null">
        sec_type = #{record.secType,jdbcType=VARCHAR},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null">
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeAmount != null">
        trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.tradeQuantity != null">
        trade_quantity = #{record.tradeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.filledAmount != null">
        filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.filledQuantity != null">
        filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.avgPrice != null">
        avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.errorCode != null">
        error_code = #{record.errorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aggregation_order
    set id = #{record.id,jdbcType=BIGINT},
      deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      trade_quantity = #{record.tradeQuantity,jdbcType=DOUBLE},
      filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      error_code = #{record.errorCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    update aggregation_order
    <set>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="tradeQuantity != null">
        trade_quantity = #{tradeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    update aggregation_order
    set deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      trade_quantity = #{tradeQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      order_type = #{orderType,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    insert into aggregation_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      deal_account,
      execution_master,
      symbol,
      seg_type,
      sec_type,
      market,
      currency,
      `action`,
      trade_amount,
      trade_quantity,
      filled_amount,
      filled_quantity,
      avg_price,
      order_type,
      error_code,
      `status`,
      `source`,
      created_at,
      updated_at,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{dealAccount,jdbcType=VARCHAR},
      #{executionMaster,jdbcType=VARCHAR},
      #{symbol,jdbcType=VARCHAR},
      #{segType,jdbcType=VARCHAR},
      #{secType,jdbcType=VARCHAR},
      #{market,jdbcType=VARCHAR},
      #{currency,jdbcType=VARCHAR},
      #{action,jdbcType=VARCHAR},
      #{tradeAmount,jdbcType=DOUBLE},
      #{tradeQuantity,jdbcType=DOUBLE},
      #{filledAmount,jdbcType=DOUBLE},
      #{filledQuantity,jdbcType=DOUBLE},
      #{avgPrice,jdbcType=DOUBLE},
      #{orderType,jdbcType=VARCHAR},
      #{errorCode,jdbcType=VARCHAR},
      #{status,jdbcType=VARCHAR},
      #{source,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      trade_quantity = #{tradeQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      order_type = #{orderType,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationOrder">
    insert into aggregation_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeQuantity != null">
        trade_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="tradeQuantity != null">
        #{tradeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="tradeQuantity != null">
        trade_quantity = #{tradeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update aggregation_order
      <set>
        <trim suffixOverrides=",">
          <if test="item.dealAccount != null">
            deal_account = #{item.dealAccount,jdbcType=VARCHAR},
          </if>
          <if test="item.executionMaster != null">
            execution_master = #{item.executionMaster,jdbcType=VARCHAR},
          </if>
          <if test="item.symbol != null">
            symbol = #{item.symbol,jdbcType=VARCHAR},
          </if>
          <if test="item.segType != null">
            seg_type = #{item.segType,jdbcType=VARCHAR},
          </if>
          <if test="item.secType != null">
            sec_type = #{item.secType,jdbcType=VARCHAR},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.action != null">
            `action` = #{item.action,jdbcType=VARCHAR},
          </if>
          <if test="item.tradeAmount != null">
            trade_amount = #{item.tradeAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.tradeQuantity != null">
            trade_quantity = #{item.tradeQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.filledAmount != null">
            filled_amount = #{item.filledAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.filledQuantity != null">
            filled_quantity = #{item.filledQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.avgPrice != null">
            avg_price = #{item.avgPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.orderType != null">
            order_type = #{item.orderType,jdbcType=VARCHAR},
          </if>
          <if test="item.errorCode != null">
            error_code = #{item.errorCode,jdbcType=VARCHAR},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.source != null">
            `source` = #{item.source,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into aggregation_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      deal_account,
      execution_master,
      symbol,
      seg_type,
      sec_type,
      market,
      currency,
      `action`,
      trade_amount,
      trade_quantity,
      filled_amount,
      filled_quantity,
      avg_price,
      order_type,
      error_code,
      `status`,
      `source`,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.dealAccount,jdbcType=VARCHAR},
        #{item.executionMaster,jdbcType=VARCHAR},
        #{item.symbol,jdbcType=VARCHAR},
        #{item.segType,jdbcType=VARCHAR},
        #{item.secType,jdbcType=VARCHAR},
        #{item.market,jdbcType=VARCHAR},
        #{item.currency,jdbcType=VARCHAR},
        #{item.action,jdbcType=VARCHAR},
        #{item.tradeAmount,jdbcType=DOUBLE},
        #{item.tradeQuantity,jdbcType=DOUBLE},
        #{item.filledAmount,jdbcType=DOUBLE},
        #{item.filledQuantity,jdbcType=DOUBLE},
        #{item.avgPrice,jdbcType=DOUBLE},
        #{item.orderType,jdbcType=VARCHAR},
        #{item.errorCode,jdbcType=VARCHAR},
        #{item.status,jdbcType=VARCHAR},
        #{item.source,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>