<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.custom.CustomTradeAllocationMapper">

    <select id="selectAllocationByAggregationId" resultMap="com.tigerbrokers.oae.storage.jdbc.mapper.TradeAllocationMapper.BaseResultMap">
        select * from trade_allocation where ref_id in (
        select id from user_order where aggregation_id = #{aggregationId}
        ) and source = #{source} and type = 'USER' and market = #{market}
        union
        select * from trade_allocation where ref_id = #{aggregationId} and source = #{source} and type = 'TRADER' and market = #{market}
    </select>
</mapper>