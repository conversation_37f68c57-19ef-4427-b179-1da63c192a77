<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.LockedAssetMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_order_id" jdbcType="BIGINT" property="userOrderId" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="portfolio_id" jdbcType="BIGINT" property="portfolioId" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="quantity" jdbcType="DOUBLE" property="quantity" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="seg_type" jdbcType="VARCHAR" property="segType" />
    <result column="sec_type" jdbcType="VARCHAR" property="secType" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="lock_step" jdbcType="VARCHAR" property="lockStep" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_order_id, account, portfolio_id, market, currency, amount, quantity, trade_time, 
    seg_type, sec_type, `type`, `status`, lock_step, contract_id, `source`, created_at, 
    updated_at
  </sql>
  <sql id="Blob_Column_List">
    detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from locked_asset
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from locked_asset
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample">
    delete from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into locked_asset (user_order_id, account, portfolio_id, 
      market, currency, amount, 
      quantity, trade_time, seg_type, 
      sec_type, `type`, `status`, 
      lock_step, contract_id, `source`, 
      created_at, updated_at, detail
      )
    values (#{userOrderId,jdbcType=BIGINT}, #{account,jdbcType=VARCHAR}, #{portfolioId,jdbcType=BIGINT}, 
      #{market,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{amount,jdbcType=DOUBLE}, 
      #{quantity,jdbcType=DOUBLE}, #{tradeTime,jdbcType=TIMESTAMP}, #{segType,jdbcType=VARCHAR}, 
      #{secType,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{lockStep,jdbcType=VARCHAR}, #{contractId,jdbcType=BIGINT}, #{source,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{detail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into locked_asset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userOrderId != null">
        user_order_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="lockStep != null">
        lock_step,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userOrderId != null">
        #{userOrderId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStep != null">
        #{lockStep,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample" resultType="java.lang.Long">
    select count(*) from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update locked_asset
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userOrderId != null">
        user_order_id = #{record.userOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioId != null">
        portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DOUBLE},
      </if>
      <if test="record.tradeTime != null">
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.segType != null">
        seg_type = #{record.segType,jdbcType=VARCHAR},
      </if>
      <if test="record.secType != null">
        sec_type = #{record.secType,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.lockStep != null">
        lock_step = #{record.lockStep,jdbcType=VARCHAR},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.detail != null">
        detail = #{record.detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update locked_asset
    set id = #{record.id,jdbcType=BIGINT},
      user_order_id = #{record.userOrderId,jdbcType=BIGINT},
      account = #{record.account,jdbcType=VARCHAR},
      portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      lock_step = #{record.lockStep,jdbcType=VARCHAR},
      contract_id = #{record.contractId,jdbcType=BIGINT},
      `source` = #{record.source,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      detail = #{record.detail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update locked_asset
    set id = #{record.id,jdbcType=BIGINT},
      user_order_id = #{record.userOrderId,jdbcType=BIGINT},
      account = #{record.account,jdbcType=VARCHAR},
      portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      lock_step = #{record.lockStep,jdbcType=VARCHAR},
      contract_id = #{record.contractId,jdbcType=BIGINT},
      `source` = #{record.source,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    update locked_asset
    <set>
      <if test="userOrderId != null">
        user_order_id = #{userOrderId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStep != null">
        lock_step = #{lockStep,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    update locked_asset
    set user_order_id = #{userOrderId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      lock_step = #{lockStep,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=BIGINT},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      detail = #{detail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    update locked_asset
    set user_order_id = #{userOrderId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      lock_step = #{lockStep,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=BIGINT},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAssetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from locked_asset
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    insert into locked_asset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      user_order_id,
      account,
      portfolio_id,
      market,
      currency,
      amount,
      quantity,
      trade_time,
      seg_type,
      sec_type,
      `type`,
      `status`,
      lock_step,
      contract_id,
      `source`,
      created_at,
      updated_at,
      detail,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{userOrderId,jdbcType=BIGINT},
      #{account,jdbcType=VARCHAR},
      #{portfolioId,jdbcType=BIGINT},
      #{market,jdbcType=VARCHAR},
      #{currency,jdbcType=VARCHAR},
      #{amount,jdbcType=DOUBLE},
      #{quantity,jdbcType=DOUBLE},
      #{tradeTime,jdbcType=TIMESTAMP},
      #{segType,jdbcType=VARCHAR},
      #{secType,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR},
      #{status,jdbcType=VARCHAR},
      #{lockStep,jdbcType=VARCHAR},
      #{contractId,jdbcType=BIGINT},
      #{source,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
      #{detail,jdbcType=LONGVARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      user_order_id = #{userOrderId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      lock_step = #{lockStep,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=BIGINT},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      detail = #{detail,jdbcType=LONGVARCHAR},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.LockedAsset">
    insert into locked_asset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userOrderId != null">
        user_order_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="lockStep != null">
        lock_step,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userOrderId != null">
        #{userOrderId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStep != null">
        #{lockStep,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="userOrderId != null">
        user_order_id = #{userOrderId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStep != null">
        lock_step = #{lockStep,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update locked_asset
      <set>
        <trim suffixOverrides=",">
          <if test="item.userOrderId != null">
            user_order_id = #{item.userOrderId,jdbcType=BIGINT},
          </if>
          <if test="item.account != null">
            account = #{item.account,jdbcType=VARCHAR},
          </if>
          <if test="item.portfolioId != null">
            portfolio_id = #{item.portfolioId,jdbcType=BIGINT},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.amount != null">
            amount = #{item.amount,jdbcType=DOUBLE},
          </if>
          <if test="item.quantity != null">
            quantity = #{item.quantity,jdbcType=DOUBLE},
          </if>
          <if test="item.tradeTime != null">
            trade_time = #{item.tradeTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.segType != null">
            seg_type = #{item.segType,jdbcType=VARCHAR},
          </if>
          <if test="item.secType != null">
            sec_type = #{item.secType,jdbcType=VARCHAR},
          </if>
          <if test="item.type != null">
            `type` = #{item.type,jdbcType=VARCHAR},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.lockStep != null">
            lock_step = #{item.lockStep,jdbcType=VARCHAR},
          </if>
          <if test="item.contractId != null">
            contract_id = #{item.contractId,jdbcType=BIGINT},
          </if>
          <if test="item.source != null">
            `source` = #{item.source,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.detail != null">
            detail = #{item.detail,jdbcType=LONGVARCHAR},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into locked_asset
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      user_order_id,
      account,
      portfolio_id,
      market,
      currency,
      amount,
      quantity,
      trade_time,
      seg_type,
      sec_type,
      `type`,
      `status`,
      lock_step,
      contract_id,
      `source`,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.userOrderId,jdbcType=BIGINT},
        #{item.account,jdbcType=VARCHAR},
        #{item.portfolioId,jdbcType=BIGINT},
        #{item.market,jdbcType=VARCHAR},
        #{item.currency,jdbcType=VARCHAR},
        #{item.amount,jdbcType=DOUBLE},
        #{item.quantity,jdbcType=DOUBLE},
        #{item.tradeTime,jdbcType=TIMESTAMP},
        #{item.segType,jdbcType=VARCHAR},
        #{item.secType,jdbcType=VARCHAR},
        #{item.type,jdbcType=VARCHAR},
        #{item.status,jdbcType=VARCHAR},
        #{item.lockStep,jdbcType=VARCHAR},
        #{item.contractId,jdbcType=BIGINT},
        #{item.source,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>