<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.AggregationExecutionOrderRecordMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="aggregation_execution_id" jdbcType="BIGINT" property="aggregationExecutionId" />
    <result column="aggregation_id" jdbcType="BIGINT" property="aggregationId" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="ref_price" jdbcType="DOUBLE" property="refPrice" />
    <result column="quantity" jdbcType="DOUBLE" property="quantity" />
    <result column="lot_size_unit" jdbcType="INTEGER" property="lotSizeUnit" />
    <result column="pre_filled_quantity" jdbcType="DOUBLE" property="preFilledQuantity" />
    <result column="pre_avg_price" jdbcType="DOUBLE" property="preAvgPrice" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, aggregation_execution_id, aggregation_id, symbol, market, currency, `action`, 
    `operation`, amount, ref_price, quantity, lot_size_unit, pre_filled_quantity, pre_avg_price, 
    `status`, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_execution_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aggregation_execution_order_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aggregation_execution_order_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample">
    delete from aggregation_execution_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_execution_order_record (aggregation_execution_id, aggregation_id, 
      symbol, market, currency, 
      `action`, `operation`, amount, 
      ref_price, quantity, lot_size_unit, 
      pre_filled_quantity, pre_avg_price, `status`, 
      created_at, updated_at)
    values (#{aggregationExecutionId,jdbcType=BIGINT}, #{aggregationId,jdbcType=BIGINT}, 
      #{symbol,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, 
      #{action,jdbcType=VARCHAR}, #{operation,jdbcType=VARCHAR}, #{amount,jdbcType=DOUBLE}, 
      #{refPrice,jdbcType=DOUBLE}, #{quantity,jdbcType=DOUBLE}, #{lotSizeUnit,jdbcType=INTEGER}, 
      #{preFilledQuantity,jdbcType=DOUBLE}, #{preAvgPrice,jdbcType=DOUBLE}, #{status,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_execution_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="aggregationExecutionId != null">
        aggregation_execution_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="refPrice != null">
        ref_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="lotSizeUnit != null">
        lot_size_unit,
      </if>
      <if test="preFilledQuantity != null">
        pre_filled_quantity,
      </if>
      <if test="preAvgPrice != null">
        pre_avg_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="aggregationExecutionId != null">
        #{aggregationExecutionId,jdbcType=BIGINT},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="refPrice != null">
        #{refPrice,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="lotSizeUnit != null">
        #{lotSizeUnit,jdbcType=INTEGER},
      </if>
      <if test="preFilledQuantity != null">
        #{preFilledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="preAvgPrice != null">
        #{preAvgPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample" resultType="java.lang.Long">
    select count(*) from aggregation_execution_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aggregation_execution_order_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.aggregationExecutionId != null">
        aggregation_execution_id = #{record.aggregationExecutionId,jdbcType=BIGINT},
      </if>
      <if test="record.aggregationId != null">
        aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      </if>
      <if test="record.symbol != null">
        symbol = #{record.symbol,jdbcType=VARCHAR},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null">
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.operation != null">
        `operation` = #{record.operation,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.refPrice != null">
        ref_price = #{record.refPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DOUBLE},
      </if>
      <if test="record.lotSizeUnit != null">
        lot_size_unit = #{record.lotSizeUnit,jdbcType=INTEGER},
      </if>
      <if test="record.preFilledQuantity != null">
        pre_filled_quantity = #{record.preFilledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.preAvgPrice != null">
        pre_avg_price = #{record.preAvgPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aggregation_execution_order_record
    set id = #{record.id,jdbcType=BIGINT},
      aggregation_execution_id = #{record.aggregationExecutionId,jdbcType=BIGINT},
      aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      `operation` = #{record.operation,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      ref_price = #{record.refPrice,jdbcType=DOUBLE},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      lot_size_unit = #{record.lotSizeUnit,jdbcType=INTEGER},
      pre_filled_quantity = #{record.preFilledQuantity,jdbcType=DOUBLE},
      pre_avg_price = #{record.preAvgPrice,jdbcType=DOUBLE},
      `status` = #{record.status,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    update aggregation_execution_order_record
    <set>
      <if test="aggregationExecutionId != null">
        aggregation_execution_id = #{aggregationExecutionId,jdbcType=BIGINT},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="refPrice != null">
        ref_price = #{refPrice,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="lotSizeUnit != null">
        lot_size_unit = #{lotSizeUnit,jdbcType=INTEGER},
      </if>
      <if test="preFilledQuantity != null">
        pre_filled_quantity = #{preFilledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="preAvgPrice != null">
        pre_avg_price = #{preAvgPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    update aggregation_execution_order_record
    set aggregation_execution_id = #{aggregationExecutionId,jdbcType=BIGINT},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      ref_price = #{refPrice,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      lot_size_unit = #{lotSizeUnit,jdbcType=INTEGER},
      pre_filled_quantity = #{preFilledQuantity,jdbcType=DOUBLE},
      pre_avg_price = #{preAvgPrice,jdbcType=DOUBLE},
      `status` = #{status,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_execution_order_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    insert into aggregation_execution_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      aggregation_execution_id,
      aggregation_id,
      symbol,
      market,
      currency,
      `action`,
      `operation`,
      amount,
      ref_price,
      quantity,
      lot_size_unit,
      pre_filled_quantity,
      pre_avg_price,
      `status`,
      created_at,
      updated_at,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{aggregationExecutionId,jdbcType=BIGINT},
      #{aggregationId,jdbcType=BIGINT},
      #{symbol,jdbcType=VARCHAR},
      #{market,jdbcType=VARCHAR},
      #{currency,jdbcType=VARCHAR},
      #{action,jdbcType=VARCHAR},
      #{operation,jdbcType=VARCHAR},
      #{amount,jdbcType=DOUBLE},
      #{refPrice,jdbcType=DOUBLE},
      #{quantity,jdbcType=DOUBLE},
      #{lotSizeUnit,jdbcType=INTEGER},
      #{preFilledQuantity,jdbcType=DOUBLE},
      #{preAvgPrice,jdbcType=DOUBLE},
      #{status,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      aggregation_execution_id = #{aggregationExecutionId,jdbcType=BIGINT},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      ref_price = #{refPrice,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      lot_size_unit = #{lotSizeUnit,jdbcType=INTEGER},
      pre_filled_quantity = #{preFilledQuantity,jdbcType=DOUBLE},
      pre_avg_price = #{preAvgPrice,jdbcType=DOUBLE},
      `status` = #{status,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderRecord">
    insert into aggregation_execution_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="aggregationExecutionId != null">
        aggregation_execution_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="refPrice != null">
        ref_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="lotSizeUnit != null">
        lot_size_unit,
      </if>
      <if test="preFilledQuantity != null">
        pre_filled_quantity,
      </if>
      <if test="preAvgPrice != null">
        pre_avg_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="aggregationExecutionId != null">
        #{aggregationExecutionId,jdbcType=BIGINT},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="refPrice != null">
        #{refPrice,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="lotSizeUnit != null">
        #{lotSizeUnit,jdbcType=INTEGER},
      </if>
      <if test="preFilledQuantity != null">
        #{preFilledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="preAvgPrice != null">
        #{preAvgPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="aggregationExecutionId != null">
        aggregation_execution_id = #{aggregationExecutionId,jdbcType=BIGINT},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="refPrice != null">
        ref_price = #{refPrice,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="lotSizeUnit != null">
        lot_size_unit = #{lotSizeUnit,jdbcType=INTEGER},
      </if>
      <if test="preFilledQuantity != null">
        pre_filled_quantity = #{preFilledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="preAvgPrice != null">
        pre_avg_price = #{preAvgPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update aggregation_execution_order_record
      <set>
        <trim suffixOverrides=",">
          <if test="item.aggregationExecutionId != null">
            aggregation_execution_id = #{item.aggregationExecutionId,jdbcType=BIGINT},
          </if>
          <if test="item.aggregationId != null">
            aggregation_id = #{item.aggregationId,jdbcType=BIGINT},
          </if>
          <if test="item.symbol != null">
            symbol = #{item.symbol,jdbcType=VARCHAR},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.action != null">
            `action` = #{item.action,jdbcType=VARCHAR},
          </if>
          <if test="item.operation != null">
            `operation` = #{item.operation,jdbcType=VARCHAR},
          </if>
          <if test="item.amount != null">
            amount = #{item.amount,jdbcType=DOUBLE},
          </if>
          <if test="item.refPrice != null">
            ref_price = #{item.refPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.quantity != null">
            quantity = #{item.quantity,jdbcType=DOUBLE},
          </if>
          <if test="item.lotSizeUnit != null">
            lot_size_unit = #{item.lotSizeUnit,jdbcType=INTEGER},
          </if>
          <if test="item.preFilledQuantity != null">
            pre_filled_quantity = #{item.preFilledQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.preAvgPrice != null">
            pre_avg_price = #{item.preAvgPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into aggregation_execution_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      aggregation_execution_id,
      aggregation_id,
      symbol,
      market,
      currency,
      `action`,
      `operation`,
      amount,
      ref_price,
      quantity,
      lot_size_unit,
      pre_filled_quantity,
      pre_avg_price,
      `status`,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.aggregationExecutionId,jdbcType=BIGINT},
        #{item.aggregationId,jdbcType=BIGINT},
        #{item.symbol,jdbcType=VARCHAR},
        #{item.market,jdbcType=VARCHAR},
        #{item.currency,jdbcType=VARCHAR},
        #{item.action,jdbcType=VARCHAR},
        #{item.operation,jdbcType=VARCHAR},
        #{item.amount,jdbcType=DOUBLE},
        #{item.refPrice,jdbcType=DOUBLE},
        #{item.quantity,jdbcType=DOUBLE},
        #{item.lotSizeUnit,jdbcType=INTEGER},
        #{item.preFilledQuantity,jdbcType=DOUBLE},
        #{item.preAvgPrice,jdbcType=DOUBLE},
        #{item.status,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>