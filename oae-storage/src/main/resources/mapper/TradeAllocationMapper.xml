<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.TradeAllocationMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ref_id" jdbcType="BIGINT" property="refId" />
    <result column="seg_type" jdbcType="VARCHAR" property="segType" />
    <result column="sec_type" jdbcType="VARCHAR" property="secType" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="source_account" jdbcType="VARCHAR" property="sourceAccount" />
    <result column="target_account" jdbcType="VARCHAR" property="targetAccount" />
    <result column="portfolio_id" jdbcType="BIGINT" property="portfolioId" />
    <result column="filled_quantity" jdbcType="DOUBLE" property="filledQuantity" />
    <result column="avg_price" jdbcType="DOUBLE" property="avgPrice" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="filled_time" jdbcType="TIMESTAMP" property="filledTime" />
    <result column="business_date" jdbcType="DATE" property="businessDate" />
    <result column="settle_date" jdbcType="DATE" property="settleDate" />
    <result column="nav_date" jdbcType="DATE" property="navDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ref_id, seg_type, sec_type, contract_id, symbol, market, `type`, source_account, 
    target_account, portfolio_id, filled_quantity, avg_price, currency, trade_time, filled_time, 
    business_date, settle_date, nav_date, `status`, `source`, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from trade_allocation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trade_allocation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from trade_allocation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample">
    delete from trade_allocation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into trade_allocation (ref_id, seg_type, sec_type, 
      contract_id, symbol, market, 
      `type`, source_account, target_account, 
      portfolio_id, filled_quantity, avg_price, 
      currency, trade_time, filled_time, 
      business_date, settle_date, nav_date, 
      `status`, `source`, created_at, 
      updated_at)
    values (#{refId,jdbcType=BIGINT}, #{segType,jdbcType=VARCHAR}, #{secType,jdbcType=VARCHAR}, 
      #{contractId,jdbcType=BIGINT}, #{symbol,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{sourceAccount,jdbcType=VARCHAR}, #{targetAccount,jdbcType=VARCHAR}, 
      #{portfolioId,jdbcType=BIGINT}, #{filledQuantity,jdbcType=DOUBLE}, #{avgPrice,jdbcType=DOUBLE}, 
      #{currency,jdbcType=VARCHAR}, #{tradeTime,jdbcType=TIMESTAMP}, #{filledTime,jdbcType=TIMESTAMP}, 
      #{businessDate,jdbcType=DATE}, #{settleDate,jdbcType=DATE}, #{navDate,jdbcType=DATE}, 
      #{status,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into trade_allocation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refId != null">
        ref_id,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="sourceAccount != null">
        source_account,
      </if>
      <if test="targetAccount != null">
        target_account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="settleDate != null">
        settle_date,
      </if>
      <if test="navDate != null">
        nav_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refId != null">
        #{refId,jdbcType=BIGINT},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="sourceAccount != null">
        #{sourceAccount,jdbcType=VARCHAR},
      </if>
      <if test="targetAccount != null">
        #{targetAccount,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample" resultType="java.lang.Long">
    select count(*) from trade_allocation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update trade_allocation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.refId != null">
        ref_id = #{record.refId,jdbcType=BIGINT},
      </if>
      <if test="record.segType != null">
        seg_type = #{record.segType,jdbcType=VARCHAR},
      </if>
      <if test="record.secType != null">
        sec_type = #{record.secType,jdbcType=VARCHAR},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=BIGINT},
      </if>
      <if test="record.symbol != null">
        symbol = #{record.symbol,jdbcType=VARCHAR},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceAccount != null">
        source_account = #{record.sourceAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.targetAccount != null">
        target_account = #{record.targetAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioId != null">
        portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      </if>
      <if test="record.filledQuantity != null">
        filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.avgPrice != null">
        avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeTime != null">
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.filledTime != null">
        filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=DATE},
      </if>
      <if test="record.settleDate != null">
        settle_date = #{record.settleDate,jdbcType=DATE},
      </if>
      <if test="record.navDate != null">
        nav_date = #{record.navDate,jdbcType=DATE},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update trade_allocation
    set id = #{record.id,jdbcType=BIGINT},
      ref_id = #{record.refId,jdbcType=BIGINT},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      contract_id = #{record.contractId,jdbcType=BIGINT},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      source_account = #{record.sourceAccount,jdbcType=VARCHAR},
      target_account = #{record.targetAccount,jdbcType=VARCHAR},
      portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      currency = #{record.currency,jdbcType=VARCHAR},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      business_date = #{record.businessDate,jdbcType=DATE},
      settle_date = #{record.settleDate,jdbcType=DATE},
      nav_date = #{record.navDate,jdbcType=DATE},
      `status` = #{record.status,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    update trade_allocation
    <set>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=BIGINT},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="sourceAccount != null">
        source_account = #{sourceAccount,jdbcType=VARCHAR},
      </if>
      <if test="targetAccount != null">
        target_account = #{targetAccount,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        settle_date = #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        nav_date = #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    update trade_allocation
    set ref_id = #{refId,jdbcType=BIGINT},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=BIGINT},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      source_account = #{sourceAccount,jdbcType=VARCHAR},
      target_account = #{targetAccount,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      currency = #{currency,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      business_date = #{businessDate,jdbcType=DATE},
      settle_date = #{settleDate,jdbcType=DATE},
      nav_date = #{navDate,jdbcType=DATE},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from trade_allocation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    insert into trade_allocation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      ref_id,
      seg_type,
      sec_type,
      contract_id,
      symbol,
      market,
      `type`,
      source_account,
      target_account,
      portfolio_id,
      filled_quantity,
      avg_price,
      currency,
      trade_time,
      filled_time,
      business_date,
      settle_date,
      nav_date,
      `status`,
      `source`,
      created_at,
      updated_at,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{refId,jdbcType=BIGINT},
      #{segType,jdbcType=VARCHAR},
      #{secType,jdbcType=VARCHAR},
      #{contractId,jdbcType=BIGINT},
      #{symbol,jdbcType=VARCHAR},
      #{market,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR},
      #{sourceAccount,jdbcType=VARCHAR},
      #{targetAccount,jdbcType=VARCHAR},
      #{portfolioId,jdbcType=BIGINT},
      #{filledQuantity,jdbcType=DOUBLE},
      #{avgPrice,jdbcType=DOUBLE},
      #{currency,jdbcType=VARCHAR},
      #{tradeTime,jdbcType=TIMESTAMP},
      #{filledTime,jdbcType=TIMESTAMP},
      #{businessDate,jdbcType=DATE},
      #{settleDate,jdbcType=DATE},
      #{navDate,jdbcType=DATE},
      #{status,jdbcType=VARCHAR},
      #{source,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      ref_id = #{refId,jdbcType=BIGINT},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=BIGINT},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      source_account = #{sourceAccount,jdbcType=VARCHAR},
      target_account = #{targetAccount,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      currency = #{currency,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      business_date = #{businessDate,jdbcType=DATE},
      settle_date = #{settleDate,jdbcType=DATE},
      nav_date = #{navDate,jdbcType=DATE},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.TradeAllocation">
    insert into trade_allocation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="sourceAccount != null">
        source_account,
      </if>
      <if test="targetAccount != null">
        target_account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="settleDate != null">
        settle_date,
      </if>
      <if test="navDate != null">
        nav_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=BIGINT},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="sourceAccount != null">
        #{sourceAccount,jdbcType=VARCHAR},
      </if>
      <if test="targetAccount != null">
        #{targetAccount,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=BIGINT},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="sourceAccount != null">
        source_account = #{sourceAccount,jdbcType=VARCHAR},
      </if>
      <if test="targetAccount != null">
        target_account = #{targetAccount,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        settle_date = #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        nav_date = #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update trade_allocation
      <set>
        <trim suffixOverrides=",">
          <if test="item.refId != null">
            ref_id = #{item.refId,jdbcType=BIGINT},
          </if>
          <if test="item.segType != null">
            seg_type = #{item.segType,jdbcType=VARCHAR},
          </if>
          <if test="item.secType != null">
            sec_type = #{item.secType,jdbcType=VARCHAR},
          </if>
          <if test="item.contractId != null">
            contract_id = #{item.contractId,jdbcType=BIGINT},
          </if>
          <if test="item.symbol != null">
            symbol = #{item.symbol,jdbcType=VARCHAR},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.type != null">
            `type` = #{item.type,jdbcType=VARCHAR},
          </if>
          <if test="item.sourceAccount != null">
            source_account = #{item.sourceAccount,jdbcType=VARCHAR},
          </if>
          <if test="item.targetAccount != null">
            target_account = #{item.targetAccount,jdbcType=VARCHAR},
          </if>
          <if test="item.portfolioId != null">
            portfolio_id = #{item.portfolioId,jdbcType=BIGINT},
          </if>
          <if test="item.filledQuantity != null">
            filled_quantity = #{item.filledQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.avgPrice != null">
            avg_price = #{item.avgPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.tradeTime != null">
            trade_time = #{item.tradeTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.filledTime != null">
            filled_time = #{item.filledTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.businessDate != null">
            business_date = #{item.businessDate,jdbcType=DATE},
          </if>
          <if test="item.settleDate != null">
            settle_date = #{item.settleDate,jdbcType=DATE},
          </if>
          <if test="item.navDate != null">
            nav_date = #{item.navDate,jdbcType=DATE},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.source != null">
            `source` = #{item.source,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into trade_allocation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      ref_id,
      seg_type,
      sec_type,
      contract_id,
      symbol,
      market,
      `type`,
      source_account,
      target_account,
      portfolio_id,
      filled_quantity,
      avg_price,
      currency,
      trade_time,
      filled_time,
      business_date,
      settle_date,
      nav_date,
      `status`,
      `source`,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.refId,jdbcType=BIGINT},
        #{item.segType,jdbcType=VARCHAR},
        #{item.secType,jdbcType=VARCHAR},
        #{item.contractId,jdbcType=BIGINT},
        #{item.symbol,jdbcType=VARCHAR},
        #{item.market,jdbcType=VARCHAR},
        #{item.type,jdbcType=VARCHAR},
        #{item.sourceAccount,jdbcType=VARCHAR},
        #{item.targetAccount,jdbcType=VARCHAR},
        #{item.portfolioId,jdbcType=BIGINT},
        #{item.filledQuantity,jdbcType=DOUBLE},
        #{item.avgPrice,jdbcType=DOUBLE},
        #{item.currency,jdbcType=VARCHAR},
        #{item.tradeTime,jdbcType=TIMESTAMP},
        #{item.filledTime,jdbcType=TIMESTAMP},
        #{item.businessDate,jdbcType=DATE},
        #{item.settleDate,jdbcType=DATE},
        #{item.navDate,jdbcType=DATE},
        #{item.status,jdbcType=VARCHAR},
        #{item.source,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>