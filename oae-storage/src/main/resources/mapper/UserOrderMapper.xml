<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.UserOrderMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="external_id" jdbcType="VARCHAR" property="externalId" />
    <result column="trade_id" jdbcType="VARCHAR" property="tradeId" />
    <result column="aggregation_id" jdbcType="BIGINT" property="aggregationId" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="portfolio_id" jdbcType="BIGINT" property="portfolioId" />
    <result column="dividend_id" jdbcType="BIGINT" property="dividendId" />
    <result column="deal_account" jdbcType="VARCHAR" property="dealAccount" />
    <result column="execution_master" jdbcType="VARCHAR" property="executionMaster" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="seg_type" jdbcType="VARCHAR" property="segType" />
    <result column="sec_type" jdbcType="VARCHAR" property="secType" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="quantity" jdbcType="DOUBLE" property="quantity" />
    <result column="trade_amount" jdbcType="DOUBLE" property="tradeAmount" />
    <result column="filled_quantity" jdbcType="DOUBLE" property="filledQuantity" />
    <result column="filled_amount" jdbcType="DOUBLE" property="filledAmount" />
    <result column="avg_price" jdbcType="DOUBLE" property="avgPrice" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="filled_time" jdbcType="TIMESTAMP" property="filledTime" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="client" jdbcType="VARCHAR" property="client" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="apply_margin" jdbcType="BIT" property="applyMargin" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    <result column="trade_msg" jdbcType="LONGVARCHAR" property="tradeMsg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, external_id, trade_id, aggregation_id, account, portfolio_id, dividend_id, deal_account, 
    execution_master, symbol, seg_type, sec_type, market, currency, `action`, amount, 
    quantity, trade_amount, filled_quantity, filled_amount, avg_price, trade_time, filled_time, 
    order_type, client, `status`, `source`, apply_margin, error_code, error_msg, created_at, 
    updated_at
  </sql>
  <sql id="Blob_Column_List">
    trade_msg
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample">
    delete from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_order (external_id, trade_id, aggregation_id, 
      account, portfolio_id, dividend_id, 
      deal_account, execution_master, symbol, 
      seg_type, sec_type, market, 
      currency, `action`, amount, 
      quantity, trade_amount, filled_quantity, 
      filled_amount, avg_price, trade_time, 
      filled_time, order_type, client, 
      `status`, `source`, apply_margin, 
      error_code, error_msg, created_at, 
      updated_at, trade_msg)
    values (#{externalId,jdbcType=VARCHAR}, #{tradeId,jdbcType=VARCHAR}, #{aggregationId,jdbcType=BIGINT}, 
      #{account,jdbcType=VARCHAR}, #{portfolioId,jdbcType=BIGINT}, #{dividendId,jdbcType=BIGINT}, 
      #{dealAccount,jdbcType=VARCHAR}, #{executionMaster,jdbcType=VARCHAR}, #{symbol,jdbcType=VARCHAR}, 
      #{segType,jdbcType=VARCHAR}, #{secType,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR}, 
      #{currency,jdbcType=VARCHAR}, #{action,jdbcType=VARCHAR}, #{amount,jdbcType=DOUBLE}, 
      #{quantity,jdbcType=DOUBLE}, #{tradeAmount,jdbcType=DOUBLE}, #{filledQuantity,jdbcType=DOUBLE}, 
      #{filledAmount,jdbcType=DOUBLE}, #{avgPrice,jdbcType=DOUBLE}, #{tradeTime,jdbcType=TIMESTAMP}, 
      #{filledTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=VARCHAR}, #{client,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{applyMargin,jdbcType=BIT}, 
      #{errorCode,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{tradeMsg,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="externalId != null">
        external_id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="dividendId != null">
        dividend_id,
      </if>
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="client != null">
        client,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="applyMargin != null">
        apply_margin,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="tradeMsg != null">
        trade_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="externalId != null">
        #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="dividendId != null">
        #{dividendId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="client != null">
        #{client,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="applyMargin != null">
        #{applyMargin,jdbcType=BIT},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeMsg != null">
        #{tradeMsg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample" resultType="java.lang.Long">
    select count(*) from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.externalId != null">
        external_id = #{record.externalId,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeId != null">
        trade_id = #{record.tradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.aggregationId != null">
        aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioId != null">
        portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      </if>
      <if test="record.dividendId != null">
        dividend_id = #{record.dividendId,jdbcType=BIGINT},
      </if>
      <if test="record.dealAccount != null">
        deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.executionMaster != null">
        execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="record.symbol != null">
        symbol = #{record.symbol,jdbcType=VARCHAR},
      </if>
      <if test="record.segType != null">
        seg_type = #{record.segType,jdbcType=VARCHAR},
      </if>
      <if test="record.secType != null">
        sec_type = #{record.secType,jdbcType=VARCHAR},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null">
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DOUBLE},
      </if>
      <if test="record.tradeAmount != null">
        trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.filledQuantity != null">
        filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.filledAmount != null">
        filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.avgPrice != null">
        avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.tradeTime != null">
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.filledTime != null">
        filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.client != null">
        client = #{record.client,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.applyMargin != null">
        apply_margin = #{record.applyMargin,jdbcType=BIT},
      </if>
      <if test="record.errorCode != null">
        error_code = #{record.errorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeMsg != null">
        trade_msg = #{record.tradeMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update user_order
    set id = #{record.id,jdbcType=BIGINT},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      trade_id = #{record.tradeId,jdbcType=VARCHAR},
      aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      account = #{record.account,jdbcType=VARCHAR},
      portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      dividend_id = #{record.dividendId,jdbcType=BIGINT},
      deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      client = #{record.client,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      apply_margin = #{record.applyMargin,jdbcType=BIT},
      error_code = #{record.errorCode,jdbcType=VARCHAR},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      trade_msg = #{record.tradeMsg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_order
    set id = #{record.id,jdbcType=BIGINT},
      external_id = #{record.externalId,jdbcType=VARCHAR},
      trade_id = #{record.tradeId,jdbcType=VARCHAR},
      aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      account = #{record.account,jdbcType=VARCHAR},
      portfolio_id = #{record.portfolioId,jdbcType=BIGINT},
      dividend_id = #{record.dividendId,jdbcType=BIGINT},
      deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      quantity = #{record.quantity,jdbcType=DOUBLE},
      trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      client = #{record.client,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      apply_margin = #{record.applyMargin,jdbcType=BIT},
      error_code = #{record.errorCode,jdbcType=VARCHAR},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    update user_order
    <set>
      <if test="externalId != null">
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="dividendId != null">
        dividend_id = #{dividendId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="client != null">
        client = #{client,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="applyMargin != null">
        apply_margin = #{applyMargin,jdbcType=BIT},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeMsg != null">
        trade_msg = #{tradeMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    update user_order
    set external_id = #{externalId,jdbcType=VARCHAR},
      trade_id = #{tradeId,jdbcType=VARCHAR},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      dividend_id = #{dividendId,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=VARCHAR},
      client = #{client,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      apply_margin = #{applyMargin,jdbcType=BIT},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      trade_msg = #{tradeMsg,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    update user_order
    set external_id = #{externalId,jdbcType=VARCHAR},
      trade_id = #{tradeId,jdbcType=VARCHAR},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      dividend_id = #{dividendId,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=VARCHAR},
      client = #{client,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      apply_margin = #{applyMargin,jdbcType=BIT},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    insert into user_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      external_id,
      trade_id,
      aggregation_id,
      account,
      portfolio_id,
      dividend_id,
      deal_account,
      execution_master,
      symbol,
      seg_type,
      sec_type,
      market,
      currency,
      `action`,
      amount,
      quantity,
      trade_amount,
      filled_quantity,
      filled_amount,
      avg_price,
      trade_time,
      filled_time,
      order_type,
      client,
      `status`,
      `source`,
      apply_margin,
      error_code,
      error_msg,
      created_at,
      updated_at,
      trade_msg,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{externalId,jdbcType=VARCHAR},
      #{tradeId,jdbcType=VARCHAR},
      #{aggregationId,jdbcType=BIGINT},
      #{account,jdbcType=VARCHAR},
      #{portfolioId,jdbcType=BIGINT},
      #{dividendId,jdbcType=BIGINT},
      #{dealAccount,jdbcType=VARCHAR},
      #{executionMaster,jdbcType=VARCHAR},
      #{symbol,jdbcType=VARCHAR},
      #{segType,jdbcType=VARCHAR},
      #{secType,jdbcType=VARCHAR},
      #{market,jdbcType=VARCHAR},
      #{currency,jdbcType=VARCHAR},
      #{action,jdbcType=VARCHAR},
      #{amount,jdbcType=DOUBLE},
      #{quantity,jdbcType=DOUBLE},
      #{tradeAmount,jdbcType=DOUBLE},
      #{filledQuantity,jdbcType=DOUBLE},
      #{filledAmount,jdbcType=DOUBLE},
      #{avgPrice,jdbcType=DOUBLE},
      #{tradeTime,jdbcType=TIMESTAMP},
      #{filledTime,jdbcType=TIMESTAMP},
      #{orderType,jdbcType=VARCHAR},
      #{client,jdbcType=VARCHAR},
      #{status,jdbcType=VARCHAR},
      #{source,jdbcType=VARCHAR},
      #{applyMargin,jdbcType=BIT},
      #{errorCode,jdbcType=VARCHAR},
      #{errorMsg,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
      #{tradeMsg,jdbcType=LONGVARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      external_id = #{externalId,jdbcType=VARCHAR},
      trade_id = #{tradeId,jdbcType=VARCHAR},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      account = #{account,jdbcType=VARCHAR},
      portfolio_id = #{portfolioId,jdbcType=BIGINT},
      dividend_id = #{dividendId,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=DOUBLE},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=VARCHAR},
      client = #{client,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      apply_margin = #{applyMargin,jdbcType=BIT},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      trade_msg = #{tradeMsg,jdbcType=LONGVARCHAR},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.UserOrder">
    insert into user_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="externalId != null">
        external_id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="portfolioId != null">
        portfolio_id,
      </if>
      <if test="dividendId != null">
        dividend_id,
      </if>
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="client != null">
        client,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="applyMargin != null">
        apply_margin,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="tradeMsg != null">
        trade_msg,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="externalId != null">
        #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="dividendId != null">
        #{dividendId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="client != null">
        #{client,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="applyMargin != null">
        #{applyMargin,jdbcType=BIT},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeMsg != null">
        #{tradeMsg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="externalId != null">
        external_id = #{externalId,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="portfolioId != null">
        portfolio_id = #{portfolioId,jdbcType=BIGINT},
      </if>
      <if test="dividendId != null">
        dividend_id = #{dividendId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="client != null">
        client = #{client,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="applyMargin != null">
        apply_margin = #{applyMargin,jdbcType=BIT},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeMsg != null">
        trade_msg = #{tradeMsg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update user_order
      <set>
        <trim suffixOverrides=",">
          <if test="item.externalId != null">
            external_id = #{item.externalId,jdbcType=VARCHAR},
          </if>
          <if test="item.tradeId != null">
            trade_id = #{item.tradeId,jdbcType=VARCHAR},
          </if>
          <if test="item.aggregationId != null">
            aggregation_id = #{item.aggregationId,jdbcType=BIGINT},
          </if>
          <if test="item.account != null">
            account = #{item.account,jdbcType=VARCHAR},
          </if>
          <if test="item.portfolioId != null">
            portfolio_id = #{item.portfolioId,jdbcType=BIGINT},
          </if>
          <if test="item.dividendId != null">
            dividend_id = #{item.dividendId,jdbcType=BIGINT},
          </if>
          <if test="item.dealAccount != null">
            deal_account = #{item.dealAccount,jdbcType=VARCHAR},
          </if>
          <if test="item.executionMaster != null">
            execution_master = #{item.executionMaster,jdbcType=VARCHAR},
          </if>
          <if test="item.symbol != null">
            symbol = #{item.symbol,jdbcType=VARCHAR},
          </if>
          <if test="item.segType != null">
            seg_type = #{item.segType,jdbcType=VARCHAR},
          </if>
          <if test="item.secType != null">
            sec_type = #{item.secType,jdbcType=VARCHAR},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.action != null">
            `action` = #{item.action,jdbcType=VARCHAR},
          </if>
          <if test="item.amount != null">
            amount = #{item.amount,jdbcType=DOUBLE},
          </if>
          <if test="item.quantity != null">
            quantity = #{item.quantity,jdbcType=DOUBLE},
          </if>
          <if test="item.tradeAmount != null">
            trade_amount = #{item.tradeAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.filledQuantity != null">
            filled_quantity = #{item.filledQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.filledAmount != null">
            filled_amount = #{item.filledAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.avgPrice != null">
            avg_price = #{item.avgPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.tradeTime != null">
            trade_time = #{item.tradeTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.filledTime != null">
            filled_time = #{item.filledTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.orderType != null">
            order_type = #{item.orderType,jdbcType=VARCHAR},
          </if>
          <if test="item.client != null">
            client = #{item.client,jdbcType=VARCHAR},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.source != null">
            `source` = #{item.source,jdbcType=VARCHAR},
          </if>
          <if test="item.applyMargin != null">
            apply_margin = #{item.applyMargin,jdbcType=BIT},
          </if>
          <if test="item.errorCode != null">
            error_code = #{item.errorCode,jdbcType=VARCHAR},
          </if>
          <if test="item.errorMsg != null">
            error_msg = #{item.errorMsg,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.tradeMsg != null">
            trade_msg = #{item.tradeMsg,jdbcType=LONGVARCHAR},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into user_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      external_id,
      trade_id,
      aggregation_id,
      account,
      portfolio_id,
      dividend_id,
      deal_account,
      execution_master,
      symbol,
      seg_type,
      sec_type,
      market,
      currency,
      `action`,
      amount,
      quantity,
      trade_amount,
      filled_quantity,
      filled_amount,
      avg_price,
      trade_time,
      filled_time,
      order_type,
      client,
      `status`,
      `source`,
      apply_margin,
      error_code,
      error_msg,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.externalId,jdbcType=VARCHAR},
        #{item.tradeId,jdbcType=VARCHAR},
        #{item.aggregationId,jdbcType=BIGINT},
        #{item.account,jdbcType=VARCHAR},
        #{item.portfolioId,jdbcType=BIGINT},
        #{item.dividendId,jdbcType=BIGINT},
        #{item.dealAccount,jdbcType=VARCHAR},
        #{item.executionMaster,jdbcType=VARCHAR},
        #{item.symbol,jdbcType=VARCHAR},
        #{item.segType,jdbcType=VARCHAR},
        #{item.secType,jdbcType=VARCHAR},
        #{item.market,jdbcType=VARCHAR},
        #{item.currency,jdbcType=VARCHAR},
        #{item.action,jdbcType=VARCHAR},
        #{item.amount,jdbcType=DOUBLE},
        #{item.quantity,jdbcType=DOUBLE},
        #{item.tradeAmount,jdbcType=DOUBLE},
        #{item.filledQuantity,jdbcType=DOUBLE},
        #{item.filledAmount,jdbcType=DOUBLE},
        #{item.avgPrice,jdbcType=DOUBLE},
        #{item.tradeTime,jdbcType=TIMESTAMP},
        #{item.filledTime,jdbcType=TIMESTAMP},
        #{item.orderType,jdbcType=VARCHAR},
        #{item.client,jdbcType=VARCHAR},
        #{item.status,jdbcType=VARCHAR},
        #{item.source,jdbcType=VARCHAR},
        #{item.applyMargin,jdbcType=BIT},
        #{item.errorCode,jdbcType=VARCHAR},
        #{item.errorMsg,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>