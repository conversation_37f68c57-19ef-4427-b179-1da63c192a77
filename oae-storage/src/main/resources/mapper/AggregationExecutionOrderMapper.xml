<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tigerbrokers.oae.storage.jdbc.mapper.AggregationExecutionOrderMapper">
  <resultMap id="BaseResultMap" type="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_id" jdbcType="VARCHAR" property="tradeId" />
    <result column="aggregation_id" jdbcType="BIGINT" property="aggregationId" />
    <result column="deal_account" jdbcType="VARCHAR" property="dealAccount" />
    <result column="execution_master" jdbcType="VARCHAR" property="executionMaster" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="total_quantity" jdbcType="DOUBLE" property="totalQuantity" />
    <result column="trade_amount" jdbcType="DOUBLE" property="tradeAmount" />
    <result column="filled_quantity" jdbcType="DOUBLE" property="filledQuantity" />
    <result column="filled_amount" jdbcType="DOUBLE" property="filledAmount" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="avg_price" jdbcType="DOUBLE" property="avgPrice" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="seg_type" jdbcType="VARCHAR" property="segType" />
    <result column="sec_type" jdbcType="VARCHAR" property="secType" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="filled_time" jdbcType="TIMESTAMP" property="filledTime" />
    <result column="business_date" jdbcType="DATE" property="businessDate" />
    <result column="settle_date" jdbcType="DATE" property="settleDate" />
    <result column="nav_date" jdbcType="DATE" property="navDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="trade_status" jdbcType="VARCHAR" property="tradeStatus" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, trade_id, aggregation_id, deal_account, execution_master, symbol, market, currency, 
    `action`, total_quantity, trade_amount, filled_quantity, filled_amount, price, avg_price, 
    order_type, seg_type, sec_type, trade_time, filled_time, business_date, settle_date, 
    nav_date, `status`, `source`, trade_status, error_code, error_msg, `type`, created_at, 
    updated_at
  </sql>
  <select id="selectByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_execution_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="forUpdate != null and forUpdate">
      for update
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aggregation_execution_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aggregation_execution_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample">
    delete from aggregation_execution_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_execution_order (trade_id, aggregation_id, deal_account, 
      execution_master, symbol, market, 
      currency, `action`, total_quantity, 
      trade_amount, filled_quantity, filled_amount, 
      price, avg_price, order_type, 
      seg_type, sec_type, trade_time, 
      filled_time, business_date, settle_date, 
      nav_date, `status`, `source`, 
      trade_status, error_code, error_msg, 
      `type`, created_at, updated_at
      )
    values (#{tradeId,jdbcType=VARCHAR}, #{aggregationId,jdbcType=BIGINT}, #{dealAccount,jdbcType=VARCHAR}, 
      #{executionMaster,jdbcType=VARCHAR}, #{symbol,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR}, 
      #{currency,jdbcType=VARCHAR}, #{action,jdbcType=VARCHAR}, #{totalQuantity,jdbcType=DOUBLE}, 
      #{tradeAmount,jdbcType=DOUBLE}, #{filledQuantity,jdbcType=DOUBLE}, #{filledAmount,jdbcType=DOUBLE}, 
      #{price,jdbcType=DOUBLE}, #{avgPrice,jdbcType=DOUBLE}, #{orderType,jdbcType=VARCHAR}, 
      #{segType,jdbcType=VARCHAR}, #{secType,jdbcType=VARCHAR}, #{tradeTime,jdbcType=TIMESTAMP}, 
      #{filledTime,jdbcType=TIMESTAMP}, #{businessDate,jdbcType=DATE}, #{settleDate,jdbcType=DATE}, 
      #{navDate,jdbcType=DATE}, #{status,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{tradeStatus,jdbcType=VARCHAR}, #{errorCode,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aggregation_execution_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="totalQuantity != null">
        total_quantity,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="settleDate != null">
        settle_date,
      </if>
      <if test="navDate != null">
        nav_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="tradeStatus != null">
        trade_status,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tradeId != null">
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        #{totalQuantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="tradeStatus != null">
        #{tradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample" resultType="java.lang.Long">
    select count(*) from aggregation_execution_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aggregation_execution_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tradeId != null">
        trade_id = #{record.tradeId,jdbcType=VARCHAR},
      </if>
      <if test="record.aggregationId != null">
        aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      </if>
      <if test="record.dealAccount != null">
        deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.executionMaster != null">
        execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="record.symbol != null">
        symbol = #{record.symbol,jdbcType=VARCHAR},
      </if>
      <if test="record.market != null">
        market = #{record.market,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.action != null">
        `action` = #{record.action,jdbcType=VARCHAR},
      </if>
      <if test="record.totalQuantity != null">
        total_quantity = #{record.totalQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.tradeAmount != null">
        trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.filledQuantity != null">
        filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="record.filledAmount != null">
        filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.avgPrice != null">
        avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.segType != null">
        seg_type = #{record.segType,jdbcType=VARCHAR},
      </if>
      <if test="record.secType != null">
        sec_type = #{record.secType,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeTime != null">
        trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.filledTime != null">
        filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=DATE},
      </if>
      <if test="record.settleDate != null">
        settle_date = #{record.settleDate,jdbcType=DATE},
      </if>
      <if test="record.navDate != null">
        nav_date = #{record.navDate,jdbcType=DATE},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeStatus != null">
        trade_status = #{record.tradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.errorCode != null">
        error_code = #{record.errorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aggregation_execution_order
    set id = #{record.id,jdbcType=BIGINT},
      trade_id = #{record.tradeId,jdbcType=VARCHAR},
      aggregation_id = #{record.aggregationId,jdbcType=BIGINT},
      deal_account = #{record.dealAccount,jdbcType=VARCHAR},
      execution_master = #{record.executionMaster,jdbcType=VARCHAR},
      symbol = #{record.symbol,jdbcType=VARCHAR},
      market = #{record.market,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      `action` = #{record.action,jdbcType=VARCHAR},
      total_quantity = #{record.totalQuantity,jdbcType=DOUBLE},
      trade_amount = #{record.tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{record.filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{record.filledAmount,jdbcType=DOUBLE},
      price = #{record.price,jdbcType=DOUBLE},
      avg_price = #{record.avgPrice,jdbcType=DOUBLE},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      seg_type = #{record.segType,jdbcType=VARCHAR},
      sec_type = #{record.secType,jdbcType=VARCHAR},
      trade_time = #{record.tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{record.filledTime,jdbcType=TIMESTAMP},
      business_date = #{record.businessDate,jdbcType=DATE},
      settle_date = #{record.settleDate,jdbcType=DATE},
      nav_date = #{record.navDate,jdbcType=DATE},
      `status` = #{record.status,jdbcType=VARCHAR},
      `source` = #{record.source,jdbcType=VARCHAR},
      trade_status = #{record.tradeStatus,jdbcType=VARCHAR},
      error_code = #{record.errorCode,jdbcType=VARCHAR},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    update aggregation_execution_order
    <set>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        total_quantity = #{totalQuantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        settle_date = #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        nav_date = #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="tradeStatus != null">
        trade_status = #{tradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    update aggregation_execution_order
    set trade_id = #{tradeId,jdbcType=VARCHAR},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      total_quantity = #{totalQuantity,jdbcType=DOUBLE},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      price = #{price,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      order_type = #{orderType,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      business_date = #{businessDate,jdbcType=DATE},
      settle_date = #{settleDate,jdbcType=DATE},
      nav_date = #{navDate,jdbcType=DATE},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      trade_status = #{tradeStatus,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aggregation_execution_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="insertOrUpdate" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    insert into aggregation_execution_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      trade_id,
      aggregation_id,
      deal_account,
      execution_master,
      symbol,
      market,
      currency,
      `action`,
      total_quantity,
      trade_amount,
      filled_quantity,
      filled_amount,
      price,
      avg_price,
      order_type,
      seg_type,
      sec_type,
      trade_time,
      filled_time,
      business_date,
      settle_date,
      nav_date,
      `status`,
      `source`,
      trade_status,
      error_code,
      error_msg,
      `type`,
      created_at,
      updated_at,
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{tradeId,jdbcType=VARCHAR},
      #{aggregationId,jdbcType=BIGINT},
      #{dealAccount,jdbcType=VARCHAR},
      #{executionMaster,jdbcType=VARCHAR},
      #{symbol,jdbcType=VARCHAR},
      #{market,jdbcType=VARCHAR},
      #{currency,jdbcType=VARCHAR},
      #{action,jdbcType=VARCHAR},
      #{totalQuantity,jdbcType=DOUBLE},
      #{tradeAmount,jdbcType=DOUBLE},
      #{filledQuantity,jdbcType=DOUBLE},
      #{filledAmount,jdbcType=DOUBLE},
      #{price,jdbcType=DOUBLE},
      #{avgPrice,jdbcType=DOUBLE},
      #{orderType,jdbcType=VARCHAR},
      #{segType,jdbcType=VARCHAR},
      #{secType,jdbcType=VARCHAR},
      #{tradeTime,jdbcType=TIMESTAMP},
      #{filledTime,jdbcType=TIMESTAMP},
      #{businessDate,jdbcType=DATE},
      #{settleDate,jdbcType=DATE},
      #{navDate,jdbcType=DATE},
      #{status,jdbcType=VARCHAR},
      #{source,jdbcType=VARCHAR},
      #{tradeStatus,jdbcType=VARCHAR},
      #{errorCode,jdbcType=VARCHAR},
      #{errorMsg,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP},
      #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      id = #{id,jdbcType=BIGINT},
      trade_id = #{tradeId,jdbcType=VARCHAR},
      aggregation_id = #{aggregationId,jdbcType=BIGINT},
      deal_account = #{dealAccount,jdbcType=VARCHAR},
      execution_master = #{executionMaster,jdbcType=VARCHAR},
      symbol = #{symbol,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      `action` = #{action,jdbcType=VARCHAR},
      total_quantity = #{totalQuantity,jdbcType=DOUBLE},
      trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      filled_amount = #{filledAmount,jdbcType=DOUBLE},
      price = #{price,jdbcType=DOUBLE},
      avg_price = #{avgPrice,jdbcType=DOUBLE},
      order_type = #{orderType,jdbcType=VARCHAR},
      seg_type = #{segType,jdbcType=VARCHAR},
      sec_type = #{secType,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      filled_time = #{filledTime,jdbcType=TIMESTAMP},
      business_date = #{businessDate,jdbcType=DATE},
      settle_date = #{settleDate,jdbcType=DATE},
      nav_date = #{navDate,jdbcType=DATE},
      `status` = #{status,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      trade_status = #{tradeStatus,jdbcType=VARCHAR},
      error_code = #{errorCode,jdbcType=VARCHAR},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
    </trim>
  </update>
  <update id="insertOrUpdateSelective" parameterType="com.tigerbrokers.oae.storage.jdbc.data.AggregationExecutionOrder">
    insert into aggregation_execution_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="aggregationId != null">
        aggregation_id,
      </if>
      <if test="dealAccount != null">
        deal_account,
      </if>
      <if test="executionMaster != null">
        execution_master,
      </if>
      <if test="symbol != null">
        symbol,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="action != null">
        `action`,
      </if>
      <if test="totalQuantity != null">
        total_quantity,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="filledQuantity != null">
        filled_quantity,
      </if>
      <if test="filledAmount != null">
        filled_amount,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="avgPrice != null">
        avg_price,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="segType != null">
        seg_type,
      </if>
      <if test="secType != null">
        sec_type,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="filledTime != null">
        filled_time,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="settleDate != null">
        settle_date,
      </if>
      <if test="navDate != null">
        nav_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="tradeStatus != null">
        trade_status,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    values 
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        #{totalQuantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        #{secType,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="tradeStatus != null">
        #{tradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="aggregationId != null">
        aggregation_id = #{aggregationId,jdbcType=BIGINT},
      </if>
      <if test="dealAccount != null">
        deal_account = #{dealAccount,jdbcType=VARCHAR},
      </if>
      <if test="executionMaster != null">
        execution_master = #{executionMaster,jdbcType=VARCHAR},
      </if>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        `action` = #{action,jdbcType=VARCHAR},
      </if>
      <if test="totalQuantity != null">
        total_quantity = #{totalQuantity,jdbcType=DOUBLE},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="filledQuantity != null">
        filled_quantity = #{filledQuantity,jdbcType=DOUBLE},
      </if>
      <if test="filledAmount != null">
        filled_amount = #{filledAmount,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="avgPrice != null">
        avg_price = #{avgPrice,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="segType != null">
        seg_type = #{segType,jdbcType=VARCHAR},
      </if>
      <if test="secType != null">
        sec_type = #{secType,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filledTime != null">
        filled_time = #{filledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=DATE},
      </if>
      <if test="settleDate != null">
        settle_date = #{settleDate,jdbcType=DATE},
      </if>
      <if test="navDate != null">
        nav_date = #{navDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="tradeStatus != null">
        trade_status = #{tradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </update>
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" separator=";">
       update aggregation_execution_order
      <set>
        <trim suffixOverrides=",">
          <if test="item.tradeId != null">
            trade_id = #{item.tradeId,jdbcType=VARCHAR},
          </if>
          <if test="item.aggregationId != null">
            aggregation_id = #{item.aggregationId,jdbcType=BIGINT},
          </if>
          <if test="item.dealAccount != null">
            deal_account = #{item.dealAccount,jdbcType=VARCHAR},
          </if>
          <if test="item.executionMaster != null">
            execution_master = #{item.executionMaster,jdbcType=VARCHAR},
          </if>
          <if test="item.symbol != null">
            symbol = #{item.symbol,jdbcType=VARCHAR},
          </if>
          <if test="item.market != null">
            market = #{item.market,jdbcType=VARCHAR},
          </if>
          <if test="item.currency != null">
            currency = #{item.currency,jdbcType=VARCHAR},
          </if>
          <if test="item.action != null">
            `action` = #{item.action,jdbcType=VARCHAR},
          </if>
          <if test="item.totalQuantity != null">
            total_quantity = #{item.totalQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.tradeAmount != null">
            trade_amount = #{item.tradeAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.filledQuantity != null">
            filled_quantity = #{item.filledQuantity,jdbcType=DOUBLE},
          </if>
          <if test="item.filledAmount != null">
            filled_amount = #{item.filledAmount,jdbcType=DOUBLE},
          </if>
          <if test="item.price != null">
            price = #{item.price,jdbcType=DOUBLE},
          </if>
          <if test="item.avgPrice != null">
            avg_price = #{item.avgPrice,jdbcType=DOUBLE},
          </if>
          <if test="item.orderType != null">
            order_type = #{item.orderType,jdbcType=VARCHAR},
          </if>
          <if test="item.segType != null">
            seg_type = #{item.segType,jdbcType=VARCHAR},
          </if>
          <if test="item.secType != null">
            sec_type = #{item.secType,jdbcType=VARCHAR},
          </if>
          <if test="item.tradeTime != null">
            trade_time = #{item.tradeTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.filledTime != null">
            filled_time = #{item.filledTime,jdbcType=TIMESTAMP},
          </if>
          <if test="item.businessDate != null">
            business_date = #{item.businessDate,jdbcType=DATE},
          </if>
          <if test="item.settleDate != null">
            settle_date = #{item.settleDate,jdbcType=DATE},
          </if>
          <if test="item.navDate != null">
            nav_date = #{item.navDate,jdbcType=DATE},
          </if>
          <if test="item.status != null">
            `status` = #{item.status,jdbcType=VARCHAR},
          </if>
          <if test="item.source != null">
            `source` = #{item.source,jdbcType=VARCHAR},
          </if>
          <if test="item.tradeStatus != null">
            trade_status = #{item.tradeStatus,jdbcType=VARCHAR},
          </if>
          <if test="item.errorCode != null">
            error_code = #{item.errorCode,jdbcType=VARCHAR},
          </if>
          <if test="item.errorMsg != null">
            error_msg = #{item.errorMsg,jdbcType=VARCHAR},
          </if>
          <if test="item.type != null">
            `type` = #{item.type,jdbcType=VARCHAR},
          </if>
          <if test="item.createdAt != null">
            created_at = #{item.createdAt,jdbcType=TIMESTAMP},
          </if>
          <if test="item.updatedAt != null">
            updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
          </if>
        </trim>
      </set>
      <where>
        <trim suffixOverrides=",">
          id = #{item.id,jdbcType=BIGINT},
        </trim>
      </where>
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into aggregation_execution_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      trade_id,
      aggregation_id,
      deal_account,
      execution_master,
      symbol,
      market,
      currency,
      `action`,
      total_quantity,
      trade_amount,
      filled_quantity,
      filled_amount,
      price,
      avg_price,
      order_type,
      seg_type,
      sec_type,
      trade_time,
      filled_time,
      business_date,
      settle_date,
      nav_date,
      `status`,
      `source`,
      trade_status,
      error_code,
      error_msg,
      `type`,
      created_at,
      updated_at,
    </trim>
     values 
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.tradeId,jdbcType=VARCHAR},
        #{item.aggregationId,jdbcType=BIGINT},
        #{item.dealAccount,jdbcType=VARCHAR},
        #{item.executionMaster,jdbcType=VARCHAR},
        #{item.symbol,jdbcType=VARCHAR},
        #{item.market,jdbcType=VARCHAR},
        #{item.currency,jdbcType=VARCHAR},
        #{item.action,jdbcType=VARCHAR},
        #{item.totalQuantity,jdbcType=DOUBLE},
        #{item.tradeAmount,jdbcType=DOUBLE},
        #{item.filledQuantity,jdbcType=DOUBLE},
        #{item.filledAmount,jdbcType=DOUBLE},
        #{item.price,jdbcType=DOUBLE},
        #{item.avgPrice,jdbcType=DOUBLE},
        #{item.orderType,jdbcType=VARCHAR},
        #{item.segType,jdbcType=VARCHAR},
        #{item.secType,jdbcType=VARCHAR},
        #{item.tradeTime,jdbcType=TIMESTAMP},
        #{item.filledTime,jdbcType=TIMESTAMP},
        #{item.businessDate,jdbcType=DATE},
        #{item.settleDate,jdbcType=DATE},
        #{item.navDate,jdbcType=DATE},
        #{item.status,jdbcType=VARCHAR},
        #{item.source,jdbcType=VARCHAR},
        #{item.tradeStatus,jdbcType=VARCHAR},
        #{item.errorCode,jdbcType=VARCHAR},
        #{item.errorMsg,jdbcType=VARCHAR},
        #{item.type,jdbcType=VARCHAR},
        #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedAt,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
</mapper>