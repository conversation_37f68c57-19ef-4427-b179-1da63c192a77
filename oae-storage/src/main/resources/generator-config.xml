<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <classPathEntry location="../migrations/drivers/mysql-connector-java-5.1.38.jar"/>

    <context id="MysqlDB" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"></property>
        <property name="endingDelimiter" value="`"></property>
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <plugin type="com.softwareloop.mybatis.generator.plugins.LombokPlugin">
            <property name="builder" value="true"></property>
            <property name="allArgsConstructor" value="true"></property>
            <property name="noArgsConstructor" value="true"></property>
        </plugin>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <plugin type="com.tigerbrokers.tools.storage.mysql.mybatis.plugin.SelectForUpdatePlugin"></plugin>
        <plugin type="com.tigerbrokers.tools.storage.mysql.mybatis.plugin.MySqlUpsertPlugin"></plugin>
        <plugin type="com.tigerbrokers.tools.storage.mysql.mybatis.plugin.BatchUpdatePlugin"></plugin>
        <plugin type="com.tigerbrokers.tools.storage.mysql.mybatis.plugin.BatchInsertPlugin"></plugin>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true" />
        </commentGenerator>
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************************************"
                        userId="dba"
                        password="a0d196fdasfdbf12968f065cdgzs103">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.tigerbrokers.oae.storage.jdbc.data" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
            <property name="constructorBased" value="false"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.tigerbrokers.oae.storage.jdbc.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

<!--        <table tableName="user_order" domainObjectName="UserOrder">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
<!--        </table>-->
        <table tableName="trade_allocation" domainObjectName="TradeAllocation">
            <generatedKey column="id" sqlStatement="Mysql" identity="true" />
        </table>
        <!--<table tableName="aggregation_order" domainObjectName="AggregationOrder">-->
        <!--    <generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
        <!--</table>-->
        <table tableName="aggregation_execution_order" domainObjectName="AggregationExecutionOrder">
            <generatedKey column="id" sqlStatement="Mysql" identity="true" />
        </table>
        <!--<table tableName="aggregation_execution_order_record" domainObjectName="AggregationExecutionOrderRecord">-->
        <!--    <generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
        <!--</table>-->
        <!--<table tableName="aggregation_execution_order_history" domainObjectName="AggregationExecutionOrderHistory" />-->
        <!--<table tableName="trade_allocation" domainObjectName="TradeAllocation" >-->
        <!--    <generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
        <!--</table>-->
        <table tableName="locked_asset" domainObjectName="LockedAsset" >
            <generatedKey column="id" sqlStatement="Mysql" identity="true" />
        </table>
    </context>
</generatorConfiguration>

