<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oae-platform</artifactId>
        <groupId>com.tigerbrokers.oae.platform</groupId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oae-storage</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tigerbrokers.oae.platform</groupId>
            <artifactId>oae-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>http-client</artifactId>
                    <groupId>com.rabbitmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.commons</groupId>
            <artifactId>storage</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tigerbrokers.brokerage.sdk</groupId>
            <artifactId>brokerage-common</artifactId>
            <version>2.0.7</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis.generator.version}</version>
                <configuration>
                    <configurationFile>src/main/resources/generator-config.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.softwareloop</groupId>
                        <artifactId>mybatis-generator-lombok-plugin</artifactId>
                        <version>${mybatis.generator.lombok.plugin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.tigerbrokers.commons</groupId>
                        <artifactId>storage</artifactId>
                        <version>${commons.tools.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven.resources.plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven.deploy.plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>